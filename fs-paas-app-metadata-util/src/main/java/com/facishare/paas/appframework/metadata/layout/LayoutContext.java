package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import lombok.Data;

/**
 * Created by zhouwr on 2020/6/12
 */
@Data
public class LayoutContext {

    private static final ThreadLocal<LayoutContext> THREAD_LOCAL = ThreadLocal.withInitial(() -> new LayoutContext());

    static {
        // 请求完成时,清空 LayoutContext
        RequestContextManager.addContextRemoveListener(context -> LayoutContext.remove());
    }

    private LayoutVersion layoutVersion;

    private LayoutAgentType layoutAgentType;

    private boolean isEnableMobileLayout;

    private Boolean isReplaceFormTable = null;

    private boolean noReplaceLayoutNameI18n;
    private boolean removeI18n;


    public static LayoutContext get() {
        return THREAD_LOCAL.get();
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }

    public static boolean isVersionV3() {
        return LayoutVersion.V3 == get().getLayoutVersion();
    }

    public static boolean isMobileLayout() {
        return LayoutAgentType.MOBILE == get().getLayoutAgentType();
    }

    public static boolean isSidebarLayout() {
        return LayoutAgentType.SIDEBAR == get().getLayoutAgentType();
    }


    public static boolean isEnableMobileLayout() {
        return get().isEnableMobileLayout;
    }

    public static Boolean isReplaceFormTable() {
        return get().isReplaceFormTable;
    }

    public static boolean noReplaceLayoutNameI18n() {
        return get().noReplaceLayoutNameI18n;
    }

    public static boolean needRemoveI18n() {
        return get().removeI18n;
    }
}
