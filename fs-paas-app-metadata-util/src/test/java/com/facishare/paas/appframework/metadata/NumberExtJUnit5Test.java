package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.Number;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * NumberExt的JUnit 5测试类
 * 测试数字处理扩展功能
 */
class NumberExtJUnit5Test {

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("of - 工厂方法创建")
    void testOf() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(10);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        assertNotNull(numberExt);
        assertEquals(10, numberExt.getLength());
        assertEquals(2, numberExt.getDecimalPlaces());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkNumberDigit - 空值情况
     */
    @Test
    @DisplayName("checkNumberDigit - 空值情况")
    void testCheckNumberDigit_EmptyValue() {
        Number number = mock(Number.class);
        NumberExt numberExt = NumberExt.of(number);

        // 测试null值
        assertTrue(numberExt.checkNumberDigit(null));

        // 测试空字符串
        assertTrue(numberExt.checkNumberDigit(""));

        // 测试空白字符串 - 注意：Strings.isNullOrEmpty()不会将空白字符串视为空
        // 空白字符串会被当作无效数字处理，返回false
        assertFalse(numberExt.checkNumberDigit("   "));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkNumberDigit - 整数情况
     */
    @Test
    @DisplayName("checkNumberDigit - 整数情况")
    void testCheckNumberDigit_IntegerValue() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(5);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试正整数 - 符合长度要求
        assertTrue(numberExt.checkNumberDigit("123"));
        assertTrue(numberExt.checkNumberDigit("12345"));
        
        // 测试负整数 - 符合长度要求
        assertTrue(numberExt.checkNumberDigit("-123"));
        assertTrue(numberExt.checkNumberDigit("+123"));
        
        // 测试超长整数 - 不符合长度要求
        assertFalse(numberExt.checkNumberDigit("123456"));
        assertFalse(numberExt.checkNumberDigit("-123456"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkNumberDigit - 小数情况
     */
    @Test
    @DisplayName("checkNumberDigit - 小数情况")
    void testCheckNumberDigit_DecimalValue() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(5);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试正小数 - 符合精度要求
        assertTrue(numberExt.checkNumberDigit("123.45"));
        assertTrue(numberExt.checkNumberDigit("12345.1"));
        assertTrue(numberExt.checkNumberDigit("1.12"));
        
        // 测试负小数 - 符合精度要求
        assertTrue(numberExt.checkNumberDigit("-123.45"));
        assertTrue(numberExt.checkNumberDigit("+123.45"));
        
        // 测试小数位数超限
        assertFalse(numberExt.checkNumberDigit("123.456"));
        assertFalse(numberExt.checkNumberDigit("1.123"));
        
        // 测试整数部分超长
        assertFalse(numberExt.checkNumberDigit("123456.12"));
        
        // 测试整数部分和小数部分都超限
        assertFalse(numberExt.checkNumberDigit("123456.123"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkNumberDigit - 边界情况
     */
    @Test
    @DisplayName("checkNumberDigit - 边界情况")
    void testCheckNumberDigit_BoundaryValues() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(3);
        when(number.getDecimalPlaces()).thenReturn(1);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试边界值 - 刚好符合要求
        assertTrue(numberExt.checkNumberDigit("123"));
        assertTrue(numberExt.checkNumberDigit("12.3"));
        assertTrue(numberExt.checkNumberDigit("-12"));
        assertTrue(numberExt.checkNumberDigit("+99"));
        
        // 测试边界值 - 刚好超出要求
        assertFalse(numberExt.checkNumberDigit("1234"));
        assertFalse(numberExt.checkNumberDigit("12.34"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkNumberDigit - 特殊数字格式
     */
    @Test
    @DisplayName("checkNumberDigit - 特殊数字格式")
    void testCheckNumberDigit_SpecialFormats() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(10);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试科学计数法
        assertTrue(numberExt.checkNumberDigit("1E2")); // 100
        assertTrue(numberExt.checkNumberDigit("1.5E1")); // 15
        
        // 测试零值
        assertTrue(numberExt.checkNumberDigit("0"));
        assertTrue(numberExt.checkNumberDigit("0.0"));
        assertTrue(numberExt.checkNumberDigit("0.00"));
        
        // 测试前导零
        assertTrue(numberExt.checkNumberDigit("001"));
        assertTrue(numberExt.checkNumberDigit("001.10"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkNumberDigit - 无效格式
     */
    @Test
    @DisplayName("checkNumberDigit - 无效格式")
    void testCheckNumberDigit_InvalidFormats() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(10);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试非数字字符串
        assertFalse(numberExt.checkNumberDigit("abc"));
        assertFalse(numberExt.checkNumberDigit("12a"));
        assertFalse(numberExt.checkNumberDigit("a12"));
        
        // 测试特殊字符
        assertFalse(numberExt.checkNumberDigit("12.34.56"));
        assertFalse(numberExt.checkNumberDigit("12..34"));
        assertFalse(numberExt.checkNumberDigit("++12"));
        assertFalse(numberExt.checkNumberDigit("--12"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValue - 空值情况
     */
    @Test
    @DisplayName("convertValue - 空值情况")
    void testConvertValue_EmptyValue() {
        Number number = mock(Number.class);
        when(number.getDecimalPlaces()).thenReturn(2);

        NumberExt numberExt = NumberExt.of(number);

        // 测试null值
        assertEquals("", numberExt.convertValue(null));

        // 测试空字符串
        assertEquals("", numberExt.convertValue(""));

        // 注意：空白字符串不会被convertValue方法特殊处理，会抛出NumberFormatException
        // 这里我们不测试空白字符串，因为它会导致异常
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValue - 整数转换
     */
    @Test
    @DisplayName("convertValue - 整数转换")
    void testConvertValue_IntegerConversion() {
        Number number = mock(Number.class);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试正整数
        assertEquals("123.00", numberExt.convertValue("123"));
        assertEquals("1.00", numberExt.convertValue("1"));
        
        // 测试负整数
        assertEquals("-123.00", numberExt.convertValue("-123"));
        
        // 测试零
        assertEquals("0.00", numberExt.convertValue("0"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValue - 小数转换
     */
    @Test
    @DisplayName("convertValue - 小数转换")
    void testConvertValue_DecimalConversion() {
        Number number = mock(Number.class);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试小数位数少于要求的情况
        assertEquals("123.10", numberExt.convertValue("123.1"));
        assertEquals("123.01", numberExt.convertValue("123.01"));
        
        // 测试小数位数等于要求的情况
        assertEquals("123.45", numberExt.convertValue("123.45"));
        
        // 测试小数位数多于要求的情况（四舍五入）
        assertEquals("123.46", numberExt.convertValue("123.456"));
        assertEquals("123.45", numberExt.convertValue("123.454"));
        assertEquals("123.46", numberExt.convertValue("123.455"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValue - 不同精度设置
     */
    @Test
    @DisplayName("convertValue - 不同精度设置")
    void testConvertValue_DifferentPrecision() {
        // 测试0位小数
        Number number0 = mock(Number.class);
        when(number0.getDecimalPlaces()).thenReturn(0);
        NumberExt numberExt0 = NumberExt.of(number0);
        
        assertEquals("123", numberExt0.convertValue("123.456"));
        assertEquals("124", numberExt0.convertValue("123.5"));
        
        // 测试1位小数
        Number number1 = mock(Number.class);
        when(number1.getDecimalPlaces()).thenReturn(1);
        NumberExt numberExt1 = NumberExt.of(number1);
        
        assertEquals("123.5", numberExt1.convertValue("123.456"));
        assertEquals("123.4", numberExt1.convertValue("123.44"));
        
        // 测试3位小数
        Number number3 = mock(Number.class);
        when(number3.getDecimalPlaces()).thenReturn(3);
        NumberExt numberExt3 = NumberExt.of(number3);
        
        assertEquals("123.456", numberExt3.convertValue("123.456"));
        assertEquals("123.400", numberExt3.convertValue("123.4"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValue - 四舍五入规则
     */
    @Test
    @DisplayName("convertValue - 四舍五入规则")
    void testConvertValue_RoundingRules() {
        Number number = mock(Number.class);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试四舍五入 - 舍
        assertEquals("123.12", numberExt.convertValue("123.124"));
        assertEquals("123.12", numberExt.convertValue("123.121"));
        
        // 测试四舍五入 - 入
        assertEquals("123.13", numberExt.convertValue("123.126"));
        assertEquals("123.13", numberExt.convertValue("123.129"));
        
        // 测试边界情况 - 5的处理（HALF_UP模式）
        assertEquals("123.13", numberExt.convertValue("123.125"));
        assertEquals("123.12", numberExt.convertValue("123.115"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValue - 科学计数法
     */
    @Test
    @DisplayName("convertValue - 科学计数法")
    void testConvertValue_ScientificNotation() {
        Number number = mock(Number.class);
        when(number.getDecimalPlaces()).thenReturn(2);
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试科学计数法转换
        assertEquals("100.00", numberExt.convertValue("1E2"));
        assertEquals("15.00", numberExt.convertValue("1.5E1"));
        assertEquals("0.01", numberExt.convertValue("1E-2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试PATTERN常量
     */
    @Test
    @DisplayName("PATTERN - 正则表达式常量")
    void testPattern() {
        // 测试正则表达式匹配
        assertTrue(NumberExt.PATTERN.matcher("+").find());
        assertTrue(NumberExt.PATTERN.matcher("-").find());
        assertTrue(NumberExt.PATTERN.matcher("+-").find());
        
        // 测试替换功能
        assertEquals("123", NumberExt.PATTERN.matcher("+123").replaceAll(""));
        assertEquals("123", NumberExt.PATTERN.matcher("-123").replaceAll(""));
        assertEquals("123", NumberExt.PATTERN.matcher("+-123").replaceAll(""));
        
        // 测试不匹配的情况
        assertFalse(NumberExt.PATTERN.matcher("123").find());
        assertFalse(NumberExt.PATTERN.matcher("abc").find());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能测试")
    void testDelegateFeatures() {
        Number number = mock(Number.class);
        when(number.getLength()).thenReturn(10);
        when(number.getDecimalPlaces()).thenReturn(2);
        when(number.getApiName()).thenReturn("test_number");
        when(number.getLabel()).thenReturn("Test Number");
        
        NumberExt numberExt = NumberExt.of(number);
        
        // 测试委托的方法
        assertEquals(10, numberExt.getLength());
        assertEquals(2, numberExt.getDecimalPlaces());
        assertEquals("test_number", numberExt.getApiName());
        assertEquals("Test Number", numberExt.getLabel());
        
        // 验证委托调用
        verify(number, times(1)).getLength();
        verify(number, times(1)).getDecimalPlaces();
        verify(number, times(1)).getApiName();
        verify(number, times(1)).getLabel();
    }
}
