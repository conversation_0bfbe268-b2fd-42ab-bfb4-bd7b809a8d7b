package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.treeview.TreeViewService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Created by zhaooju on 2022/5/30
 */

class MetaDataActionServiceImplTest extends Specification {

    @InjectMocks
    MetaDataActionServiceImpl metaDataActionService
    @Mock
    MetaDataMiscService metaDataMiscService
    @Mock
    DescribeLogicService describeLogicService
    @Mock
    ObjectDataProxy dataProxy
    @Mock
    OrgService orgService
    @Mock
    AutoNumberLogicService autoNumberLogicService
    @Mock
    ObjectDataServiceImpl objectDataService
    @Mock
    UniqueRuleLogicService uniqueRuleLogicService
    @Mock
    DuplicatedSearchService duplicatedSearchService
    @Mock
    TreeViewService treeViewService;
    @Mock
    MultiCurrencyLogicService multiCurrencyLogicService
    @Mock
    MetaDataFindService metaDataFindService
    @Mock
    LogService logService

    User user
    IObjectDescribe describe

    def setup() {
        MockitoAnnotations.initMocks(this)

        user = new User("12321", "1000")
        describe = new ObjectDescribe()
        def describeJson = '''{"tenant_id":"12321","package":"CRM","is_active":true,"last_modified_time":1667894677177,"create_time":*************,"description":"","last_modified_by":"1000","display_name":"模板对象主","created_by":"1000","version":3,"is_open_display_name":false,"index_version":1,"icon_index":0,"is_deleted":false,"api_name":"master__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"pnv","_id":"636a0cbef18bd900014a45db","fields":{"lock_rule":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":1667894462195,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定规则","is_unique":false,"rules":[],"default_value":"default_lock_rule","label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"636a0cbef18bd900014a45d2","is_single":false,"label_r":"锁定规则","is_index_field":false,"index_name":"s_1","status":"new"},"html_rich_text__c":{"describe_api_name":"master__c","default_is_expression":false,"is_index":false,"is_active":true,"create_time":1667894462337,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"default_value":"","label":"富文本","type":"html_rich_text","field_num":3,"default_to_zero":false,"is_required":false,"api_name":"html_rich_text__c","define_type":"custom","_id":"636a0cbef18bd900014a45be","is_single":false,"is_index_field":false,"index_name":"s_2","max_length":131072,"help_text":"","status":"new","description":""},"image__c":{"describe_api_name":"master__c","auto_adapt_places":false,"is_unique":false,"file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"index_name":"a_3","support_file_types":["jpg","gif","jpeg","png"],"is_index":true,"file_amount_limit":1,"is_active":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"create_time":1667894462348,"is_encrypted":false,"label":"图片","is_watermark":false,"field_num":6,"file_size_limit":20971520,"is_ocr_recognition":false,"api_name":"image__c","is_need_cdn":false,"_id":"636a0cbef18bd900014a45c9","is_index_field":false,"identify_type":"","help_text":"单个图片不得超过20M","status":"new","description":""},"mc_exchange_rate":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"label_r":"汇率","index_name":"d_2","max_length":16,"is_index":true,"is_active":true,"create_time":1667894462358,"is_encrypted":false,"length":10,"label":"汇率","field_num":10,"api_name":"mc_exchange_rate","_id":"636a0cbef18bd900014a45d7","is_index_field":false,"round_mode":4,"help_text":"","status":"new"},"date__c":{"describe_api_name":"master__c","default_is_expression":false,"is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"","label":"日期","type":"date","time_zone":"GMT+8","field_num":11,"default_to_zero":false,"is_required":false,"api_name":"date__c","define_type":"custom","date_format":"yyyy-MM-dd","_id":"636a0cbef18bd900014a45c3","is_single":false,"is_index_field":false,"index_name":"l_1","help_text":"","status":"new","description":""},"life_status_before_invalid":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":1667894462223,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","field_num":12,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"636a0cbef18bd900014a45d5","is_single":false,"label_r":"作废前生命状态","is_index_field":false,"index_name":"t_4","max_length":256,"status":"new"},"date_time__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"type":"date_time","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"l_2","is_index":true,"is_active":true,"create_time":1667894462344,"is_encrypted":false,"not_use_multitime_zone":false,"default_value":"","label":"日期时间","time_zone":"GMT+8","field_num":13,"api_name":"date_time__c","date_format":"yyyy-MM-dd HH:mm","_id":"636a0cbef18bd900014a45c5","is_index_field":false,"help_text":"","status":"new","description":""},"phone_number__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"^[0-9+-;,]{0,100}$","remove_mask_roles":{},"is_unique":false,"type":"phone_number","is_required":false,"define_type":"custom","is_single":false,"index_name":"p_1","verification":false,"is_index":true,"is_active":true,"create_time":1667894462345,"is_encrypted":false,"default_value":"","label":"手机","field_num":14,"api_name":"phone_number__c","_id":"636a0cbef18bd900014a45c6","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new","description":""},"owner_department":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":true,"label_r":"负责人主属部门","index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1667894462327,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"636a0cbef18bd900014a45b4","is_index_field":false,"help_text":"","status":"new","description":""},"file_attachment__c":{"describe_api_name":"master__c","is_index":true,"file_amount_limit":1,"is_active":true,"create_time":1667894462349,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"file_source":["local","net"],"label":"附件","type":"file_attachment","field_num":16,"file_size_limit":104857600,"is_required":false,"api_name":"file_attachment__c","define_type":"custom","_id":"636a0cbef18bd900014a45ca","is_single":false,"is_index_field":false,"index_name":"a_5","support_file_types":[],"help_text":"单个文件不得超过100M","status":"new","description":""},"lock_status":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462222,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","field_num":17,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"636a0cbef18bd900014a45d3","is_single":false,"label_r":"锁定状态","is_index_field":false,"index_name":"s_4","config":{},"status":"new"},"package":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"package","label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","max_length":200,"status":"released"},"create_time":{"describe_api_name":"master__c","is_index":true,"create_time":*************,"is_unique":false,"description":"create_time","label":"创建时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"currency__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"length":12,"default_value":"","label":"金额","currency_unit":"￥","field_num":19,"api_name":"currency__c","_id":"636a0cbef18bd900014a45c2","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new","description":""},"account__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[],"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_6","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"label":"关联客户","target_api_name":"AccountObj","target_related_list_name":"target_related_list_6434f__c","field_num":21,"target_related_list_label":"模板对象主","action_on_target_delete":"set_null","related_wheres":[],"api_name":"account__c","_id":"636a0cbef18bd900014a45cb","is_index_field":true,"help_text":"","status":"new","description":""},"order_amount__c":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":*************,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"currency","is_unique":false,"label":"引用订单金额","type":"quote","quote_field":"order_id__c__r.order_amount","is_required":false,"api_name":"order_amount__c","define_type":"custom","_id":"636a0d95f18bd900014a46c8","is_single":false,"is_index_field":false,"index_name":"d_5","help_text":"","status":"new","description":""},"version":{"describe_api_name":"master__c","is_index":false,"create_time":*************,"length":8,"is_unique":false,"description":"version","label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"created_by":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"relevant_team":{"describe_api_name":"master__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1667894462356,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"636a0cbef18bd900014a45d1","is_single":false,"label_r":"相关团队","is_index_field":false,"index_name":"a_team","help_text":"相关团队","status":"new","description":""},"percentile__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":true,"is_unique":false,"type":"percentile","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_4","max_length":14,"is_index":true,"is_active":true,"create_time":1667894462347,"is_encrypted":false,"length":12,"default_value":"","label":"百分数","field_num":25,"api_name":"percentile__c","_id":"636a0cbef18bd900014a45c8","is_index_field":false,"help_text":"","status":"new","description":""},"data_own_department":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462328,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"wheres":[],"api_name":"data_own_department","define_type":"package","_id":"636a0cbef18bd900014a45b5","is_single":true,"label_r":"归属部门","is_index_field":false,"index_name":"data_owner_dept_id","help_text":"","status":"new","description":""},"name":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","is_single":false,"label_r":"主属性","index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1667894676952,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"636a0cbef18bd900014a45b2","is_index_field":false,"help_text":"","status":"new"},"email__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462346,"is_encrypted":false,"auto_adapt_places":false,"pattern":"\\\\w+((-w+)|(\\\\.\\\\w+))*\\\\@[A-Za-z0-9]+((\\\\.|-)[A-Za-z0-9]+)*\\\\.[A-Za-z0-9]+","remove_mask_roles":{},"is_unique":false,"default_value":"","label":"邮箱","type":"email","field_num":27,"is_required":false,"api_name":"email__c","define_type":"custom","_id":"636a0cbef18bd900014a45c7","is_single":false,"is_index_field":false,"index_name":"w_2","is_show_mask":false,"help_text":"","status":"new","description":""},"time__c":{"describe_api_name":"master__c","default_is_expression":false,"is_index":true,"is_active":true,"create_time":1667894462343,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"","label":"时间","type":"time","time_zone":"GMT+8","field_num":26,"default_to_zero":false,"is_required":false,"api_name":"time__c","define_type":"custom","date_format":"HH:mm","_id":"636a0cbef18bd900014a45c4","is_single":false,"is_index_field":false,"index_name":"l_3","help_text":"","status":"new","description":""},"_id":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"department__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462355,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"部门","type":"department","field_num":29,"is_required":false,"wheres":[],"api_name":"department__c","define_type":"custom","_id":"636a0cbef18bd900014a45d0","is_single":true,"is_index_field":false,"index_name":"a_7","help_text":"","status":"new","description":""},"tenant_id":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"tenant_id","label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"data_own_organization":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462361,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"636a0cbef18bd900014a45da","is_single":true,"label_r":"归属组织","is_index_field":false,"index_name":"a_1","status":"released","description":""},"location__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462353,"is_encrypted":false,"auto_location":false,"auto_adapt_places":false,"is_unique":false,"label":"定位","is_geo_index":false,"type":"location","field_num":2,"is_required":false,"api_name":"location__c","range_limit":false,"define_type":"custom","_id":"636a0cbef18bd900014a45ce","radius_range":100,"is_single":false,"is_index_field":false,"index_name":"t_1","help_text":"","status":"new","description":""},"select_many__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462339,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":[],"label":"多选","type":"select_many","field_num":4,"is_required":false,"api_name":"select_many__c","options":[{"label":"示例选项","value":"option1"},{"label":"选项1-1","value":"oq5j8vkB7"},{"label":"选项1-2","value":"f281bRLm1"},{"label":"选项2-1","value":"23e3SbkoM"},{"label":"选项3-1","value":"25xn3u48b"},{"label":"选项3-2","value":"7XlIqR89t"},{"label":"选项3-3","value":"G3Ew111Gz"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"636a0cbef18bd900014a45c0","is_single":false,"is_index_field":false,"index_name":"a_2","config":{},"help_text":"","status":"new","description":""},"text__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_2","max_length":100,"is_index":true,"is_active":true,"create_time":1667894462335,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":5,"api_name":"text__c","_id":"636a0cbef18bd900014a45bc","is_index_field":false,"help_text":"","status":"new","description":""},"long_text__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"long_text","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"t_3","max_length":2000,"is_index":true,"is_active":true,"create_time":1667894462336,"is_encrypted":false,"min_length":0,"default_value":"","label":"多行文本","field_num":7,"api_name":"long_text__c","_id":"636a0cbef18bd900014a45bd","is_index_field":false,"help_text":"","status":"new","description":""},"lock_user":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":1667894462222,"is_encrypted":false,"auto_adapt_places":false,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","field_num":9,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"636a0cbef18bd900014a45d4","is_single":true,"label_r":"加锁人","is_index_field":false,"index_name":"a_4","status":"new"},"number__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1667894462340,"is_encrypted":false,"display_style":"input","step_value":1,"length":12,"default_value":"","label":"数字","field_num":8,"api_name":"number__c","_id":"636a0cbef18bd900014a45c1","is_index_field":false,"round_mode":4,"help_text":"","status":"new","description":""},"is_deleted":{"describe_api_name":"master__c","is_index":false,"create_time":*************,"is_unique":false,"description":"is_deleted","default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","options":[],"define_type":"system","index_name":"is_del","status":"released"},"object_describe_api_name":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"object_describe_api_name","label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","max_length":200,"status":"released"},"out_owner":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"index_name":"o_owner","config":{"display":1},"status":"released","description":""},"mc_functional_currency":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":1667894462359,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"本位币","type":"select_one","field_num":15,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"636a0cbef18bd900014a45d8","is_single":false,"label_r":"本位币","is_index_field":false,"index_name":"s_3","config":{},"help_text":"","status":"new"},"order_id__c":{"describe_api_name":"master__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[],"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_9","is_index":true,"is_active":true,"create_time":1667894577415,"is_encrypted":false,"label":"关联订单","target_api_name":"SalesOrderObj","target_related_list_name":"target_related_list_0sBXu__c","field_num":31,"target_related_list_label":"模板对象主","action_on_target_delete":"set_null","related_wheres":[],"api_name":"order_id__c","_id":"636a0d33f18bd900014a4681","is_index_field":true,"help_text":"","status":"new","description":""},"owner":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462326,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"wheres":[],"api_name":"owner","define_type":"package","_id":"636a0cbef18bd900014a45b3","is_single":true,"label_r":"负责人","is_index_field":false,"index_name":"owner","help_text":"","status":"new","description":""},"last_modified_time":{"describe_api_name":"master__c","is_index":true,"create_time":*************,"is_unique":false,"description":"last_modified_time","label":"最后修改时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"true_or_false__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462352,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":true,"label":"布尔值","type":"true_or_false","field_num":18,"is_required":false,"api_name":"true_or_false__c","options":[{"label":"是","value":true},{"label":"否","value":false}],"define_type":"custom","_id":"636a0cbef18bd900014a45cd","is_single":false,"is_index_field":false,"index_name":"b_1","help_text":"","status":"new","description":""},"life_status":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462334,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","field_num":20,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"636a0cbef18bd900014a45bb","is_single":false,"label_r":"生命状态","is_index_field":false,"index_name":"s_5","config":{},"help_text":"","status":"new","description":""},"field_VxDbu__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462351,"is_encrypted":false,"auto_adapt_places":false,"pattern":"^(((http[s]?|ftp):\\\\/\\\\/|www\\\\.)[a-z0-9\\\\.\\\\-]+\\\\.([a-z]{2,4})|((http[s]?|ftp):\\\\/\\\\/)?(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))(\\\\.(([01]?[\\\\d]{1,2})|(2[0-4][\\\\d])|(25[0-5]))){3})(:\\\\d+)?(\\\\/[a-z0-9\\\\$\\\\^\\\\*\\\\+\\\\?\\\\(\\\\)\\\\{\\\\}\\\\.\\\\-_~!@#%&:;\\\\/=<>]*)?","is_unique":false,"default_value":"","label":"网址","type":"url","field_num":22,"is_required":false,"api_name":"field_VxDbu__c","define_type":"custom","_id":"636a0cbef18bd900014a45cc","is_single":false,"is_index_field":false,"index_name":"w_1","help_text":"","status":"new","description":""},"last_modified_by":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"out_tenant_id","label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","index_name":"o_ei","config":{"display":0},"max_length":200,"status":"released"},"mc_currency":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462357,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"default_value":"","label":"币种","type":"select_one","field_num":23,"is_required":false,"api_name":"mc_currency","options":[{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"BHD - Bahraini Dinar","value":"BHD"},{"label":"AED - UAE Dirham","value":"AED"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"636a0cbef18bd900014a45d6","is_single":false,"label_r":"币种","is_index_field":false,"index_name":"s_7","config":{},"help_text":"","status":"new"},"employee__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462354,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","label":"人员","type":"employee","field_num":24,"is_required":false,"wheres":[],"api_name":"employee__c","define_type":"custom","_id":"636a0cbef18bd900014a45cf","is_single":true,"is_index_field":false,"index_name":"a_6","help_text":"","status":"new","description":""},"record_type":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462333,"is_encrypted":false,"auto_adapt_places":false,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"636a0cbef18bd900014a45ba","is_single":false,"label_r":"业务类型","is_index_field":false,"index_name":"r_type","config":{},"help_text":"","status":"released"},"mc_exchange_rate_version":{"describe_api_name":"master__c","is_index":false,"is_active":true,"create_time":1667894462360,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"label":"汇率版本","type":"text","field_num":28,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"636a0cbef18bd900014a45d9","is_single":false,"label_r":"汇率版本","is_index_field":false,"index_name":"t_5","max_length":256,"help_text":"","status":"new"},"select_one__c":{"describe_api_name":"master__c","is_index":true,"is_active":true,"create_time":1667894462338,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"","label":"单选","type":"select_one","field_num":30,"is_required":false,"api_name":"select_one__c","options":[{"label":"示例选项","value":"option1"},{"label":"选项1","value":"HinBvn762"},{"label":"选项2","value":"GO7ag7snC"},{"label":"选项3","value":"1yyL7UyRM"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"636a0cbef18bd900014a45bf","is_single":false,"is_index_field":false,"index_name":"s_8","config":{},"help_text":"","status":"new","description":""}},"release_version":"6.4","actions":{}}'''
        describe.fromJsonString(describeJson)

        Mockito.when(describeLogicService.findObjectWithoutCopyIfGray("12321", "master__c")).thenReturn(describe)

        def objectData = Mockito.mock(IObjectData)
        Mockito.when(dataProxy.update(Mockito.any(), Mockito.any())).thenReturn(objectData)
        Mockito.when(dataProxy.create(Mockito.any(), Mockito.any())).thenReturn(objectData)

        Mockito.when(autoNumberLogicService.calculateAutoNumberValue(Mockito.any(IObjectDescribe), Mockito.anyList())).thenReturn([])
    }

    def "test saveObjectData"() {
        given:
        def context = Stub(IActionContext)

        and: "mock objectData"
        IObjectData objectData = new ObjectData()
        objectData.fromJsonString('''{"name":"test-1000","owner":"1000","mc_currency":"CNY","object_describe_api_name":"master__c"}''')

        and: "mock orgService"
        Mockito.when(orgService.getUserNameMapByIds(Mockito.anyString(), Mockito.anyString(), Mockito.anyList())).thenReturn(["1000": "管理员张三"])

        when:
        def ret = metaDataActionService.saveObjectData(user, objectData, context)
        then:
        Mockito.verify(dataProxy.create(objectData, context), Mockito.times(1))
    }

    def "test updateObjectData"() {
        given:
        def objectData = new ObjectData()
        objectData.fromJsonString('''{"name":"test-1000","object_describe_api_name":"master__c"}''')

        when:
        def ret = metaDataActionService.updateObjectData(user, objectData)
        then:
        noExceptionThrown()
    }

    def "test updateObjectData context"() {
        given:
        def objectData = new ObjectData()
        objectData.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def context = Stub(IActionContext)

        when:
        def ret = metaDataActionService.updateObjectData(user, objectData, context)
        then:
        Mockito.verify(dataProxy.update(objectData, context), Mockito.times(1))
    }


    def "test updateObjectData allowUpdateInvalid"() {
        given:
        def objectData = new ObjectData()
        objectData.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        when:

        def ret = metaDataActionService.updateObjectData(user, objectData, true)
        then:
        noExceptionThrown()
    }

    def "test invalid"() {
        given:
        def objectData = new ObjectData()
        objectData.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def context = Stub(IActionContext)

        when:
        def ret = metaDataActionService.invalid(objectData, user, context)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "test bulkUpsertObjectData emptyData:#empty"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def dataList = empty ? [] : [data]

        def objectDataServiceImpl = Mock(ObjectDataServiceImpl)
        MetaDataActionServiceImpl metaDataActionService = new MetaDataActionServiceImpl("objectDataService": objectDataServiceImpl)
        when:
        metaDataActionService.bulkUpsertObjectData(dataList, user)
        then:
        expect * objectDataServiceImpl.bulkCreate(dataList, false, _ as IActionContext) >> dataList

        where:
        empty || expect
        false || 1
        true  || 0
    }

    def "test batchUpdateOrderBy"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def dataList = [data]

        Mockito.when(objectDataService.batchUpdateOrderBy(Mockito.anyList(), Mockito.any(IActionContext))).thenReturn([])
        when:
        metaDataActionService.batchUpdateOrderBy(dataList, user)
        then:
        noExceptionThrown()
    }

    def "test batchUpdateWithFieldsForCalculate"() {
        given:
        def context = Stub(IActionContext)
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def dataList = [data]

        when:
        metaDataActionService.batchUpdateWithFieldsForCalculate(context, dataList, ["name"])
        then:
        noExceptionThrown()
    }

    def "test batchUpdate"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def dataList = [data]
        when:

        metaDataActionService.batchUpdate(dataList, user)
        then:
        noExceptionThrown()
    }

    def "test updateWithMap"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")

        def fieldMap = ["name": "1123"]
        when:
        metaDataActionService.updateWithMap(user, data, fieldMap)
        then:
        noExceptionThrown()
    }

    def "test bulkLockObjectData"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def dataList = [data]

        when:
        metaDataActionService.bulkLockObjectData(dataList, isLock, lockRule, user)
        then:
        noExceptionThrown()

        where:
        isLock | lockRule
        true   | "lockRule"
    }

    def "test bulkUpdateObjectDataOneField"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c"}""")
        def dataList = [data]
        when:
        metaDataActionService.bulkUpdateObjectDataOneField(fieldName, dataList, value, user)
        then:
        noExceptionThrown()
        where:
        fieldName | value
        "name"    | 123
    }

    def "test bulkRecover"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        Mockito.when(dataProxy.bulkRecover(Mockito.anyList(), Mockito.any(IActionContext))).thenReturn(dataList)
        when:
        metaDataActionService.bulkRecover(dataList, user)
        then:
        noExceptionThrown()
    }

    def "test bulkUpdateWithMap"() {
        given:
        def context = Stub(IActionContext)
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        def map = ["name": "test-1001"]

        when:
        metaDataActionService.bulkUpdateWithMap(user, dataList, map, context)
        then:
        noExceptionThrown()
    }

    def "test bulkSaveObjectData"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        when:
        metaDataActionService.bulkSaveObjectData(dataList, user)
        then:
        noExceptionThrown()
    }

    def "test batchUpdateRelevantTeam"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        when:
        metaDataActionService.batchUpdateRelevantTeam(user, dataList, true)
        then:
        noExceptionThrown()
    }

    def "test bulkInvalidAndDeleteWithSuperPrivilege"() {
        given:
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        when:
        metaDataActionService.bulkInvalidAndDeleteWithSuperPrivilege(dataList, user)
        then:
        noExceptionThrown()
    }

    def "test bulkAssociate"() {
        given:
        def masterDataId = "11111"
        def masterDescribe = Mock(IObjectDescribe)
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        and: "mock"
        masterDescribe.getApiName() >> "AccountObj"
        masterDescribe.getFieldDescribe("name") >> Stub(IFieldDescribe)
        def returnData = Stub(IObjectData)
        Mockito.when(metaDataFindService.findObjectDataIgnoreFormula(user, masterDataId, masterDescribe.getApiName())).thenReturn(returnData)

        when:
        metaDataActionService.bulkAssociate(masterDataId, masterDescribe, describe, user, dataList, "account__c", ["account__c"])
        then:
        noExceptionThrown()
    }

    def "test bulkDisassociate"() {
        given:
        def masterDataId = "11111"
        def masterDescribe = Mock(IObjectDescribe)
        def data = new ObjectData()
        data.fromJsonString("""{"name":"test-1000","object_describe_api_name":"master__c","life_status_before_invalid":"123"}""")
        def dataList = [data]

        and: "mock"
        masterDescribe.getApiName() >> "AccountObj"
        masterDescribe.getFieldDescribe("name") >> Stub(IFieldDescribe)
        def returnData = Stub(IObjectData)
        Mockito.when(metaDataFindService.findObjectDataIgnoreFormula(user, masterDataId, masterDescribe.getApiName())).thenReturn(returnData)

        when:
        metaDataActionService.bulkDisassociate(masterDataId, masterDescribe, describe, user, dataList, "account__c", ["account__c"])
        then:
        noExceptionThrown()
    }
}
