package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.layout.component.RelatedListFormComponentExt
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.ui.layout.ILayout
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022/4/15
 */
class LayoutLogicServiceImplTest extends Specification {
    LayoutLogicServiceImpl layoutLogicService
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    ConfigService configService = Mock(ConfigService)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    def setup() {
        layoutLogicService = new LayoutLogicServiceImpl("describeLogicService": describeLogicService, "configService": configService)
    }

    def "test checkEditLayoutComponent no expect"() {
        given: "初始化参数"

        User user = Mock(User)
        LayoutExt layoutExt = Mock(LayoutExt)

        and: "构造中间变量"
        IObjectDescribe describe = Stub(IObjectDescribe)
        RelatedListFormComponentExt component = Mock(RelatedListFormComponentExt)

        def clos1 = { param -> returnComponents ? [component] : [] }
        when:
        layoutLogicService.checkEditLayoutComponent(user, layoutExt)
        then:
        1 * layoutExt.isEditLayout() >> isEditLayout
        step1time * layoutExt.getRelatedListFormComponents() >> clos1.call(returnComponents)
        step2time * component.getRefObjectApiName() >> refObjectApiName
        step2time * component.getFieldApiName() >> relatedFieldName
        step2time * describeLogicService.findObject(tenantId, refObjectApiName) >> describe
        step2time * describeLogicService.isRelatedListFormSupportObject(user, describe, relatedFieldName) >> relatedListFormSupport
        step2time * user.getTenantId() >> tenantId
        noExceptionThrown()

        where:
        tenantId | refObjectApiName  | relatedFieldName || isEditLayout | returnComponents | relatedListFormSupport || step1time | step2time
        "74255"  | "object_2Y030__c" | "field_Eu16z__c" || false        | _                | _                      || 0         | 0
        "74255"  | "object_2Y030__c" | "field_Eu16z__c" || true         | false            | _                      || 1         | 0
        "74255"  | "object_2Y030__c" | "field_Eu16z__c" || true         | true             | true                   || 1         | 1
    }

    def "test checkEditLayoutComponent throw expect"() {
        given: "初始化参数"

        User user = Mock(User)
        LayoutExt layoutExt = Mock(LayoutExt)

        and: "构造中间变量"
        IObjectDescribe describe = Stub(IObjectDescribe)
        RelatedListFormComponentExt component = Mock(RelatedListFormComponentExt)

        def clos1 = { param -> returnComponents ? [component] : [] }
        when:
        layoutLogicService.checkEditLayoutComponent(user, layoutExt)
        then:
        1 * layoutExt.isEditLayout() >> isEditLayout
        step1time * layoutExt.getRelatedListFormComponents() >> clos1.call(returnComponents)
        step2time * component.getRefObjectApiName() >> refObjectApiName
        step2time * component.getFieldApiName() >> relatedFieldName
        step2time * describeLogicService.findObject(tenantId, refObjectApiName) >> describe
        step2time * describeLogicService.isRelatedListFormSupportObject(user, describe, relatedFieldName) >> relatedListFormSupport
        step3time * user.getTenantId() >> tenantId

        thrown(ValidateException)

        where:
        tenantId | refObjectApiName  | relatedFieldName || isEditLayout | returnComponents | relatedListFormSupport || step1time | step2time | step3time
        "74255"  | "object_2Y030__c" | "field_Eu16z__c" || true         | true             | false                  || 1         | 1         | 2
    }

    def "test isDealUIEvent"() {
        given:
        Layout layout = Spy()
        layout.set(LayoutExt.IS_DEAL_UI, flag)
        layout.setLayoutType(ILayout.DETAIL_LAYOUT_TYPE)
        layout.setUiEventIds(uiEventIds)

        Layout layoutDB = Spy()
        layoutDB.setUiEventIds(uiEventIdsInDB)
        when:
        configService.findTenantConfig(_, _) >> value
        def result = layoutLogicService.isDealUIEvent(layout, layoutDB, '74255')
        then:
        isDealUIEvent == result
        where:
        flag  | uiEventIds | uiEventIdsInDB | value || isDealUIEvent
        true  | []         | ['id1']        | "1"   || true
        false | []         | ['id1']        | "1"   || true
        false | ['id1']    | ['id1']        | "1"   || false
        false | []         | ['id1']        | "1"   || true
        false | []         | []             | "1"   || false
        false | []         | ['id1']        | "0"   || false
    }

    def "test isDealUIEvent not exist layout in db"() {
        given:
        Layout layout = Spy()
        layout.set(LayoutExt.IS_DEAL_UI, flag)
        layout.setLayoutType(ILayout.DETAIL_LAYOUT_TYPE)
        layout.setUiEventIds(uiEventIds)


        when:
        configService.findTenantConfig(_, _) >> value
        def result = layoutLogicService.isDealUIEvent(layout, null, '74255')
        then:
        isDealUIEvent == result
        where:
        flag  | uiEventIds | value || isDealUIEvent
        false | []         | "1"   || false
    }
}
