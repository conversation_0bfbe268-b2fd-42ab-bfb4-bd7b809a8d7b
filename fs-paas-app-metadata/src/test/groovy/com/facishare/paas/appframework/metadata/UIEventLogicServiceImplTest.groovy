package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.metadata.api.IUIEvent
import com.facishare.paas.metadata.exception.ErrorCode
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.service.impl.UIEventService
import spock.lang.Specification

class UIEventLogicServiceImplTest extends Specification {

    UIEventLogicService UIEventLogicService
    UIEventService eventService = Mock(UIEventService)

    def setup() {
        UIEventLogicService = new UIEventLogicServiceImpl("eventService": eventService)

    }

    def "CreateEvents"() {
        given:
        def user = User.systemUser("74255")
        def uIEvent = Mock(IUIEvent)
        def list = []
        list << uIEvent
        eventService.batchInsert(_, _) >> list
        when:
        def events = UIEventLogicService.createEvents(list, user)
        then:
        noExceptionThrown()
        assert CollectionUtils.notEmpty(events)
    }

    def "CreateEvents exception"() {
        given:
        def user = User.systemUser("74255")
        def uIEvent = Mock(IUIEvent)
        def list = []
        list << uIEvent
        eventService.batchInsert(_, _) >> { throw new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "错误") }
        when:
        def events = UIEventLogicService.createEvents(list, user)
        then:
        thrown(MetaDataBusinessException)
    }

    def "CreateOrUpdateEvents"() {
        given:
        def user = User.systemUser("74255")
        def uIEvent = Mock(IUIEvent)
        def list = []
        list << uIEvent
        eventService.batchUpsert(_, _) >> list
        when:
        def events = UIEventLogicService.createOrUpdateEvents(list, user)
        then:
        noExceptionThrown()
        assert CollectionUtils.notEmpty(events)
    }

    def "CreateOrUpdateEvents exception"() {
        given:
        def user = User.systemUser("74255")
        def uIEvent = Mock(IUIEvent)
        def list = []
        list << uIEvent
        eventService.batchUpsert(_, _) >> { throw new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "错误") }
        when:
        def events = UIEventLogicService.createOrUpdateEvents(list, user)
        then:
        thrown(MetaDataBusinessException)
    }

    def "FindEventListByIds"() {
        given:
        def uIEvent = Mock(IUIEvent)
        def eventsList = []
        eventsList << uIEvent
        eventService.findUIEventsByIdList(_, _) >> eventsList
        when:
        def events = UIEventLogicService.findEventListByIds(list, "74255")
        then:
        returnEmpty == CollectionUtils.empty(events)
        where:
        list  || returnEmpty
        ["1"] || false
        []    || true
    }

    def "BatchDelete"() {
        given:
        def user = User.systemUser("74255")
        when:
        eventService.batchDelete(_, _) >> true
        UIEventLogicService.batchDelete(["1"], user)
        then:
        1 * eventService.batchDelete(_, _)
    }

    def "BatchDelete empty list"() {
        given:
        def user = User.systemUser("74255")
        when:
        eventService.batchDelete(_, _) >> true
        UIEventLogicService.batchDelete([], user)
        then:
        0 * eventService.batchDelete(_, _)
    }

    def "BatchDelete exception"() {
        given:
        def user = User.systemUser("74255")
        when:
        eventService.batchDelete(_, _) >> { throw new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "错误") }
        UIEventLogicService.batchDelete(["1"], user)
        then:
        thrown(MetaDataBusinessException)
    }

    def "FindEventById"() {
        given:
        def uIEvent = Mock(IUIEvent)
        when:
        eventService.findByTenantIdAndId(_, _) >> uIEvent
        then:
        def uiEvent = UIEventLogicService.findEventById(id, "74255")
        result == Objects.isNull(uiEvent)
        where:
        id  || result
        ""  || true
        "1" || false
    }

    def "BatchUpdateUIEvent"() {
        given:
        def user = User.systemUser("74255")
        def uIEvent = Mock(IUIEvent)
        def uiEventList = []
        uiEventList << uIEvent
        eventService.batchUpdate(_, _) >> uiEventList
        when:
        def events = UIEventLogicService.batchUpdateUIEvent(list, user)
        then:
        result == CollectionUtils.notEmpty(events)
        where:
        list        || result
        []          || false
        buildList() || true
    }

    def buildList() {
        def uIEvent = Mock(IUIEvent)
        def uiEventList = []
        uiEventList << uIEvent
        return uiEventList
    }

    def "BatchUpdateUIEvent exception"() {
        given:
        def user = User.systemUser("74255")
        def uIEvent = Mock(IUIEvent)
        def list = []
        list << uIEvent
        eventService.batchUpdate(_, _) >> { throw new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "错误") }
        when:
        def events = UIEventLogicService.batchUpdateUIEvent(list, user)
        then:
        thrown(MetaDataBusinessException)
    }

    def "FindEventByObject"() {
        given:
        def uIEvent = Mock(IUIEvent)
        def list = []
        list << uIEvent
        when:
        eventService.findUIEventsByDescribeApiName(_, _) >> list
        then:
        def uiEventList = UIEventLogicService.findEventByObject(describeApiName, "74255")
        Objects.isNull(uiEventList) == result
        where:
        describeApiName || result
        null            || true
        "AccountObj"    || false
    }
}
