package com.facishare.paas.appframework.metadata


import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel
import com.facishare.paas.appframework.metadata.layout.LayoutTypes
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.ui.layout.ILayout
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * create by z<PERSON><PERSON> on 2020/07/09
 */
class RecordTypeLogicServiceImplTest extends Specification {

    RecordTypeLogicServiceImpl recordTypeLogicService
    LayoutLogicService layoutService = Mock(LayoutLogicService)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        recordTypeLogicService = new RecordTypeLogicServiceImpl(layoutService: layoutService)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateLayoutAndRecordType方法在不同场景下的布局验证逻辑
     */
    @Unroll
    def "validateLayoutAndRecordTypeTest各种场景"() {
        given: "准备测试数据和Mock对象"
        def user = new User("testTenant", "testUser")
        def objectDescribes = Maps.newHashMap()
        
        // 创建主对象描述
        def masterObjectDescribe = Mock(IObjectDescribe)
        masterObjectDescribe.getApiName() >> "MasterObj"
        objectDescribes.put("MasterObj", masterObjectDescribe)
        
        // 创建子对象描述
        def detailObjectDescribe = Mock(IObjectDescribe)
        detailObjectDescribe.getApiName() >> "DetailObj"
        objectDescribes.put("DetailObj", detailObjectDescribe)
        
        // 创建测试数据模型
        def masterLayoutRuleInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("masterLayout")
                .recordType("masterRecordType")
                .build()
        
        def detailLayoutRuleInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("detailLayout")
                .recordType("detailRecordType")
                .build()
        
        def masterModel = DescribeLayoutValidateModel.builder()
                .objectApiName("MasterObj")
                .isMaster(true)
                .masterRecordType("masterRecordType")
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList(masterLayoutRuleInfo))
                .build()
        
        def detailModel = DescribeLayoutValidateModel.builder()
                .objectApiName("DetailObj")
                .isMaster(false)
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList(detailLayoutRuleInfo))
                .build()
        
        def describeLayoutValidateModels = Lists.newArrayList(masterModel, detailModel)
        
        // 根据测试场景配置Mock布局
        def masterLayout = createMockLayout(masterLayoutFound ? "masterLayout" : "otherLayout")
        def detailLayout = createMockLayout(detailLayoutFound ? "detailLayout" : "otherLayout")
        
        layoutService.findObjectLayoutWithType(_, _, _, _, _, _) >>> [masterLayout, detailLayout]

        when: "调用验证布局的方法"
        def result = recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, describeLayoutValidateModels)

        then: "验证结果"
        result == expectedResult

        where: "测试场景"
        masterLayoutFound | detailLayoutFound | expectedResult
        true             | true              | true
        true             | false             | false
        false            | true              | false
        false            | false             | false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateLayoutAndRecordType方法在没有主对象的情况下的处理
     */
    def "validateLayoutAndRecordTypeErrorNoMasterObject"() {
        given: "准备测试数据和Mock对象，但不包含主对象"
        def user = new User("testTenant", "testUser")
        def objectDescribes = Maps.newHashMap()
        
        // 创建子对象描述
        def detailObjectDescribe = Mock(IObjectDescribe)
        detailObjectDescribe.getApiName() >> "DetailObj"
        objectDescribes.put("DetailObj", detailObjectDescribe)
        
        // 创建测试数据模型，但都不是主对象
        def detailLayoutRuleInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("detailLayout")
                .recordType("detailRecordType")
                .build()
        
        def detailModel = DescribeLayoutValidateModel.builder()
                .objectApiName("DetailObj")
                .isMaster(false)
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList(detailLayoutRuleInfo))
                .build()
        
        def describeLayoutValidateModels = Lists.newArrayList(detailModel)

        when: "调用验证布局的方法"
        def result = recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, describeLayoutValidateModels)

        then: "验证结果"
        result == false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateLayoutAndRecordType方法在对象描述为空的情况下的处理
     */
    def "validateLayoutAndRecordTypeErrorNullObjectDescribe"() {
        given: "准备测试数据和Mock对象，但对象描述为空"
        def user = new User("testTenant", "testUser")
        def objectDescribes = Maps.newHashMap()
        
        // 创建测试数据模型
        def masterLayoutRuleInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("masterLayout")
                .recordType("masterRecordType")
                .build()
        
        def masterModel = DescribeLayoutValidateModel.builder()
                .objectApiName("MasterObj")  // 但在objectDescribes中不存在
                .isMaster(true)
                .masterRecordType("masterRecordType")
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList(masterLayoutRuleInfo))
                .build()
        
        def describeLayoutValidateModels = Lists.newArrayList(masterModel)

        when: "调用验证布局的方法"
        def result = recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, describeLayoutValidateModels)

        then: "验证结果"
        result == false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateLayoutAndRecordType方法在layoutRuleValidateInfos为空的情况下的处理
     */
    def "validateLayoutAndRecordTypeTestEmptyLayoutRuleValidateInfos"() {
        given: "准备测试数据和Mock对象，但layoutRuleValidateInfos为空"
        def user = new User("testTenant", "testUser")
        def objectDescribes = Maps.newHashMap()
        
        // 创建主对象描述
        def masterObjectDescribe = Mock(IObjectDescribe)
        masterObjectDescribe.getApiName() >> "MasterObj"
        objectDescribes.put("MasterObj", masterObjectDescribe)
        
        // 创建子对象描述
        def detailObjectDescribe = Mock(IObjectDescribe)
        detailObjectDescribe.getApiName() >> "DetailObj"
        objectDescribes.put("DetailObj", detailObjectDescribe)
        
        // 创建测试数据模型，但layoutRuleValidateInfos为空
        def masterModel = DescribeLayoutValidateModel.builder()
                .objectApiName("MasterObj")
                .isMaster(true)
                .masterRecordType("masterRecordType")
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList())  // 空列表
                .build()
        
        def detailModel = DescribeLayoutValidateModel.builder()
                .objectApiName("DetailObj")
                .isMaster(false)
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList())  // 空列表
                .build()
        
        def describeLayoutValidateModels = Lists.newArrayList(masterModel, detailModel)

        when: "调用验证布局的方法"
        def result = recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, describeLayoutValidateModels)

        then: "验证结果 - 空的layoutRuleValidateInfos应当返回true（因为没有规则需要验证）"
        result == true
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateLayoutAndRecordType方法在有多个layoutRuleValidateInfos的情况下的处理
     */
    @Unroll
    def "validateLayoutAndRecordTypeTestMultipleLayoutRuleValidateInfos"() {
        given: "准备测试数据和Mock对象，具有多个layoutRuleValidateInfos"
        def user = new User("testTenant", "testUser")
        def objectDescribes = Maps.newHashMap()
        
        // 创建主对象描述
        def masterObjectDescribe = Mock(IObjectDescribe)
        masterObjectDescribe.getApiName() >> "MasterObj"
        objectDescribes.put("MasterObj", masterObjectDescribe)
        
        // 创建测试数据模型，包含多个layoutRuleValidateInfos
        def layoutRuleInfo1 = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("layout1")
                .recordType("recordType1")
                .build()
                
        def layoutRuleInfo2 = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("layout2")
                .recordType("recordType2")
                .build()
        
        def masterModel = DescribeLayoutValidateModel.builder()
                .objectApiName("MasterObj")
                .isMaster(true)
                .masterRecordType("masterRecordType")
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList(layoutRuleInfo1, layoutRuleInfo2))
                .build()
        
        def describeLayoutValidateModels = Lists.newArrayList(masterModel)
        
        // 根据测试场景配置Mock布局
        def layout1 = createMockLayout(layout1Found ? "layout1" : "otherLayout")
        def layout2 = createMockLayout(layout2Found ? "layout2" : "otherLayout")
        
        layoutService.findObjectLayoutWithType(_, _, _, _, _, _) >>> [layout1, layout2]

        when: "调用验证布局的方法"
        def result = recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, describeLayoutValidateModels)

        then: "验证结果"
        result == expectedResult

        where: "测试场景"
        layout1Found | layout2Found | expectedResult
        true         | true         | true
        true         | false        | false
        false        | true         | false
        false        | false        | false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateLayoutAndRecordType方法在布局为null的情况下的处理
     */
    def "validateLayoutAndRecordTypeErrorNullLayout"() {
        given: "准备测试数据和Mock对象，但布局为null"
        def user = new User("testTenant", "testUser")
        def objectDescribes = Maps.newHashMap()
        
        // 创建主对象描述
        def masterObjectDescribe = Mock(IObjectDescribe)
        masterObjectDescribe.getApiName() >> "MasterObj"
        objectDescribes.put("MasterObj", masterObjectDescribe)
        
        // 创建测试数据模型
        def masterLayoutRuleInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName("masterLayout")
                .recordType("masterRecordType")
                .build()
        
        def masterModel = DescribeLayoutValidateModel.builder()
                .objectApiName("MasterObj")
                .isMaster(true)
                .masterRecordType("masterRecordType")
                .layoutType(LayoutTypes.DETAIL)
                .layoutRuleValidateInfos(Lists.newArrayList(masterLayoutRuleInfo))
                .build()
        
        def describeLayoutValidateModels = Lists.newArrayList(masterModel)
        
        // 返回null布局
        layoutService.findObjectLayoutWithType(_, _, _, _, _, _) >> null

        when: "调用验证布局的方法"
        def result = recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, describeLayoutValidateModels)

        then: "验证结果 - 布局为null应当返回false"
        result == false
    }
    
    // 帮助方法：创建模拟布局
    private ILayout createMockLayout(String layoutName) {
        def layout = Mock(ILayout)
        layout.getName() >> layoutName
        return layout
    }
}
