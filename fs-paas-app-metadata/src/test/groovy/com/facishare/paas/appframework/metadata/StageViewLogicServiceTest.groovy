package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.config.ConfigValueType
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.dto.StageViewInfo
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * create by <PERSON><PERSON><PERSON> on 2020/02/05
 */
class StageViewLogicServiceTest extends Specification {

    StageViewLogicService stageViewLogicService
    ConfigService configService
    DescribeLogicService describeLogicService
    def user
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    void setup() {
        configService = Mock(ConfigService)
        describeLogicService = Mock(DescribeLogicService)
        stageViewLogicService = new StageViewLogicServiceImpl("configService": configService, "describeLogicService": describeLogicService)
        user = new User("78057", "1000")
    }

    def "test_save_stage_view_validate_arg"() {
        when:
        def viewInfo = StageViewInfo.fromJsonArray(viewInfoJson)
        stageViewLogicService.saveStageView(describeApiName, viewInfo, user)
        then:
        thrown(ValidateException)
        where:
        describeApiName | viewInfoJson
        ""              | ""
    }

    def "test_save_stage_view"() {
        when:
        def viewInfo = StageViewInfo.fromJsonArray(json)
        def isSuccess = stageViewLogicService.saveStageView(describeApiName, viewInfo, user)
        then:
        isSuccess
        0 * configService.createUserConfig(user, describeApiName + StageViewLogicServiceImpl.STAGE_VIEW_POST, json, ConfigValueType.JSON)
        where:
        describeApiName | json
        "ObjectA"       | '''[{"api_name":"name","label":"商机名称","render_type":"text"},{"api_name":"probability","label":"赢率","render_type":"percentile"},{"api_name":"stg_changed_time","label":"阶段变更时间","render_type":"time"},{"api_name":"sales_status","label":"阶段状态","render_type":"select_one"},{"api_name":"sales_process_id","label":"销售流程","render_type":"select_one"}]'''
    }

    def "test_get_stage_view"() {
        given:
        IObjectDescribe describe = Mock(IObjectDescribe)

        when:
        def viewInfo = stageViewLogicService.findStageView(describeApiName, user)
        then:
        CollectionUtils.notEmpty(viewInfo)
        1 * configService.findUserConfig(user, describeApiName + StageViewLogicServiceImpl.STAGE_VIEW_POST) >> config
        1 * describeLogicService.findObject(user.getTenantId(), describeApiName) >> describe

        describe.containsField(_) >> true
        describe.getFieldDescribe(_) >> Mock(IFieldDescribe)
        println(viewInfo)
        where:
        describeApiName || config
        "ObjectB"       || '''[{"is_show":true,"api_name":"name","label":"商机名称","render_type":"text","field_name":"name","order":0},{"is_show":true,"api_name":"probability","label":"赢率","render_type":"percentile","field_name":"probability","order":1},{"is_show":true,"api_name":"stg_changed_time","label":"阶段变更时间","render_type":"time","field_name":"stg_changed_time","order":2},{"is_show":true,"api_name":"sales_status","label":"阶段状态","render_type":"select_one","field_name":"sales_status","order":3},{"is_show":true,"api_name":"sales_process_id","label":"销售流程","render_type":"select_one","field_name":"sales_process_id","order":4}]'''
    }

    def "test_get_stage_view_config_is_empty"() {
        given:
        when:
        def viewInfo = stageViewLogicService.findStageView(describeApiName, user)
        then:
        CollectionUtils.empty(viewInfo)
        1 * configService.findUserConfig(user, describeApiName + StageViewLogicServiceImpl.STAGE_VIEW_POST) >> ""
        println(viewInfo)
        where:
        describeApiName || _
        "ObjectB"       || _
    }
}
