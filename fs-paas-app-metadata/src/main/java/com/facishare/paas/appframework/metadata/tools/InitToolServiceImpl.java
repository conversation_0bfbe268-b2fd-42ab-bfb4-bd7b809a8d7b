package com.facishare.paas.appframework.metadata.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ExtraDescribeLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.tools.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.handler.HandlerDefinitionService;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.handler.HandlerRuntimeConfigService;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectFieldExtra;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseIdsByEnvironment;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.notifier.support.NotifierClient;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.BinaryOperator;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_PACKAGE;

@Slf4j
@Service("initToolService")
public class InitToolServiceImpl implements InitToolService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private BrushToolsTransferProxy brushToolsTransferProxy;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private HandlerRuntimeConfigService handlerRuntimeConfigService;
    @Autowired
    private HandlerDefinitionService handlerDefinitionService;
    @Autowired
    private HandlerLogicService handlerLogicService;
    @Autowired
    private RedisDao redisDao;

    private ExtraDescribeLogicService objExtraService;

    private Map<String, String> routeTenantId2EnvLabelMap;

    @Autowired
    public void setObjExtraService(ExtraDescribeLogicService objExtraService) {
        this.objExtraService = objExtraService;
    }

    private RateLimiter rateLimiter;
    private final ExecutorService initToolsExecutorService = DynamicExecutors.newThreadPool(
            20,
            50,
            10000L,
            1000,
            new ThreadFactoryBuilder()
                    .setNameFormat("ParallelUtils-InitTools-%d")
                    .setDaemon(true)
                    .build());
    private static final String PREFIX = "-";

    private static final Splitter SEMICOLON_SPLITTER = Splitter.on(";").omitEmptyStrings().trimResults();

    @PostConstruct
    void init() {
        ConfigFactory.getInstance().getConfig("fs-paas-appframework-config",
                config -> {
                    try {
                        rateLimiter = RateLimiter.create(config.getDouble("init_tools_rate_limiter", 20));
                    } catch (Exception e) {
                        log.warn("cannot reload {} and exception: {}", config.getName(), e);
                    }
                }
        );
    }

    @Override
    public List<Integer> getEnterpriseList(Integer currentEnv, List<Integer> runStatusList) {
        GetEnterpriseIdsByEnvironment.Arg envArg = new GetEnterpriseIdsByEnvironment.Arg();
        envArg.setEnv(currentEnv);
        envArg.setRunStatusList(CollectionUtils.empty(runStatusList) ? Lists.newArrayList(2) : runStatusList);
        GetEnterpriseIdsByEnvironment.Result ret = enterpriseEditionService.getEnterpriseIdsByEnvironment(envArg);
        return ret.getEnterpriseIds();
    }

    @Override
    public BrushProgress.Result progressControl(BrushProgress.Arg arg) {
        String taskDataId = arg.getTaskDataId();
        String taskRecordType = arg.getTaskRecordType();
        int action = arg.getAction();
        // action=1 查询为停止处理
        if (action == 1) {
            redisDao.delKey(InitToolService.getKey(taskRecordType, taskDataId));
            return BrushProgress.Result.builder().build();
        }
        String successKey = InitToolService.getKey(taskRecordType, taskDataId, SUCCESS);
        String failureKey = InitToolService.getKey(taskRecordType, taskDataId, FAIL);
        String totalKey = InitToolService.getKey(taskRecordType, taskDataId, TOTAL);
        if (action == 2) {
            redisDao.batchDelKey(Lists.newArrayList(successKey, failureKey, totalKey));
            return BrushProgress.Result.builder().build();
        }
        String totalStr = redisDao.getStrCache(totalKey);
        Long total = StringUtils.isEmpty(totalStr) ? 0 : Long.parseLong(totalStr);
        Long succeed = redisDao.getLen(successKey);
        Long failed = redisDao.getLen(failureKey);
        List<String> successEnterpriseIds = redisDao.getlRange(successKey, 0, -1);
        List<String> failureEnterpriseIds = redisDao.getlRange(failureKey, 0, -1);
        return BrushProgress.Result.builder()
                .totalCount(total)
                .successCount(succeed)
                .failureCount(failed)
                .successEnterpriseIds(successEnterpriseIds)
                .failureEnterpriseIds(failureEnterpriseIds)
                .build();
    }

    private void execute(TransferBrush.Arg arg, String enterpriseId, List<String> routeTenantIds) {
        if (CollectionUtils.notEmpty(routeTenantIds)) {
            routeTenantIds.forEach(routeTenantId -> executeBrush(arg, enterpriseId, routeTenantId));
        } else {
            executeBrush(arg, enterpriseId, enterpriseId);
        }
    }

    private void executeBrush(TransferBrush.Arg arg, String enterpriseId, String routeTenantId) {
        if (!CollectionUtils.notEmpty(routeTenantId2EnvLabelMap)) {
            routeTenantId2EnvLabelMap = AppFrameworkConfig.getRouteTenantId2EnvLabelMap();
        }
        if (!redisDao.exists(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId()))) {
            redisDao.lpush(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), FAIL), enterpriseId);
            return;
        }
        String actionCode = null;
        InitTools.Arg initToolsArg = null;
        Map<String, String> headers = RestUtils.buildHeaders(User.systemUser(routeTenantId));
        if ("default__c".equals(arg.getTaskRecordType())) {
            actionCode = "createOrUpdateDescribe";
            initToolsArg = BrushDescribe.Arg.builder()
                    .describeApiName(arg.getDescribeApiName())
                    .describeAttribute(arg.getDescribeAttribute())
                    .describeExtraAttribute(arg.getDescribeExtraAttribute())
                    .fieldExtraAttribute(arg.getFieldExtraAttribute())
                    .describeJson(arg.getDescribeJson())
                    .fields(arg.getFields())
                    .tenantId(enterpriseId)
                    .build();

        } else if ("record_layout__c".equals(arg.getTaskRecordType())) {
            actionCode = "createOrUpdateLayouts";
            initToolsArg = BrushLayouts.Arg.builder()
                    .detailLayout(arg.getDetailLayout())
                    .mobileLayout(arg.getMobileLayout())
                    .listLayout(arg.getListLayout())
                    .addEditLayout(arg.getAddEditLayout())
                    .describeApiName(arg.getDescribeApiName())
                    .tenantId(enterpriseId)
                    .build();
        }
        InitTools.Result brushResult;
        try {
            brushResult = brushToolsTransferProxy.executeBrush(actionCode, initToolsArg, headers);
            if (Objects.nonNull(brushResult) && (brushResult.getCode() == 0 || brushResult.getCode() == 200)) {
                redisDao.lpush(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), SUCCESS), enterpriseId);
            } else {
                brushFailed(arg, enterpriseId, routeTenantId, brushResult.getMessage());
            }
        } catch (Exception e) {
            brushFailed(arg, enterpriseId, routeTenantId, e.getMessage());
        }
    }

    private void brushFailed(TransferBrush.Arg arg, String enterpriseId, String routeTenantId, String brushResult) {
        redisDao.lpush(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), FAIL), enterpriseId);
        if (StringUtils.contains(enterpriseId, PREFIX) && CollectionUtils.nullToEmpty(routeTenantId2EnvLabelMap).containsKey(routeTenantId)) {
            String envLabel = routeTenantId2EnvLabelMap.get(routeTenantId);
            sendAuditLog(enterpriseId, arg.getDescribeApiName(), String.format("%s[%s]:%s", envLabel, enterpriseId, brushResult));
        }
    }

    @Override
    @Transactional
    public void executeBrushDescribe(BrushDescribe.Arg arg, String enterpriseId) {
        String objectApiName = arg.getDescribeApiName();
        try {
            Map<String, Object> describeJson = arg.getDescribeJson();
            IObjectDescribe describeDraft;
            boolean existObj = false;
            if (CollectionUtils.notEmpty(describeJson)) {
                describeDraft = new ObjectDescribe(describeJson);
                describeDraft.setTenantId(enterpriseId);
                if (StringUtils.isNotEmpty(objectApiName)) {
                    describeDraft.setApiName(objectApiName);
                }
            } else {
                describeDraft = describeLogicService.findObject(enterpriseId, objectApiName);
                existObj = true;
            }
            boolean isNotUdef = BooleanUtils.isNotTrue(describeDraft.isUdef()) && !enterpriseId.startsWith(PREFIX);
            if (existObj) {
                if (isNotUdef) {
                    return;
                }
                Map<String, Map<String, Object>> fields = arg.getFields();
                if (CollectionUtils.notEmpty(fields)) {
                    List<IFieldDescribe> fieldDescribeList = updateFieldByMap(fields, describeDraft);
                    if (fields.keySet().stream().anyMatch(fieldName -> fieldName.endsWith("__c"))) {
                        describeDraft.addFieldDescribeList(fieldDescribeList);
                        objectDescribeService.update(describeDraft);
                    } else {
                        objectDescribeService.updateOrInsertFieldsForOnline(enterpriseId, arg.getDescribeApiName(), fieldDescribeList);
                    }
                }
                Map<String, Object> describeAttribute = arg.getDescribeAttribute();
                if (CollectionUtils.notEmpty(describeAttribute)) {
                    describeAttribute.keySet().forEach(attr -> describeDraft.set(attr, describeAttribute.get(attr)));
                    objectDescribeService.updateOnlyDescribeForOnline(enterpriseId, describeDraft);
                }
            } else {
                IObjectDescribe created = describeLogicService.create(true, describeDraft);
                // 功能权限初始化
                functionPrivilegeService.initFunctionPrivilege(User.systemUser(enterpriseId), created);
            }
            // todo 先下掉
            /*if (enterpriseId.startsWith(PREFIX)) {
                NotifierClient.send("sys-describe-change-event", "Clean");
            }*/
            // 刷字段扩展属性
            processFieldExtraAttribute(enterpriseId, objectApiName, arg.getFieldExtraAttribute(), isNotUdef);
            // 对象附加描述属性保存
            // 预设对象，刷 icon_slot 需要 fs-webpage-iconSlot-preObject 中存在 objApi 和 icon_slot 的对应关系
            if (DEFINE_TYPE_PACKAGE.equals(describeDraft.getDefineType())) {
                // 描述中存在 icon_slot 值才刷
                Map<String, Object> describeExtraAttribute = arg.getDescribeExtraAttribute();
                if (MapUtils.isEmpty(describeExtraAttribute)) {
                    return;
                }
                if (Objects.isNull(describeExtraAttribute.get(ObjectDescribeExt.ICON_SLOT))) {
                    return;
                }

                // 如果为预设对象设置 icon 需要存在默认图标，在对象编辑恢复默认图标时使用
                int iconSlot = PreObjDefaultConfig.existIconSlotAndGet(objectApiName);
                if (iconSlot < 0) {
                    log.warn("not found default icon_slot for PreObj: 「{}」, you need config fs-webpage-iconSlot-preObject for your PreObj", objectApiName);
                    throw new ValidateException("not found default icon_slot for PreObj");
                }
                objExtraService.save(enterpriseId, describeDraft, describeExtraAttribute);
            }
        } catch (ObjectDefNotFoundError e) {
            log.warn("createOrUpdateDescribe ObjectDefNotFoundError, tenantId:{}, objectApiName:{}", enterpriseId, objectApiName, e);
            // 记录日志方便排查问题
            sendAuditLog(enterpriseId, arg.getDescribeApiName(), e.getMessage());
        } catch (Exception e) {
            log.warn("createOrUpdateDescribe error, tenantId:{}, objectApiName:{}", enterpriseId, objectApiName, e);
            // 记录日志方便排查问题
            sendAuditLog(enterpriseId, arg.getDescribeApiName(), e.getMessage());
            throw new MetaDataBusinessException("brush describe error");
        }
    }

    /**
     * 处理字段扩展属性刷库
     *
     * @param enterpriseId 企业ID
     * @param objectApiName 对象API名称
     * @param fieldExtraAttribute 字段扩展属性
     */
    private void processFieldExtraAttribute(String enterpriseId, String objectApiName, Map<String, Map<String, Object>> fieldExtraAttribute, boolean isNotUdef) {
        if (isNotUdef || CollectionUtils.empty(fieldExtraAttribute)) {
            log.info("processFieldExtraAttribute: skip, isNotUdef:{}, fieldExtraAttribute:{}", isNotUdef, fieldExtraAttribute);
            return;
        }
        User user = User.systemUser(enterpriseId);
        // 1. 先查询现有的字段扩展属性
        Map<String, List<IObjectFieldExtra>> existingFieldExtrasMap = describeLogicService.findDescribeExtra(user, Lists.newArrayList(objectApiName));
        List<IObjectFieldExtra> existingFieldExtras = existingFieldExtrasMap.getOrDefault(objectApiName, Lists.newArrayList());
        // 2. 准备合并后的字段扩展属性列表并添加所有新值
        Set<String> fields = fieldExtraAttribute.keySet();
        List<IObjectFieldExtra> updateList = Lists.newArrayList();
        fields.forEach(fieldApiName -> {
            Map<String, Object> fieldExtra = fieldExtraAttribute.get(fieldApiName);
            IObjectFieldExtra existing = findFieldExtra(existingFieldExtras, fieldApiName);
            if (Objects.nonNull(existing)) {
                // 如果存在，更新现有的扩展属性
                ObjectFieldExtra objectFieldExtra = (ObjectFieldExtra) existing;
                Map<String, Object> newFieldExtra = Maps.newHashMap(objectFieldExtra.getContainerDocument());
                newFieldExtra.putAll(fieldExtra);
                ObjectFieldExtra newExtra = new ObjectFieldExtra(newFieldExtra);
                updateList.add(newExtra);
            } else {
                // 如果不存在，创建新的扩展属性
                ObjectFieldExtra newExtra = new ObjectFieldExtra(fieldExtra);
                newExtra.setFieldApiName(fieldApiName);
                updateList.add(newExtra);
            }
        });
        // 3. 更新字段扩展属性
        if (CollectionUtils.notEmpty(updateList)) {
            describeLogicService.upsertObjectFieldExtra(user, objectApiName, updateList);
        }
    }

    private IObjectFieldExtra findFieldExtra(List<IObjectFieldExtra> extras, String fieldApiName) {
        if (CollectionUtils.empty(extras)) {
            return null;
        }
        return extras.stream()
                .filter(extra -> fieldApiName.equals(extra.getFieldApiName()))
                .findFirst()
                .orElse(null);
    }

    private List<IFieldDescribe> updateFieldByMap(Map<String, Map<String, Object>> fields, IObjectDescribe oldIObjectDescribe) {
        Map<String, IFieldDescribe> map = Maps.newHashMap();
        fields.forEach((fieldName, v) -> {
            IFieldDescribe newField = null;
            Set<Map.Entry<String, Object>> entrySet = v.entrySet();
            for (Map.Entry<String, Object> fieldAttribute : entrySet) {
                if (newField == null) {
                    newField = oldIObjectDescribe.getFieldDescribe(fieldName);
                    if (newField == null) {
                        return;
                    }
                    newField.set(fieldAttribute.getKey(), v.get(fieldAttribute.getKey()));
                } else {
                    newField.set(fieldAttribute.getKey(), v.get(fieldAttribute.getKey()));
                }
            }
            map.put(fieldName, oldIObjectDescribe.getFieldDescribe(fieldName));
        });
        fields.forEach((fieldName, v) -> {
            Set<Map.Entry<String, Object>> entrySet = v.entrySet();
            for (Map.Entry<String, Object> fieldAttribute : entrySet) {
                if (map.get(fieldName) == null) {
                    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fields.get(fieldName));
                    map.put(fieldName, fieldDescribe);
                    return;
                }
                map.get(fieldName).set(fieldAttribute.getKey(), v.get(fieldAttribute.getKey()));
            }
        });
        return Lists.newArrayList(map.values());
    }

    private void sendAuditLog(String enterpriseId, String describeApiName, String message) {
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(RuntimeUtils.getAppName())
                .serverIp(RuntimeUtils.getServerIp())
                .profile(RuntimeUtils.getProfile())
                .action("initTools")
                .tenantId(enterpriseId)
                .userId(User.SUPPER_ADMIN_USER_ID)
                .objectApiNames(describeApiName)
                .num(0)
                .message(message)
                .traceId(TraceContext.get().getTraceId())
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    @Override
    public void executeBrushLayout(BrushLayouts.Arg arg, String enterpriseId) {
        try {
            List<ILayout> layoutList = Lists.newArrayList();
            if (Objects.nonNull(arg.getDetailLayout())) {
                layoutList.add(new Layout(arg.getDetailLayout()));
            }
            if (Objects.nonNull(arg.getMobileLayout())) {
                layoutList.add(new Layout(arg.getMobileLayout()));
            }
            if (Objects.nonNull(arg.getListLayout())) {
                layoutList.add(new Layout(arg.getListLayout()));
            }
            if (Objects.nonNull(arg.getAddEditLayout())) {
                layoutList.add(new Layout(arg.getAddEditLayout()));
            }
            layoutLogicService.createOrUpdateLayoutForDevTools(enterpriseId, arg.getDescribeApiName(), layoutList);
            if (enterpriseId.startsWith(PREFIX)) {
                NotifierClient.send("sys-layout-change-event", "Clean");
            }
        } catch (Exception e) {
            log.warn("createOrUpdateLayoutForDevTools error", e);
            sendAuditLog(enterpriseId, arg.getDescribeApiName(), e.getMessage());
            throw new MetaDataBusinessException("brush layout error");
        }
    }

    @Override
    public void transferExecute(TransferBrush.Arg arg) {
        List<String> enterpriseIds = Lists.newArrayList();
        BrushScope brushScope = BrushScope.of(arg.getBrushScope());
        if (brushScope == BrushScope.SPECIFIC_ENTERPRISE) {
            enterpriseIds.addAll(arg.getEnterpriseIds());
        }
        Map<String, String> envTenantRouterMap = getEnvTenantRouterMap(arg.getEnvTenantRouter());
        List<String> routeTenantIds = Lists.newArrayList();
        if (brushScope == BrushScope.SYSTEM_LIBRARY) {
            enterpriseIds.addAll(arg.getEnterpriseIds());
            if (CollectionUtils.empty(arg.getEnvList())) {
                throw new ValidateException("envList is empty");
            }
            arg.getEnvList().forEach(env -> routeTenantIds.add(AppFrameworkConfig.getBrushToolsRouterTenantId(String.valueOf(env))));
            routeTenantIds.addAll(envTenantRouterMap.values());
        }
        if (brushScope == BrushScope.ALL_ENTERPRISE) {
            CollectionUtils.nullToEmpty(arg.getEnvList()).forEach(env -> {
                List<Integer> enterpriseList = getEnterpriseList(env, arg.getRunStatusList());
                List<String> tenantIds = enterpriseList.stream().map(String::valueOf).collect(Collectors.toList());
                enterpriseIds.addAll(tenantIds);
            });
            envTenantRouterMap.keySet().forEach(env -> {
                List<Integer> enterpriseList = getEnterpriseList(Integer.parseInt(env), arg.getRunStatusList());
                List<String> tenantIds = enterpriseList.stream().map(String::valueOf).collect(Collectors.toList());
                routeTenantIds.addAll(tenantIds);
            });
        }
        if (CollectionUtils.empty(enterpriseIds)) {
            return;
        }
        enterpriseIds.removeAll(CollectionUtils.nullToEmpty(arg.getBlackTenantIds()));
        String taskRecordType = arg.getTaskRecordType();
        String taskDataId = arg.getTaskDataId();
        if (!redisDao.exists(InitToolService.getKey(taskRecordType, taskDataId))) {
            redisDao.set(InitToolService.getKey(taskRecordType, taskDataId), "1", 60 * 60 * 24);
        }
        int totalSize;
        int size = enterpriseIds.size();
        if (CollectionUtils.empty(routeTenantIds)) {
            totalSize = size;
        } else {
            totalSize = routeTenantIds.size() * size;
        }
        redisDao.set(InitToolService.getKey(taskRecordType, taskDataId, TOTAL), String.valueOf(totalSize));
        if (size < 10) {
            enterpriseIds.forEach(enterpriseId -> execute(arg, enterpriseId, routeTenantIds));
        } else {
            enterpriseIds.forEach(enterpriseId -> {
                        if (!redisDao.exists(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId()))) {
                            redisDao.lpush(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), FAIL), enterpriseId);
                            return;
                        }
                        rateLimiter.acquire();
                        initToolsExecutorService.submit(MonitorTaskWrapper.wrap(() -> execute(arg, enterpriseId, routeTenantIds)));
                    }
            );
        }
    }

    private Map<String, String> getEnvTenantRouterMap(String envTenantRouter) {
        return SEMICOLON_SPLITTER.splitToList(StringUtils.defaultString(envTenantRouter)).stream()
                .map(str -> Tuple.of(StringUtils.substringBefore(str, ":"), StringUtils.substringAfter(str, ":")))
                .collect(Collectors.toMap(Tuple::getKey, Tuple::getValue));
    }

    @Override
    public List<HandlerDescribe> findHandlerDescribes(User user, String objectApiName, String interfaceCode) {
        List<HandlerRuntimeConfig> configs = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(user.getTenantId(),
                objectApiName, interfaceCode, true, true);
        if (CollectionUtils.empty(configs)) {
            return Lists.newArrayList();
        }
        List<String> handlerApiNames = configs.stream().map(HandlerRuntimeConfig::getHandlerApiName).distinct().collect(Collectors.toList());
        List<HandlerDefinition> definitions = handlerDefinitionService.findByApiNames(user.getTenantId(), handlerApiNames, false, true);
        if (CollectionUtils.empty(definitions)) {
            return Lists.newArrayList();
        }
        Map<String, HandlerDefinition> definitionMap = definitions.stream().collect(Collectors.toMap(HandlerDefinition::getApiName, x -> x, (a, b) -> a));
        return configs.stream()
                .filter(config -> definitionMap.containsKey(config.getHandlerApiName()))
                .map(config -> HandlerDescribe.of(definitionMap.get(config.getHandlerApiName()), config))
                .sorted(Comparator.comparingInt(HandlerDescribe::getDefaultOrder))
                .collect(Collectors.toList());
    }

    @Override
    public ValidateHandler.Result validateHandler(User user, List<HandlerDescribe> handlerDescribes, String objectApiName, String interfaceCode) {
        List<HandlerDefinition> definitions = Lists.newArrayList();
        List<HandlerRuntimeConfig> runtimeConfigs = Lists.newArrayList();
        List<HandlerDescribe> handlerDescribesInDB = findHandlerDescribes(user, objectApiName, interfaceCode);
        diffAndBuildHandlerDescribes(objectApiName, interfaceCode, handlerDescribesInDB, handlerDescribes, definitions, runtimeConfigs);
        return ValidateHandler.Result.builder()
                .definitions(definitions)
                .runtimeConfigs(runtimeConfigs)
                .build();
    }

    private static void diffAndBuildHandlerDescribes(String objectApiName, String interfaceCode, List<HandlerDescribe> handlerDescribesInDB, List<HandlerDescribe> handlerDescribes, List<HandlerDefinition> definitions, List<HandlerRuntimeConfig> runtimeConfigs) {
        Map<String, HandlerDescribe> handlerDescribeMapInDB = handlerDescribesInDB.stream()
                .collect(Collectors.toMap(HandlerDescribe::getApiName, x -> x, (a, b) -> a));
        Map<String, HandlerDescribe> handlerDescribeMap = handlerDescribes.stream()
                .collect(Collectors.toMap(HandlerDescribe::getApiName, x -> x, (a, b) -> a));
        MapDifference<String, HandlerDescribe> difference = Maps.difference(handlerDescribeMapInDB, handlerDescribeMap);
        Map<String, MapDifference.ValueDifference<HandlerDescribe>> differenceMap = difference.entriesDiffering();
        differenceMap.forEach((apiName, valueDifference) -> {
            HandlerDescribe handlerDescribe = valueDifference.rightValue();
            definitions.add(handlerDescribe.toDefinition(objectApiName, interfaceCode));
            runtimeConfigs.add(handlerDescribe.toConfig(objectApiName, interfaceCode));
        });
        Map<String, HandlerDescribe> upsertMap = difference.entriesOnlyOnRight();
        upsertMap.forEach((apiName, handlerDescribe) -> {
            definitions.add(handlerDescribe.toDefinition(objectApiName, interfaceCode));
            runtimeConfigs.add(handlerDescribe.toConfig(objectApiName, interfaceCode));
        });
        log.info("definitions:{}, runtimeConfigs:{}", JSON.toJSONString(definitions), JSON.toJSONString(runtimeConfigs));
//        return difference;
    }

    @Override
    public String upsertHandlerDefinitionAndRuntimeConfig(User user, List<HandlerDescribe> handlerDescribes, String objectApiName, String interfaceCode) {
        List<HandlerDefinition> definitions = Lists.newArrayList();
        List<HandlerRuntimeConfig> runtimeConfigs = Lists.newArrayList();
        List<HandlerDescribe> handlerDescribesInDB = findHandlerDescribes(user, objectApiName, interfaceCode);
        diffAndBuildHandlerDescribes(objectApiName, interfaceCode, handlerDescribesInDB, handlerDescribes, definitions, runtimeConfigs);
        handlerLogicService.batchUpsertHandlerDefinitionAndRuntimeConfig(user, definitions, runtimeConfigs);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("definitions", definitions);
        jsonObject.put("runtimeConfigs", runtimeConfigs);
        return jsonObject.toJSONString();
    }

    @Override
    public String transferExecute(BrushHandler.Arg arg) {
        long startTime = System.currentTimeMillis();
        List<String> enterpriseIds = Lists.newArrayList();
        Map<String, String> envTenantRouterMap = getEnvTenantRouterMap(arg.getEnvTenantRouter());
        List<String> routeTenantIds = Lists.newArrayList();
        enterpriseIds.addAll(arg.getEnterpriseIds());
        if (CollectionUtils.empty(arg.getEnvList())) {
            throw new ValidateException("envList is empty");
        }
        arg.getEnvList().forEach(env -> routeTenantIds.add(AppFrameworkConfig.getBrushToolsRouterTenantId(String.valueOf(env))));
        routeTenantIds.addAll(envTenantRouterMap.values());
        List<CompletableFuture<Map<String, List<String>>>> completableFutureList = Lists.newArrayList();
        routeTenantIds.forEach(routeTenantId -> completableFutureList.addAll(executeBrushHandler(arg, enterpriseIds, routeTenantId)));
        Map<String, String> routeTenantId2EnvLabelMap = AppFrameworkConfig.getRouteTenantId2EnvLabelMap();
        CompletableFuture<Map<String, List<String>>> future = sequenceMap(completableFutureList, (oldList, newList) -> {
            List<String> mergedList = Lists.newArrayList(oldList);
            mergedList.addAll(newList);
            return mergedList;
        });
        log.info("before future join cost:{}, completableFutureList size:{}", System.currentTimeMillis() - startTime, completableFutureList.size());
        future.join();
        StringBuilder stringBuilder = new StringBuilder();
        future.whenComplete((map, throwable) -> {
            if (Objects.nonNull(throwable)) {
                log.warn("transferExecute error", throwable);
                return;
            }
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                String routerTenantId = entry.getKey();
                stringBuilder.append(routeTenantId2EnvLabelMap.get(routerTenantId));
                List<String> tenantIds = entry.getValue();
                // TODO: 2024/9/20 I18n 
                String tenantIdsStr = tenantIds.stream()
                        .map(tenantId -> "-101".equals(tenantId) ? "灰度" : "全网") // ignoreI18n
                        .collect(Collectors.joining(","));
                stringBuilder.append("（").append(tenantIdsStr).append("）");
            }
        });
        log.info("transferExecute cost:{}", System.currentTimeMillis() - startTime);
        String message = stringBuilder.toString();
        if (StringUtils.isEmpty(message)) {
            return "所选环境刷库成功"; // ignoreI18n
        }
        return StringUtils.prependIfMissing(message, "失败环境："); // ignoreI18n
    }

    private List<CompletableFuture<Map<String, List<String>>>> executeBrushHandler(BrushHandler.Arg arg, List<String> enterpriseIds, String routeTenantId) {
        List<CompletableFuture<Map<String, List<String>>>> completableFutureList = Lists.newArrayList();
        enterpriseIds.forEach(tenantId -> {
            CompletableFuture<Map<String, List<String>>> completableFuture = CompletableFuture.supplyAsync(wrap(() -> {
                Map<String, List<String>> resultMap = Maps.newHashMap();
                InitTools.Arg initToolsArg = BrushHandler.Arg.builder()
                        .handlerDescribes(arg.getHandlerDescribes())
                        .interfaceCode(arg.getInterfaceCode())
                        .describeApiName(arg.getDescribeApiName())
                        .build();
                boolean result = executeBrushHandler(initToolsArg, routeTenantId, tenantId);
                if (!result) {
                    resultMap.put(routeTenantId, Lists.newArrayList(tenantId));
                }
                return resultMap;
            }), initToolsExecutorService);
            completableFutureList.add(completableFuture);
        });
        return completableFutureList;
    }

    private boolean executeBrushHandler(InitTools.Arg initToolsArg, String routeTenantId, String tenantId) {
        Map<String, String> headers = RestUtils.buildHeaders(User.systemUser(routeTenantId));
        initToolsArg.setTenantId(tenantId);
        InitTools.Result brushResult;
        try {
            brushResult = brushToolsTransferProxy.executeBrush("upsertHandlerDefinitionAndRuntimeConfig", initToolsArg, headers);
        } catch (Exception e) {
            log.warn("upsertHandlerDefinitionAndRuntimeConfig error, tenantId:{}, objectApiName:{}", tenantId, initToolsArg.getDescribeApiName(), e);
            return false;
        }
        log.info("executeBrushHandler result:{}", brushResult);
        return Objects.nonNull(brushResult) && (brushResult.getCode() == 0 || brushResult.getCode() == 200);
    }


    /**
     * 将List<CompletableFuture<Map<K, V>>> 转为 CompletableFuture<Map<K, V>>
     *
     * @Param mergeFunction 自定义key冲突时的merge策略
     */
    public static <K, V> CompletableFuture<Map<K, V>> sequenceMap(
            Collection<CompletableFuture<Map<K, V>>> completableFutures, BinaryOperator<V> mergeFunction) {
        return CompletableFuture
                .allOf(completableFutures.toArray(new CompletableFuture<?>[0]))
                .thenApply(v -> completableFutures.stream().map(CompletableFuture::join)
                        .flatMap(map -> map.entrySet().stream())
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, mergeFunction)));
    }

    public static <T> Supplier<T> wrap(Supplier<T> supplier) {
        TraceContext context = TraceContext.get().copyNecessaries();
        return () -> {
            context.setCurrentThreadName(Thread.currentThread().getName());
            TraceContext._set(context);
            mdcPutData(context);
            T var2;
            try {
                var2 = supplier.get();
            } finally {
                TraceContext.remove();
            }
            return var2;
        };
    }

    private static void mdcPutData(TraceContext context) {
        MDC.put("colorized", context.isColor() ? "1" : "0");
        MDC.put("traceId", context.getTraceId());
        MDC.put("userId", context.getUid());
        MDC.put("ei", context.getEi());
        MDC.put("ea", context.getEa());
        MDC.put("employeeId", context.getEmployeeId());
        MDC.put("sourceProcessId", context.getSourceProcessId());
        MDC.put("restHeader", context.getRestHeader());
    }
}
