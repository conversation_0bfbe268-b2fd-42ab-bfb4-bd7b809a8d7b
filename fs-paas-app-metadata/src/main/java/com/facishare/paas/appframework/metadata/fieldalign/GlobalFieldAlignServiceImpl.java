package com.facishare.paas.appframework.metadata.fieldalign;

import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.LanguageClientUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.i18n.client.api.Language;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/5/13
 */
@Service("fieldAlignService")
public class GlobalFieldAlignServiceImpl implements GlobalFieldAlignService {

    public static final String GLOBAL_LAYOUT_CONF_KEY = "pass_app_global_layout_conf";

    @Autowired
    private ConfigService configService;

    @Autowired
    private MessagePollingService messagePollingService;
    @Autowired
    LicenseService licenseService;

    private final static String DEFAULT = "default";

    @Override
    public GlobalFieldAlign getGlobalFieldAlign(String tenantId) {
        String jsonStr = configService.findTenantConfig(User.systemUser(tenantId), GLOBAL_LAYOUT_CONF_KEY);
        GlobalFieldAlign fieldAlign = JacksonUtils.fromJson(jsonStr, GlobalFieldAlign.class);

        if (Objects.isNull(fieldAlign)) {
            fieldAlign = GlobalFieldAlign.of("", "", false, Lists.newArrayList());
        }

        if (!licenseService.isSupportMultiLanguage(tenantId)) {
            return fieldAlign;
        }
        fieldAlign.setFieldAlignByLangList(fillLangFieldAlign(tenantId, fieldAlign.getFieldAlignByLangList()));

        return fieldAlign;
    }


    private List<GlobalFieldAlign.FieldLangAlign> fillLangFieldAlign(String tenantId, List<GlobalFieldAlign.FieldLangAlign> fieldAlignByLang) {
        List<Language> languages = LanguageClientUtil.getLanguages(tenantId);
        if (CollectionUtils.empty(languages)) {
            return fieldAlignByLang;
        }
        Map<String, Map<String, String>> langFieldAlginConfigMap = AppFrameworkConfig.getLangFieldAlginConfigMap();

        Map<String, GlobalFieldAlign.FieldLangAlign> fieldLangAlignMap = fieldAlignByLang.stream()
                .collect(Collectors.toMap(GlobalFieldAlign.FieldLangAlign::getLang, it -> it, (x1, x2) -> x1));
        for (Language language : languages) {
            GlobalFieldAlign.FieldLangAlign fieldLangAlign = fieldLangAlignMap.get(language.getCode());
            if (Objects.nonNull(fieldLangAlign)) {
                continue;
            }
            Map<String, String> langFieldConfig = langFieldAlginConfigMap.get(language.getCode());
            GlobalFieldAlign.FieldLangAlign fieldLangAlignByConfig = null;
            if (Objects.isNull(langFieldConfig)) {
                Map<String, String> defaultConfig = langFieldAlginConfigMap.get(DEFAULT);
                fieldLangAlignByConfig = GlobalFieldAlign.FieldLangAlign.of(defaultConfig);
            } else {
                fieldLangAlignByConfig = GlobalFieldAlign.FieldLangAlign.of(langFieldConfig);
            }

            if (Objects.nonNull(fieldLangAlignByConfig)) {
                fieldAlignByLang.add(fieldLangAlignByConfig);
            }
        }
        return fieldAlignByLang;
    }


    @Override
    public void updateGlobalFieldAlign(String tenantId, GlobalFieldAlign fieldAlign) {
        String jsonStr = JacksonUtils.toJson(fieldAlign);
        configService.upsertTenantConfig(User.systemUser(tenantId), GLOBAL_LAYOUT_CONF_KEY, jsonStr, ConfigValueType.JSON);
        GlobalFieldAlign globalFieldAlign = getGlobalFieldAlign(tenantId);
        if (!Objects.equals(globalFieldAlign, fieldAlign)) {
            messagePollingService.notifyDescribeLayoutChange(tenantId, false);
        }
    }

    @Override
    public GlobalFieldAlign.FieldAlign getFieldAlignByLayout(String tenantId, ILayout layout) {
        return Optional.ofNullable(layout)
                .map(LayoutExt::of)
                .map(this::getFieldAlignByLayout)
                .orElseGet(() -> getGlobalFieldAlign(tenantId).getFieldAlign());
    }

    private GlobalFieldAlign.FieldAlign getFieldAlignByLayout(LayoutExt layoutExt) {
        String fieldAlign = LayoutStructure.getFieldAlign(layoutExt);
        return GlobalFieldAlign.FieldAlign.of(fieldAlign);
    }

    @Override
    public void handleLayoutWithGlobalFieldAlign(String tenantId, IObjectDescribe describe, ILayout layout, Lang lang) {
        if (!isGrayFieldAlign(tenantId, describe.getApiName())) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);

        GlobalFieldAlign globalFieldAlign = getGlobalFieldAlign(tenantId);
        GlobalFieldAlign.FieldAlign fieldAlign = Optional.ofNullable(getFieldAlignByLayout(layoutExt))
                .orElse(globalFieldAlign.getFieldAlignByLang(lang));
        layoutExt.set(LayoutStructure.FIELD_ALIGN, fieldAlign.getType());
        layoutExt.set(LayoutStructure.DETAIL_FORM_LAYOUT, globalFieldAlign.getDetailFormLayout());
    }

    @Override
    public boolean isGrayFieldAlign(String tenantId, String describeApiName) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_ALIGN, tenantId);
    }
}
