package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Maps;

import java.util.List;

import static com.facishare.paas.appframework.metadata.layout.LayoutContext.isEnableMobileLayout;

public class MobileLayoutProcessorImpl extends AbstractLayoutProcessor {
    /**
     * 构造函数
     *
     * @param pageType        页面类型
     * @param webLayout       Web布局配置
     * @param describeExt     对象描述扩展
     * @param objectData      对象数据
     * @param componentConfig 组件配置列表
     */
    protected MobileLayoutProcessorImpl(PageType pageType, LayoutExt webLayout, ObjectDescribeExt describeExt, IObjectData objectData, List<IComponent> componentConfig) {
        super(pageType, webLayout, describeExt, objectData, componentConfig);
    }

    @Override
    protected LayoutExt extractLayout() {
        LayoutExt targetLayout;
        if (webLayout.isEnableMobileLayout()) {
            targetLayout = LayoutExt.of(webLayout.getMobileLayout());
            //从web端布局同步相关列表配置
            LayoutExt.fillNameComponent(targetLayout, webLayout, targetLayout.getComponentsSilently());
            syncRelatedListInfo(targetLayout);
            orderComponentsInNavigation(targetLayout);
        } else {
            targetLayout = LayoutExt.of(Maps.newHashMap());
            targetLayout.setButtons(webLayout.getButtonOrder());
            targetLayout.setHiddenButtons(webLayout.getHiddenButtons());
            targetLayout.setHiddenComponents(webLayout.getHiddenComponents());
            targetLayout.setComponents(getComponents());
            targetLayout.setLayoutStructure(getLayoutStructure(targetLayout));
        }
        return targetLayout;
    }

    @Override
    protected void createLayoutResetFromComponentToFirst(LayoutExt targetLayout) {
        if (webLayout.isEditLayout()) {
            EditLayout.of(targetLayout).placeFormComponentToFirst();
        }
    }

    @Override
    protected void mergeFormComponentToTargetLayout(LayoutExt targetLayout) {
        if (webLayout.isEditLayout()) {
            EditLayout.of(targetLayout.getLayout()).mergeFormComponents(webLayout);
            EditLayout.of(targetLayout.getLayout()).mergeDetailComponents(describeExt.getTenantId(), describeExt.getApiName(), webLayout);
        }
    }

    @Override
    protected boolean isEnableTargetLayout() {
        return webLayout.isEnableMobileLayout();
    }

}
