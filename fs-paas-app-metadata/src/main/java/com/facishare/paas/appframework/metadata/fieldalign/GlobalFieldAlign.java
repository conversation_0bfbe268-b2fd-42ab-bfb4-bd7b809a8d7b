package com.facishare.paas.appframework.metadata.fieldalign;

import com.facishare.paas.appframework.core.util.Lang;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Lists;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.solr.common.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2022/5/13
 */
@Slf4j
@EqualsAndHashCode
@ToString
public class GlobalFieldAlign {
    @JsonProperty("field_align")
    private FieldAlign fieldAlign;
    @JsonProperty("detail_form_layout")
    private DetailFormLayout detailFormLayout;
    @JsonProperty("field_align_follow_lang")
    private Boolean fieldAlignFollowLang;
    @Setter
    @JsonProperty("field_align_by_lang")
    private List<FieldLangAlign> fieldAlignByLangList;

    private GlobalFieldAlign(FieldAlign fieldAlign, DetailFormLayout detailFormLayout) {
        this.fieldAlign = fieldAlign;
        this.detailFormLayout = detailFormLayout;
    }

    private GlobalFieldAlign(FieldAlign fieldAlign, DetailFormLayout detailFormLayout,
                             Boolean fieldAlignFollowLang, List<FieldLangAlign> fieldAlignByLang) {
        this.fieldAlign = fieldAlign;
        this.detailFormLayout = detailFormLayout;
        this.fieldAlignFollowLang = fieldAlignFollowLang;
        this.fieldAlignByLangList = fieldAlignByLang;
    }

    @JsonCreator
    public static GlobalFieldAlign of(@JsonProperty("field_align") String fieldAlign,
                                      @JsonProperty("detail_form_layout") String detailFormLayout,
                                      @JsonProperty("field_align_follow_lang") Boolean fieldAlignFollowLang,
                                      @JsonProperty("field_align_by_lang") List<FieldLangAlign> fieldAlignByLang) {
        return new GlobalFieldAlign(FieldAlign.of(fieldAlign), DetailFormLayout.of(detailFormLayout),
                fieldAlignFollowLang, fieldAlignByLang);
    }

    public static GlobalFieldAlign of(String fieldAlign, String detailFormLayout) {
        return new GlobalFieldAlign(FieldAlign.of(fieldAlign), DetailFormLayout.of(detailFormLayout));
    }

    /**
     * 校验是否和法
     * <p>
     * fieldAlign 为空,认为不合法
     *
     * @return false 不合法发
     */
    public boolean validateFieldAlign() {
        return Objects.nonNull(fieldAlign);
    }

    /**
     * 获取字段对齐方式,为空时获取默认的对齐方式--左对齐
     *
     * @return 返回 fieldAlign, 为空时返回 {@link FieldAlign.LEFT}
     */
    public FieldAlign getFieldAlign() {
        return Optional.ofNullable(fieldAlign).orElse(FieldAlign.LEFT);
    }

    public FieldAlign getFieldAlignByLang(Lang lang) {
        if (BooleanUtils.isNotTrue(getFieldAlignFollowLang())) {
            return getFieldAlign();
        }
        Map<String, GlobalFieldAlign.FieldLangAlign> fieldAlignMap = getFieldAlignByLangList().stream()
                .collect(Collectors.toMap(GlobalFieldAlign.FieldLangAlign::getLang, it -> it, (x1, x2) -> x1));

        GlobalFieldAlign.FieldLangAlign fieldLangAlign = fieldAlignMap
                .get(Optional.ofNullable(lang).map(Lang::getValue).orElse(Lang.zh_CN.getValue()));
        if (Objects.isNull(fieldLangAlign)) {
            return getFieldAlign();
        }
        return fieldLangAlign.getFieldAlign();
    }

    public String getDetailFormLayout() {
        return Optional.ofNullable(detailFormLayout).map(DetailFormLayout::getType).orElse(null);
    }

    public Boolean getFieldAlignFollowLang() {
        return Optional.ofNullable(fieldAlignFollowLang).orElse(false);
    }

    public List<FieldLangAlign> getFieldAlignByLangList() {
        return Optional.ofNullable(fieldAlignByLangList).orElse(Lists.newArrayList());
    }

    @EqualsAndHashCode
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FieldLangAlign {
        @JsonProperty("lang")
        private String lang;
        @JsonProperty("field_align")
        private FieldAlign fieldAlign;

        public FieldAlign getFieldAlign() {
            return Optional.ofNullable(fieldAlign).orElse(FieldAlign.LEFT);
        }

        public String getLang() {
            return Optional.ofNullable(lang).orElse(Lang.zh_CN.getValue());
        }

        public static FieldLangAlign of(Map<String, String> fieldAlignMap) {
            if (Objects.isNull(fieldAlignMap.get("lang")) || Objects.isNull(fieldAlignMap.get("field_align"))) {
                log.warn("fieldAlignMap does not contain lang or field align");
                return null;
            }
            return new FieldLangAlign(fieldAlignMap.get("lang"), FieldAlign.of(fieldAlignMap.get("field_align")));
        }

        public static FieldLangAlign of(String lang, FieldAlign fieldAlign) {
            if (StringUtils.isEmpty(lang) || Objects.isNull(fieldAlign)) {
                log.warn("fieldAlignMap does not contain lang or field align");
                return null;
            }
            return new FieldLangAlign(lang, fieldAlign);
        }
    }

    public enum FieldAlign {
        LEFT("left"),
        CENTER("center"),
        TOP_AND_BOTTOM("top_and_bottom"),
        WHOLE_LEFT("whole_left"),
        ;
        private final String type;

        FieldAlign(String type) {
            this.type = type;
        }

        private static final Map<String, FieldAlign> FIELD_ALIGN_MAP;

        static {
            FIELD_ALIGN_MAP = Stream.of(values()).collect(Collectors.toMap(FieldAlign::getType, Function.identity()));
        }

        @JsonCreator
        public static FieldAlign of(String type) {
            return FIELD_ALIGN_MAP.get(type);
        }

        @JsonValue
        public String getType() {
            return type;
        }
    }

    public enum DetailFormLayout {
        RESPONSIVE("responsive"),
        STATIC("static"),
        ;
        private final String type;

        DetailFormLayout(String type) {
            this.type = type;
        }

        private static final Map<String, DetailFormLayout> map;

        static {
            map = Stream.of(values()).collect(Collectors.toMap(DetailFormLayout::getType, Function.identity()));
        }

        @JsonCreator
        public static DetailFormLayout of(String type) {
            return map.get(type);
        }

        @JsonValue
        public String getType() {
            return type;
        }
    }
}
