package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupFieldComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.NavigationComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ComponentExt.NAME_COMPONENT;
import static com.facishare.paas.appframework.metadata.layout.LayoutStructure.LAYOUT;

/**
 * 布局处理器抽象基类
 * <p>
 * 使用模板方法模式定义布局处理的通用流程，子类实现具体的布局创建逻辑。
 * 提取了MobileLayoutBuilder和SidebarLayoutBuilder的公共逻辑，提高代码复用性。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
public abstract class AbstractLayoutProcessor {
    //web端不急
    protected LayoutExt webLayout;
    //需要处理的目标布局
    private LayoutExt targetLayout;
    //页面类型
    private PageType pageType;
    //描述
    protected ObjectDescribeExt describeExt;
    //数据
    private IObjectData objectData;

    private List<IComponent> componentConfig;

    private static final List<String> LAYOUT_STRUCTURE_PROPS_TO_SYNC = Lists.newArrayList("show_tag");

    private static final List<String> targets = Collections.unmodifiableList(Lists.newArrayList(ComponentExt.NAVIGATION_COMPONENT_NAME, ComponentExt.FORM_COMPONENT));


    /**
     * 构造函数
     *
     * @param pageType        页面类型
     * @param webLayout       Web布局配置
     * @param describeExt     对象描述扩展
     * @param objectData      对象数据
     * @param componentConfig 组件配置列表
     */
    protected AbstractLayoutProcessor(PageType pageType,
                                      LayoutExt webLayout,
                                      ObjectDescribeExt describeExt,
                                      IObjectData objectData,
                                      List<IComponent> componentConfig) {
        this.pageType = pageType;
        this.webLayout = webLayout;
        this.describeExt = describeExt;
        this.objectData = objectData;
        this.componentConfig = componentConfig;
    }

    // ==================== 模板方法 ====================

    /**
     * 处理布局的模板方法
     * <p>
     * 定义了布局处理的标准流程：
     * 1. 参数验证
     * 2. 创建布局
     * 3. 后处理
     *
     * @return 处理后的布局对象
     */
    public final LayoutExt processLayout() {
        StopWatch stopWatch = StopWatch.create("AbstractLayoutProcessor.processLayout");

        // 1. 参数验证
        before();
        stopWatch.lap("before");
        // 2. 转换布局
        targetLayout = convertLayout();
        stopWatch.lap("convertLayout");
        // 3. 后处理
        postProcessLayout(targetLayout);
        stopWatch.lap("postProcessLayout");

        return targetLayout;
    }

    private LayoutExt convertLayout() {
        //提取布局
        targetLayout = extractLayout();

        //将web端布局的form组件合并到mobile布局中(新建编辑页)
        mergeFormComponentToTargetLayout(targetLayout);

        //重置详细信息和摘要信息的字段
        resetFieldsInComponents(targetLayout);

        //处理三流组件以及签到、收款组件
        processExtraComponents(targetLayout);

        //新建编辑页布局将form_component放第一位
        createLayoutResetFromComponentToFirst(targetLayout);

        //从web端布局同步layout_structure中的属性
        syncLayoutStructure(targetLayout);


        return targetLayout;
    }

    protected abstract LayoutExt extractLayout();

    protected abstract void createLayoutResetFromComponentToFirst(LayoutExt targetLayout);


    private void syncLayoutStructure(LayoutExt mobileLayout) {
        if (!webLayout.isV3Layout()) {
            return;
        }
        Map<String, Object> webLayoutStructure = webLayout.getLayoutStructure();
        LAYOUT_STRUCTURE_PROPS_TO_SYNC.stream().filter(webLayoutStructure::containsKey).forEach(prop -> mobileLayout.getLayoutStructure().put(prop, webLayoutStructure.get(prop)));

        //同步插件信息
        LayoutStructure.syncLayoutPlugins(webLayout, mobileLayout);
        //同步新建编辑页标题名称
        LayoutStructure.syncTitleNameWithMobile(webLayout, mobileLayout);

        // 关于字段显示信息：没启用移动端布局，则配置全部取web端的
        if (!isEnableTargetLayout()) {
            LayoutStructure.setFieldAlign(mobileLayout, LayoutStructure.getFieldAlign(webLayout));
            LayoutStructure.setIsTitleHelpText(mobileLayout, LayoutStructure.getIsTitleHelpText(webLayout));
        } else {
            //启用的话，则取移动端的
            LayoutStructure.setIsOpenTileHelpText(mobileLayout, LayoutStructure.getIsOpenTileHelpText(mobileLayout));
            if (!LayoutStructure.getIsOpenTileHelpText(mobileLayout)) {
                LayoutStructure.setIsTitleHelpText(mobileLayout, LayoutStructure.getIsTitleHelpText(webLayout));
            }
        }
    }

    private void processExtraComponents(LayoutExt targetLayout) {
        if (webLayout.isDetailLayout()) {
            //外露按钮配置
            processFlowComponents(describeExt.getTenantId(), targetLayout, describeExt);
            processGroupFieldComponents(targetLayout);
            WebDetailLayout.of(targetLayout).processSummaryInfo(describeExt.getTenantId(), webLayout);
        }
    }

    private void processGroupFieldComponents(LayoutExt mobileLayout) {
        if (pageType == PageType.Designer) {
            return;
        }
        List<IComponent> groupFieldComponents = getGroupFieldComponents(mobileLayout);
        WebDetailLayout.of(mobileLayout).addComponentsUpTargets(groupFieldComponents, targets);
    }

    private List<IComponent> getGroupFieldComponents(LayoutExt mobileLayout) {
        List<IButton> simpleButtons = TopInfoComponentBuilder.builder().describeExt(describeExt).layoutExt(mobileLayout).objectData(objectData).build().getSimpleComponentButtons();
        if (CollectionUtils.empty(simpleButtons)) {
            return Lists.newArrayList();
        }
        return simpleButtons.stream().map(x -> convertButtonToComponent(x)).collect(Collectors.toList());
    }


    private IComponent convertButtonToComponent(IButton button) {
        GroupFieldComponent component = new GroupFieldComponent();
        component.setName(button.getAction() + "_group_field");
        component.setHeader(button.getLabel());
        component.setAction(button.getAction());
        return component;
    }


    public static void processFlowComponents(String tenantId, LayoutExt mobileLayout, IObjectDescribe objectDescribe) {
        if (objectDescribe.isBigObject()) {
            return;
        }
        //补充三流组件
        boolean unDeletable = !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_FLOW_COMPONENTS_IN_WEB_LAYOUT, tenantId);
        List<IComponent> flowComponents = LayoutComponents.getFlowComponents(unDeletable);
        mobileLayout.removeHiddenComponents(flowComponents);
        WebDetailLayout.of(mobileLayout).addComponentsUpTargets(flowComponents, targets);
    }

    protected abstract void mergeFormComponentToTargetLayout(LayoutExt targetLayout);

    protected abstract boolean isEnableTargetLayout();

    protected void before() {
        //如果是新版布局，则需要将web布局中的组件进行排序
        if (webLayout.isNewLayout()) {
            LayoutComponents.restoreComponentOrder(webLayout);
        }
    }

    private void resetFieldsInComponents(LayoutExt mobileLayout) {
        if (pageType == PageType.Designer) {
            //设计器不需要详细信息和摘要信息的字段
            mobileLayout.getNewTopInfoComponent().ifPresent(x -> x.setFieldSections(Lists.newArrayList()));
            mobileLayout.getFormComponents().forEach(x -> x.setFieldSections(Lists.newArrayList()));
        } else {
            //详情页需要从Web端布局取详细信息和摘要信息的字段信息
            mobileLayout.getNewTopInfoComponent().ifPresent(x -> {
                if (!ComponentExt.of(x).isDepend()) {
                    webLayout.getNewTopInfoComponent().ifPresent(y -> x.setFieldSections(y.getFieldSections()));
                }
            });
            mobileLayout.getFormComponents().forEach(x -> webLayout.getComponentByApiName(x.getName()).map(y -> (IFormComponent) y).ifPresent(y -> x.setFieldSections(y.getFieldSections())));

            //从web端布局的表格组件中同步字段，因为移动端不支持表格组件
            LayoutExt.of(webLayout).syncFieldsFromFormTable(mobileLayout);
        }
    }


    protected List<IComponent> getComponents() {
        //去掉不支持的组件
        List<String> flowComponentApiNames = LayoutComponents.getFlowComponentApiNames();
        List<IComponent> components = webLayout.getComponentsSilently().stream()
                .filter(x -> !ComponentExt.HEAD_INFO_COMPONENT_NAME.equals(x.getName()))
                .filter(x -> !ComponentExt.of(x).isTabs())
                .filter(x -> !ComponentExt.of(x).isCustomComponent())
                .filter(x -> !ComponentExt.of(x).isContactRelation())
                .filter(x -> !ComponentExt.of(x).isGrid())
                .filter(x -> !ComponentExt.of(x).isTextComponent())
                .filter(x -> !ComponentExt.ACCOUNT_OPERATION_MAP_NAME.equals(x.getName()))
                .filter(x -> !ComponentExt.of(x).isFormTable())
                .filter(x -> !AppFrameworkConfig.isWebToMobileLayoutFilterComponent(x.getName()))
                .filter(x -> {
                    if (ComponentExt.of(x).isAiInsightComponent()) {
                        return StringUtils.isNotEmpty(ComponentExt.of(x).getVersion());
                    }
                    return !AppFrameworkConfig.isWebToMobileLayoutFilterComponentType(x.getType());
                })
                .filter(x -> !webLayout.isComponentHidden(x.getName()))
                .filter(x -> !flowComponentApiNames.contains(x.getName()))
                .collect(Collectors.toList());

        if (webLayout.isDetailLayout()) {
            //补充摘要信息和摘要卡片
            if (!webLayout.isNewLayout()) {
                LayoutComponents.addTopInfoComponentByLayout(webLayout, components, Lists.newArrayList(), describeExt);
                LayoutComponents.addSummaryCardComponent(webLayout.getRefObjectApiName(), components, Lists.newArrayList(), webLayout);
            }
            if (!webLayout.getHiddenComponents().contains(NAME_COMPONENT)
                    && components.stream().map(IComponent::getName).noneMatch(NAME_COMPONENT::equals)) {
                components.add(LayoutComponents.buildNameComponent(webLayout));
            }
            //补充其他页签
            ITabComponent otherTab = getOtherTabComponent(components);
            if (CollectionUtils.notEmpty(otherTab.getComponents())) {
                components.add(otherTab);
            }
            //组件排序
            components = ComponentOrder.order(components, webLayout, componentConfig);
            //补充页签导航
            components.add(getNavigationComponent(components, otherTab));
            handleRelatedDataComponent(components);
        }

        if (webLayout.isEditLayout()) {
            //组件排序
            components = ComponentOrder.order(components, webLayout, componentConfig);
            //补充快捷定位组件
            components.add(0, LayoutComponents.buildShortcutComponent());
            //补充head_info组件
            if (pageType == PageType.Designer) {
                components.add(getHeadInfo());
            }
        }

        return components;
    }

    protected void dealLayout() {
        //从web端布局同步相关列表配置
        LayoutExt.fillNameComponent(targetLayout, webLayout, targetLayout.getComponentsSilently());
        syncRelatedListInfo(targetLayout);
        orderComponentsInNavigation(targetLayout);
    }

    protected Map<String, Object> getLayoutStructure(LayoutExt mobileLayout) {
        Map<String, Object> layoutStructure = Maps.newLinkedHashMap();
        Map<String, Object> row1 = Maps.newLinkedHashMap();
        row1.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));

        List<String> components = Lists.newArrayList();
        if (webLayout.isDetailLayout()) {
            //主属性组件
            mobileLayout.getNameComponent().ifPresent(x -> components.add(x.getName()));
            //摘要信息
            mobileLayout.getNewTopInfoComponent().ifPresent(x -> components.add(x.getName()));
            //摘要卡片
            mobileLayout.getSummaryCardComponent().ifPresent(x -> components.add(x.getName()));
            //相关团队
            mobileLayout.getTeamComponent().ifPresent(x -> components.add(x.getName()));
            //产品属性
            mobileLayout.getComponentByApiName(ComponentExt.PRODUCT_ATTRIBUTE_COMPONENT_NAME).ifPresent(x -> components.add(x.getName()));
            //关键信息组件
            components.addAll(mobileLayout.getSummaryInfoComponentApiNames());
            //配置文件中指定保留在layout_structure中的组件
            List<String> componentApiNames = CollectionUtils.nullToEmpty(AppFrameworkConfig.getWebToMobileLayoutReserveInStructureComponents());
            List<String> componentTypes = CollectionUtils.nullToEmpty(AppFrameworkConfig.getWebToMobileLayoutReserveInStructureComponentTypes());
            List<String> configApiNames = mobileLayout.getComponentsSilently().stream()
                    .filter(it -> componentApiNames.contains(it.getName()) || componentTypes.contains(it.getType()))
                    .map(IComponent::getName)
                    .filter(it -> !components.contains(it))
                    .collect(Collectors.toList());
            components.addAll(configApiNames);
            //页签导航
            components.add(ComponentExt.NAVIGATION_COMPONENT_NAME);
        }

        if (webLayout.isEditLayout()) {
            components.addAll(mobileLayout.getComponentsSilently().stream().map(x -> x.getName()).collect(Collectors.toList()));
        }

        List<List<String>> componentsList = Lists.newArrayList();
        componentsList.add(components);
        row1.put(LayoutStructure.COMPONENTS, componentsList);

        layoutStructure.put(LAYOUT, Lists.newArrayList(row1));

        return layoutStructure;
    }

    private IComponent getHeadInfo() {
        IComponent headInfo = LayoutComponents.buildHeadInfoComponent();
        List<ListComponentInfo> buttonInfo = EditLayout.of(webLayout).getButtonInfo();
        buttonInfo.forEach(x -> x.filterMobileHiddenButtons());
        ComponentExt.of(headInfo).setButtonInfo(buttonInfo);
        return headInfo;
    }


    private INavigationComponent getNavigationComponent(List<IComponent> components, ITabComponent otherTab) {
        Set<String> otherTabComponents = Sets.newHashSet(CollectionUtils.nullToEmpty(otherTab.getComponents()));
        INavigationComponent navigation = new NavigationComponent();
        navigation.setName(ComponentExt.NAVIGATION_COMPONENT_NAME);
        navigation.setHeader("navigation");
        List<String> childComponents = components.stream()
                .filter(x -> !otherTabComponents.contains(x.getName()))
                .filter(x -> !ComponentExt.of(x).isIFrameComponent())
                .filter(x -> !ComponentExt.of(x).isChartComponent())
                .filter(x -> !ComponentExt.of(x).isTeamComponent())
                .filter(x -> !ComponentExt.TOP_INFO_COMPONENT_NAME.equals(x.getName()))
                .filter(x -> !ComponentExt.SUMMARY_CARD_COMPONENT_NAME.equals(x.getName()))
                .filter(x -> !ComponentExt.PRODUCT_ATTRIBUTE_COMPONENT_NAME.equals(x.getName()))
                .filter(x -> !ComponentExt.of(x).isSummaryInfoComponent())
                .filter(x -> !ComponentExt.of(x).isNameComponent())
                .filter(x -> !AppFrameworkConfig.getWebToMobileLayoutReserveInStructureComponents().contains(x.getName()))
                .filter(x -> !AppFrameworkConfig.getWebToMobileLayoutReserveInStructureComponentTypes().contains(x.getType()))
                .map(x -> x.getName())
                .collect(Collectors.toList());
        navigation.setComponents(childComponents);
        return navigation;
    }

    private ITabComponent getOtherTabComponent(List<IComponent> components) {
        ITabComponent otherTab = new TabComponent();
        otherTab.setName("tab_other");
        String labelKey = String.format(I18NKey.CHART_GROUP, webLayout.getTenantId(), webLayout.getRefObjectApiName());
        otherTab.setHeader(I18NExt.getOrDefault(labelKey, I18NExt.getOrDefault(I18NKey.OTHER, "其他")));// ignoreI18n
        otherTab.set("nameI18nKey", I18NKey.OTHER);
        //配置文件中指定保留在layout_structure中的组件
        List<String> componentApiNames = CollectionUtils.nullToEmpty(AppFrameworkConfig.getWebToMobileLayoutReserveInStructureComponents());
        List<String> componentTypes = CollectionUtils.nullToEmpty(AppFrameworkConfig.getWebToMobileLayoutReserveInStructureComponentTypes());
        List<IComponent> childComponents = components.stream()
                .filter(x -> ComponentExt.of(x).isIFrameComponent()
                        || ComponentExt.of(x).isChartComponent()
                        || ComponentExt.of(x).isBiDashboard())
                .filter(it -> !componentTypes.contains(it.getType()))
                .filter(it -> !componentApiNames.contains(it.getName()))
                .collect(Collectors.toList());
        Collections.sort(childComponents, Comparator.comparing(x -> x.getOrder()));
        otherTab.setComponents(childComponents.stream().map(x -> x.getName()).collect(Collectors.toList()));
        return otherTab;
    }

    /**
     * （白名单）同步Web端相关列表组件配置到移动端
     * 情况一（白名单）：场景配置、按钮配置、指定字段数据汇总
     * 1. 相关列表组件 or 主从列表组件
     * 2. Web端组件(wc)开启独立配置（component containsKey scene_info）
     * 3. 移动端组件(mc)未开启独立配置（component containsKey scene_info）
     * 情况二：指定字段数据汇总
     * 1. 相关列表组件 or 主从列表组件
     * 2. Web端组件(wc)开启独立配置（component containsKey scene_info）
     * 3. 移动端组件(mc)开启独立配置（component containsKey scene_info） 且 移动端配置显示(沿用Web配置)指定数据汇总
     *
     * @param mobileLayout
     */
    protected void syncRelatedListInfo(LayoutExt mobileLayout) {
        mobileLayout.getComponentsSilently().stream()
                .filter(mc -> ComponentExt.of(mc).isRelatedList() || ComponentExt.of(mc).isMasterDetailComponent())//组件类型正确
//                .filter(mc -> !ComponentExt.of(mc).enableRelatedListConfig())//移动端组件未开启独立配置 **此步骤移到循环内**
                .forEach(mc -> {
                    //根据移动端组件API获取Web端组件
                    Optional<IComponent> wcOpt = LayoutExt.of(webLayout).getComponentByApiName(mc.getName());
                    wcOpt.filter(wc -> ComponentExt.of(wc).enableRelatedListConfig()).ifPresent(//Web端组件开启独立配置
                            wc -> ComponentExt.of(mc).syncComponentInfo(Lists.newArrayList(wc), (mcAsLeft, wcAsRight) -> {
                                ComponentExt mcExt = ComponentExt.of(mcAsLeft);
                                if (!mcExt.enableRelatedListConfig()) {//移动端组件开启未独立配置
                                    handleRelatedDataComponent(Lists.newArrayList(wcAsRight));
                                    mcAsLeft.set(ListComponentExt.SCENE_INFO, wcAsRight.get(ListComponentExt.SCENE_INFO));
                                    mcAsLeft.set(ListComponentExt.BUTTON_INFO, wcAsRight.get(ListComponentExt.BUTTON_INFO));
                                    mcAsLeft.set(ListComponentExt.ALL_PAGE_SUMMARY_INFO, wcAsRight.get(ListComponentExt.ALL_PAGE_SUMMARY_INFO));
                                } else if (mcExt.getDisplayTotalsFromMobile()) {//移动端组件开启独立配置 且 移动端配置显示(沿用Web配置)指定字段汇总
                                    mcAsLeft.set(ListComponentExt.ALL_PAGE_SUMMARY_INFO, wcAsRight.get(ListComponentExt.ALL_PAGE_SUMMARY_INFO));
                                }
                            }));
                });
    }

    protected void orderComponentsInNavigation(LayoutExt mobileLayout) {
        if (CollectionUtils.empty(componentConfig)) {
            return;
        }
        mobileLayout.getNavigationComponent().ifPresent(navigation -> {
            List<String> children = navigation.getComponents();
            List<IComponent> components = mobileLayout.getComponentByApiNames(children);
            mobileLayout.setComponentOrder(components, componentConfig);
            Map<String, Integer> componentOrder = components.stream().collect(Collectors.toMap(x -> x.getName(), x -> x.getOrder()));
            Collections.sort(children, Comparator.comparing(x -> componentOrder.getOrDefault(x, ComponentExt.DEFAULT_ORDER)));
            navigation.setComponents(children);
        });
    }

    /**
     * 详情页相关列表页组件,web端布局转移动端,列表通用按钮的外漏按钮个数最多为1
     *
     * @param components
     */
    private void handleRelatedDataComponent(List<IComponent> components) {
        if (CollectionUtils.empty(components)) {
            return;
        }
        components.stream()
                .filter(it -> ComponentExt.of(it).isRelatedList())
                .forEach(component -> {
                    ListComponentExt.of(component).getButtonInfo().stream()
                            .filter(buttonInfo -> ListComponentInfo.ButtonRenderType.getTypeByUsePage(ButtonUsePageType.ListNormal).equals(buttonInfo.getRenderType()))
                            .findFirst()
                            .ifPresent(buttonInfo -> {
                                if (Objects.nonNull(buttonInfo.getExposedButton())) {
                                    buttonInfo.setExposedButton(1);
                                }
                            });

                });
    }

    /**
     * 布局后处理
     *
     * @param layout 需要后处理的布局对象
     */
    protected void postProcessLayout(LayoutExt layout) {
    }


}