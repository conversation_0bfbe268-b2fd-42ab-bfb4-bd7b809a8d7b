//package com.facishare.paas.appframework.log
//
//import com.facishare.paas.I18N
//import com.facishare.paas.I18NCSVResource
//import com.facishare.paas.I18NClientResource
//import com.facishare.paas.appframework.common.service.ConnectionServiceProxy
//import com.facishare.paas.appframework.common.service.OrgService
//import com.facishare.paas.appframework.common.service.UserRoleInformationService
//import com.facishare.paas.appframework.config.OptionalFeaturesService
//import com.facishare.paas.appframework.core.i18n.I18NKey
//import com.facishare.paas.appframework.core.model.User
//import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
//import com.facishare.paas.appframework.log.dto.*
//import com.facishare.paas.appframework.log.metadatahandle.FillFieldInfoHandle
//import com.facishare.paas.appframework.metadata.LayoutExt
//import com.facishare.paas.appframework.metadata.TeamMemberRoleService
//import com.facishare.paas.metadata.api.IUdefFunction
//import com.facishare.paas.metadata.api.describe.IObjectDescribe
//import com.facishare.paas.metadata.api.service.IObjectDescribeService
//import com.facishare.paas.metadata.impl.ObjectData
//import com.facishare.paas.metadata.impl.describe.ObjectDescribe
//import com.facishare.paas.metadata.service.impl.UdefFunctionService
//import com.facishare.paas.metadata.ui.layout.ILayout
//import com.facishare.rest.core.exception.RestProxyInvokeException
//import com.google.common.collect.Maps
//import org.powermock.reflect.Whitebox
//import spock.lang.Specification
//
//class LogServiceImplTest extends Specification {
//    LogServiceImpl logService
//    OrgService orgService
//    AsyncLogSender asyncLogSender
//    LogServiceProxy logServiceProxy
//    IObjectDescribeService objectDescribeService
//    UdefFunctionService udefFunctionService
//    ModifyRecordInitManager modifyRecordInitManager
//    FillFieldInfoHandle fillFieldInfoHandle
//    ConnectionServiceProxy connectionServiceProxy
//    OptionalFeaturesService optionalFeaturesService
//    TeamMemberRoleService teamMemberRoleService
//    UserRoleInformationService userRoleInformationService
//
//    // Mock I18N resources
//    def mockI18NClientResource = Mock(I18NClientResource)
//    def mockI18NCSVResource = Mock(I18NCSVResource)
//    def mockI18nResources = [mockI18NClientResource, mockI18NCSVResource]
//
//    void setup() {
//        logService = new LogServiceImpl()
//        orgService = Mock(OrgService)
//        asyncLogSender = Mock(AsyncLogSender)
//        logServiceProxy = Mock(LogServiceProxy)
//        objectDescribeService = Mock(IObjectDescribeService)
//        udefFunctionService = Mock(UdefFunctionService)
//        modifyRecordInitManager = Mock(ModifyRecordInitManager)
//        fillFieldInfoHandle = Mock(FillFieldInfoHandle)
//        connectionServiceProxy = Mock(ConnectionServiceProxy)
//        optionalFeaturesService = Mock(OptionalFeaturesService)
//        teamMemberRoleService = Mock(TeamMemberRoleService)
//        userRoleInformationService = Mock(UserRoleInformationService)
//
//        logService.orgService = orgService
//        logService.asyncLogSender = asyncLogSender
//        logService.logServiceProxy = logServiceProxy
//        logService.objectDescribeService = objectDescribeService
//        logService.udefFunctionService = udefFunctionService
//        logService.modifyRecordInitManager = modifyRecordInitManager
//        logService.fillFieldInfoHandle = fillFieldInfoHandle
//        logService.connectionServiceProxy = connectionServiceProxy
//        logService.optionalFeaturesService = optionalFeaturesService
//        logService.teamMemberRoleService = teamMemberRoleService
//        logService.userRoleInformationService = userRoleInformationService
//
//        // Mock ModifyRecordInitManager to handle LogInfo to ModifyRecord conversion
//        def oldDataModelProvider = Mock(OldDataModelModifyRecordProvider)
//        modifyRecordInitManager.getProvider("OldDataModel") >> oldDataModelProvider
//        oldDataModelProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            def record = new ModifyRecord()
//            record.setLogID(info.getLogId())
//            record.setOperationTime(info.getOperationTime())
//            record.setOperationType(info.getBizOperationName())
//            record.setLogInfo(info)
//            return record
//        }
//
//        Whitebox.setInternalState(I18N.class, "I18N_RESOURCES", mockI18nResources)
//    }
//
//    def cleanup() {
//        // Reset I18N MetaClass
//        I18N.metaClass = null
//    }
//
//    /**
//     * GenerateByAI
//     * Test basic log method with text message
//     */
//    def "testBasicLogTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def textMessage = "Test message"
//        def describe = Mock(IObjectDescribe)
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        describe.getApiName() >> objectApiName
//        objectDescribeService.findDescribeListByApiNames(_, _) >> [describe]
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectApiName, textMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.textMessage == textMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test log method with international text message
//     */
//    def "testLogInternationalTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def textMessage = "Test message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def describe = Mock(IObjectDescribe)
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        describe.getApiName() >> objectApiName
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logInternational(user, eventType, actionType, objectApiName, textMessage, internationalMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.textMessage == textMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test log method with international message and module
//     */
//    def "testLogInternationalWithModuleTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def module = "CustomModule"
//        def textMessage = "Test message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .internationalParameters(["param1", "param2"])
//                .build()
//        def describe = Mock(IObjectDescribe)
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        describe.getApiName() >> objectApiName
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logInternational(user, eventType, actionType, objectApiName, module, textMessage, internationalMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.module == module
//            assert info.textMessage == textMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot.textMsg[0].text == textMessage
//            assert info.snapshot.textMsg[0].internationalText == internationalMessage
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test log method with international message and data ID
//     */
//    def "testLogInternationalWithDataIdTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def module = "CustomModule"
//        def dataId = "TEST-001"
//        def textMessage = "Test message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def describe = Mock(IObjectDescribe)
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        describe.getApiName() >> objectApiName
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logInternational(user, eventType, actionType, describe, module, dataId, textMessage, internationalMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.module == module
//            assert info.objectId == dataId
//            assert info.textMessage == textMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test log method with object data
//     */
//    def "testLogWithObjectDataTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def describe = Mock(IObjectDescribe)
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "testId")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", "TestObject")
//        def data = new ObjectData(containerDocument)
//
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        describe.getApiName() >> "TestObject"
//        describe.getDisplayName() >> "Test Object"
//        describe.getFieldDescribes() >> []
//        describe.copy() >> describe
//
//        objectDescribeService.findDescribeListByApiNames(_, _) >> [describe]
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, describe, data)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == "TestObject"
//            assert info.objectId == "testId"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//        }
//    }
//
//    /**
//     * Test log method with international custom message and empty data list
//     */
//    def "testLogWithInternationalCustomMessageEmptyDataListTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectDescribe = Mock(IObjectDescribe)
//
//        // 创建实际的ObjectData对象而不是Mock
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", "describe_api_name")
//        def data = new ObjectData(containerDocument)
//
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        objectDescribe.getApiName() >> "TestObject"
//        objectDescribe.getDisplayName() >> "Test Object"
//        data.getId() >> "TEST-001"
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithInternationalCustomMessage(user, eventType, actionType, objectDescribe, data, customMessage, internationalMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.objectId == "TEST-001"
//            true
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test log method with master-detail relationship
//     */
//    def "testMasterDetailLogTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe using Map
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe using Map
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : [:],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create master data
//        def masterContainerDocument = Maps.newHashMap()
//        masterContainerDocument.put("_id", "MASTER-001")
//        masterContainerDocument.put("name", "Master Data")
//        masterContainerDocument.put("describe_api_name", masterObjectApiName)
//        def masterData = new ObjectData(masterContainerDocument)
//
//        // Create detail data
//        def detailContainerDocument = Maps.newHashMap()
//        detailContainerDocument.put("_id", "DETAIL-001")
//        detailContainerDocument.put("name", "Detail Data")
//        detailContainerDocument.put("describe_api_name", detailObjectApiName)
//        detailContainerDocument.put("masterId", "MASTER-001")
//        def detailData = new ObjectData(detailContainerDocument)
//
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//        def dataList = [masterData, detailData]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//        user.getUserIdOrOutUserIdIfOutUser() >> "testUser"
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        // Mock objectDescribeService behavior
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, masterObjectApiName) >> masterDescribe
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, detailObjectApiName) >> detailDescribe
//        objectDescribeService.findDescribeListByApiNames(_, _) >> [masterDescribe, detailDescribe]
//
//        when:
//        logService.masterDetailLog(user, eventType, actionType, objectDescribes, dataList)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId == "MASTER-001"
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId == "DETAIL-001"
//                assert info.masterId == "MASTER-001"
//            }
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test logUdefFunction method
//     */
//    def "testLogUdefFunctionTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def apiName = "testFunction"
//        def function = Mock(IUdefFunction)
//        def describe = Mock(IObjectDescribe)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//        user.getUserIdOrOutUserIdIfOutUser() >> "testUser"
//
//        function.getFunctionName() >> "Test Function"
//        describe.getDisplayName() >> "Test Object Display"
//        describe.getApiName() >> objectApiName
//
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        udefFunctionService.findFunctionByApiName(_, _, _) >> function
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logUdefFunction(user, eventType, actionType, objectApiName, apiName, function)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.module == "UserDefineFunc"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test logTemporaryRights method
//     */
//    def "testLogTemporaryRightsTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def jsonStr = '{"key": "value", "testField": "testValue"}'
//        def describe = Mock(IObjectDescribe)
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        describe.getApiName() >> objectApiName
//        describe.getDisplayName() >> "Test Object"
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logTemporaryRights(user, eventType, actionType, objectApiName, jsonStr)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.module == "44"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//            assert info.snapshot.snapshot.key == "value"
//            assert info.snapshot.snapshot.testField == "testValue"
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test logDataPermission method
//     */
//    def "testLogDataPermissionTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def snapshots = [
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage("Test message", null, objectApiName)])
//                        .build()
//        ]
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//        user.getUserIdOrOutUserIdIfOutUser() >> "testUser"
//
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logDataPermission(user, eventType, actionType, snapshots)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.module == "44"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * GenerateByAI
//     * Test deleteLog method
//     */
//    def "testDeleteLogTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new DeleteLog.Arg()
//        arg.module = "DataPrivilegeManagement"
//        arg.objectIds = ["id1", "id2"]
//        arg.deleteOnlyEs = true
//        arg.operationTimeFrom = 1000L
//        arg.operationTimeTo = 2000L
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//        user.getUserIdOrOutUserIdIfOutUser() >> "testUser"
//
//        when:
//        logService.deleteLog(user, arg)
//
//        then:
//        1 * logServiceProxy.deleteLog(_, _) >> { header, condition ->
//            assert condition.corpId == "testTenant"
//            assert condition.get("module") == ["DataPrivilegeManagement"]
//            assert condition.objectIds == ["id1", "id2"]
//            assert condition.deleteOnlyEs
//            assert condition.get("operationTimeFrom") == 1000L
//            assert condition.get("operationTimeTo") == 2000L
//        }
//    }
//
//    /**
//     * Test getLoginLog method
//     */
//    def "testGetLoginLogTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with custom login condition
//     */
//    def "testGetLoginLogWithCustomConditionTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        def loginCondition = LoginLog.LoginInfoCondition.builder()
//                .userIds(["user1", "user2"])
//                .deptIds(["dept1", "dept2"])
//                .ownerIds(["owner1"])
//                .operationTimeFrom(1000L)
//                .operationTimeTo(2000L)
//                .build()
//        arg.loginCondition = loginCondition
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            assert condition.userIds == ["user1", "user2"]
//            assert condition.deptIds == ["dept1", "dept2"]
//            assert condition.ownerIds == ["owner1"]
//            assert condition.operationTimeFrom == 1000L
//            assert condition.operationTimeTo == 2000L
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with null loginCondition
//     */
//    def "testGetLoginLogWithNullConditionTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        arg.loginCondition = null
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method when logServiceProxy throws exception
//     */
//    def "testGetLoginLogWithExceptionTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(0)
//                .totalCount(0)
//                .hasMore(null)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//        logServiceProxy.getLoginLog(_, _) >> { throw new RuntimeException("Test exception") }
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with empty login condition
//     */
//    def "testGetLoginLogWithEmptyConditionTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with invalid page size
//     */
//    def "testGetLoginLogWithInvalidPageSizeTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = -1  // 无效的页大小
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)  // 应该使用默认页大小
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10  // 验证使用了默认页大小
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with complex login condition
//     */
//    def "testGetLoginLogWithComplexConditionTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        def loginCondition = LoginLog.LoginInfoCondition.builder()
//                .operationTimeFrom(2000L)  // 开始时间大于结束时间
//                .operationTimeTo(1000L)
//                .build()
//        arg.loginCondition = loginCondition
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            assert condition.operationTimeFrom == 2000L
//            assert condition.operationTimeTo == 1000L
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with invalid time range
//     */
//    def "testGetLoginLogWithInvalidTimeRangeTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//        arg.isPrePage = true
//        arg.isChangePageSize = false
//        def loginCondition = LoginLog.LoginInfoCondition.builder()
//                .operationTimeFrom(2000L)  // 开始时间大于结束时间
//                .operationTimeTo(1000L)
//                .build()
//        arg.loginCondition = loginCondition
//        def expectedResult = LoginLog.Result.builder()
//                .results([])
//                .pageSize(10)
//                .totalCount(0)
//                .hasMore(false)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, arg)
//
//        then:
//        1 * logServiceProxy.getLoginLog(_, _) >> { header, condition ->
//            assert condition.tenantId == "testTenant"
//            assert condition.id == "testId"
//            assert condition.pageSize == 10
//            assert condition.isPrePage
//            assert !condition.isChangePageSize
//            assert condition.operationTimeFrom == 2000L
//            assert condition.operationTimeTo == 1000L
//            expectedResult
//        }
//        result == expectedResult
//    }
//
//    /**
//     * Test getLoginLog method with null arg
//     */
//    def "testGetLoginLogWithNullArgTest"() {
//        given:
//        def user = Mock(User)
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLoginLog(user, null)
//
//        then:
//        thrown(NullPointerException.class)
//    }
//
//    /**
//     * Test getLoginLog method with null user
//     */
//    def "testGetLoginLogWithNullUserTest"() {
//        given:
//        def arg = new LoginLog.Arg()
//        arg.id = "testId"
//        arg.pageSize = 10
//
//        when:
//        def result = logService.getLoginLog(null, arg)
//
//        then:
//        thrown(NullPointerException.class)
//    }
//
//    /**
//     * Test logDataPermission method with empty snapshots list
//     */
//    def "testLogDataPermissionWithEmptySnapshotsTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def snapshots = []
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        when:
//        logService.logDataPermission(user, eventType, actionType, snapshots)
//
//        then:
//        0 * asyncLogSender.offer(_)
//        noExceptionThrown()
//    }
//
//    /**
//     * Test logDataPermission method with multiple snapshots
//     */
//    def "testLogDataPermissionWithMultipleSnapshotsTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def snapshots = [
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage("Test message 1", null, "TestObject1")])
//                        .build(),
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage("Test message 2", null, "TestObject2")])
//                        .build()
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//        user.getUserIdOrOutUserIdIfOutUser() >> "testUser"
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logDataPermission(user, eventType, actionType, snapshots)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.module == "44"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//            true
//        }
//    }
//
//    /**
//     * Test logDataPermission method with out user
//     */
//    def "testLogDataPermissionWithOutUserTest"() {
//        given:
//        def outUser = Mock(User)
//        def realUser = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def snapshots = [
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage("Test message", null, "TestObject")])
//                        .build()
//        ]
//
//        outUser.getTenantId() >> "testTenant"
//        outUser.getUserId() >> "outUser"
//        outUser.getUserName() >> null
//        outUser.isOutUser() >> true
//        outUser.getOutTenantId() >> "outTenantId"
//        outUser.getUserIdOrOutUserIdIfOutUser() >> "outUser"
//
//        realUser.getTenantId() >> "testTenant"
//        realUser.getUserId() >> "realUser"
//        realUser.getUserName() >> "Real User"
//        realUser.isOutUser() >> false
//        realUser.getOutTenantId() >> null
//        realUser.getUserIdOrOutUserIdIfOutUser() >> "realUser"
//
//        orgService.getUser(_, _) >> realUser
//        orgService.fillUserName(outUser) >> realUser
//
//        when:
//        logService.logDataPermission(outUser, eventType, actionType, snapshots)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "realUser"
//            assert info.userName == "Real User"
//            assert info.outTenantId == "outTenantId"
//            assert info.module == "44"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//            true
//        }
//    }
//
//    /**
//     * Test logDataPermission method with invalid snapshot message
//     */
//    def "testLogDataPermissionWithInvalidSnapshotMessageTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def snapshots = [
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage(null, null, objectApiName)])  // Empty message but valid objectApiName
//                        .build()
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//        user.getUserIdOrOutUserIdIfOutUser() >> "testUser"
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logDataPermission(user, eventType, actionType, snapshots)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.module == "44"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//            true
//        }
//        noExceptionThrown()
//    }
//
//    /**
//     * Test deleteLog method with multiple conditions
//     */
//    def "testDeleteLogWithMultipleConditionsTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new DeleteLog.Arg()
//        arg.module = "DataPrivilegeManagement"
//        arg.objectIds = ["id1", "id2"]
//        arg.deleteOnlyEs = true
//        arg.operationTimeFrom = 1000L
//        arg.operationTimeTo = 2000L
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        logService.deleteLog(user, arg)
//
//        then:
//        1 * logServiceProxy.deleteLog(_, _) >> { header, condition ->
//            assert condition.corpId == "testTenant"
//            assert condition.get("module") == ["DataPrivilegeManagement"]
//            assert condition.objectIds == ["id1", "id2"]
//            assert condition.deleteOnlyEs
//            assert condition.get("operationTimeFrom") == 1000L
//            assert condition.get("operationTimeTo") == 2000L
//        }
//    }
//
//    /**
//     * Test deleteLog method with empty objectIds
//     */
//    def "testDeleteLogWithEmptyObjectIdsTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new DeleteLog.Arg()
//        arg.module = "DataPrivilegeManagement"
//        arg.objectIds = []
//        arg.deleteOnlyEs = true
//        arg.operationTimeFrom = 1000L
//        arg.operationTimeTo = 2000L
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        logService.deleteLog(user, arg)
//
//        then:
//        1 * logServiceProxy.deleteLog(_, _) >> { header, condition ->
//            assert condition.corpId == "testTenant"
//            assert condition.get("module") == ["DataPrivilegeManagement"]
//            assert condition.objectIds == []
//            assert condition.deleteOnlyEs
//            assert condition.get("operationTimeFrom") == 1000L
//            assert condition.get("operationTimeTo") == 2000L
//        }
//    }
//
//    /**
//     * Test deleteLog method with null module
//     */
//    def "testDeleteLogWithNullModuleTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new DeleteLog.Arg()
//        arg.module = null
//        arg.objectIds = ["id1"]
//        arg.deleteOnlyEs = true
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        logService.deleteLog(user, arg)
//
//        then:
//        1 * logServiceProxy.deleteLog(_, _) >> { header, condition ->
//            assert condition.corpId == "testTenant"
//            assert condition.get("module") == [null]
//            assert condition.objectIds == ["id1"]
//            assert condition.deleteOnlyEs
//        }
//    }
//
//    /**
//     * Test deleteLog method when logServiceProxy throws exception
//     */
//    def "testDeleteLogWithExceptionTest"() {
//        given:
//        def user = Mock(User)
//        def arg = new DeleteLog.Arg()
//        arg.module = "DataPrivilegeManagement"
//        arg.objectIds = ["id1"]
//        arg.deleteOnlyEs = true
//
//        user.getTenantId() >> "testTenant"
//        logServiceProxy.deleteLog(_, _) >> { throw new RuntimeException("Test exception") }
//
//        when:
//        logService.deleteLog(user, arg)
//
//        then:
//        noExceptionThrown()
//    }
//
//    /**
//     * Test logDataWithInternationalCustomMessage method
//     */
//    def "testLogDataWithInternationalCustomMessageTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        objectDescribeService.findByTenantIdAndDescribeApiName(_, _) >> describe
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logDataWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * Test getLogById method
//     */
//    def "testGetLogByIdTest"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "LOG-001"
//        def expectedLogInfo = LogInfo.builder()
//                .logId(logId)
//                .corpId("testTenant")
//                .objectName(apiName)
//                .build()
//
//        user.getTenantId() >> "testTenant"
//
//        when:
//        def result = logService.getLogById(apiName, logId, user)
//
//        then:
//        1 * logServiceProxy.modifyLog(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("logId") == logId
//            assert arg.getCondition().get("module") == apiName
//            assert arg.getCondition().get("corpId") == "testTenant"
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([expectedLogInfo])
//            searchResult
//        }
//        result == expectedLogInfo
//    }
//
//    /**
//     * Test getLogByModule method
//     */
//    def "testGetLogByModuleTest"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 10
//        def pageNumber = 1
//        def expectedLogInfo = LogInfo.builder()
//                .corpId("testTenant")
//                .module(module)
//                .bizOperationName(ActionType.Modify.getId())
//                .build()
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            ModifyRecord modifyRecord = new ModifyRecord()
//            modifyRecord.setLogInfo(info)
//            return modifyRecord
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == module
//            assert arg.getCondition().get("pageSize") == pageSize
//            assert arg.getCondition().get("pageNum") == pageNumber
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([expectedLogInfo])
//            searchResult.setTotalCount(1)
//            searchResult.setPageSize(pageSize)
//            searchResult.setPage(pageNumber)
//            searchResult
//        }
//        result.getPageInfo().getPageSize() == pageSize
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getLogInfos().size() == 1
//        (result.getLogInfos()[0] as ModifyRecord).getLogInfo() == expectedLogInfo
//    }
//
//    /**
//     * Test getLogByModule method with empty results
//     */
//    def "testGetLogByModuleEmptyResultsTest"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 10
//        def pageNumber = 1
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.logInfoToModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            return info
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == module
//            assert arg.getCondition().get("pageSize") == pageSize
//            assert arg.getCondition().get("pageNum") == pageNumber
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([])
//            searchResult.setTotalCount(0)
//            searchResult.setPageSize(pageSize)
//            searchResult.setPage(pageNumber)
//            searchResult
//        }
//        result.getPageInfo().getPageSize() == pageSize
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getLogInfos().isEmpty()
//    }
//
//    /**
//     * Test getLogByModule method with invalid page size
//     */
//    def "testGetLogByModuleWithInvalidPageSizeTest"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = -1  // 无效的页大小
//        def pageNumber = 1
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            ModifyRecord modifyRecord = new ModifyRecord()
//            modifyRecord.setLogInfo(info)
//            return modifyRecord
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == module
//            assert arg.getCondition().get("pageSize") == pageSize  // 应该使用默认页大小
//            assert arg.getCondition().get("pageNum") == pageNumber
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([])
//            searchResult.setTotalCount(0)
//            searchResult.setPageSize(10)
//            searchResult.setPage(pageNumber)
//            searchResult
//        }
//        result.getPageInfo().getPageSize() == 10
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getLogInfos().isEmpty()
//    }
//
//    /**
//     * Test getLogByModule method with invalid page number
//     */
//    def "testGetLogByModuleWithInvalidPageNumberTest"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 10
//        def pageNumber = 0  // 无效的页码
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            ModifyRecord modifyRecord = new ModifyRecord()
//            modifyRecord.setLogInfo(info)
//            return modifyRecord
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == module
//            assert arg.getCondition().get("pageSize") == pageSize
//            assert arg.getCondition().get("pageNum") == pageNumber  // 应该使用默认页码
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([])
//            searchResult.setTotalCount(0)
//            searchResult.setPageSize(pageSize)
//            searchResult.setPage(1)
//            searchResult
//        }
//        result.getPageInfo().getPageSize() == pageSize
//        result.getPageInfo().getPageNumber() == 1
//        result.getLogInfos().isEmpty()
//    }
//
//    /**
//     * Test getLogByModule method with null module
//     */
//    def "testGetLogByModuleWithNullModuleTest"() {
//        given:
//        def user = Mock(User)
//        def module = null
//        def pageSize = 10
//        def pageNumber = 1
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            ModifyRecord modifyRecord = new ModifyRecord()
//            modifyRecord.setLogInfo(info)
//            return modifyRecord
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == null
//            assert arg.getCondition().get("pageSize") == pageSize
//            assert arg.getCondition().get("pageNum") == pageNumber
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([])
//            searchResult.setTotalCount(0)
//            searchResult.setPageSize(pageSize)
//            searchResult.setPage(pageNumber)
//            searchResult
//        }
//        result.getPageInfo().getPageSize() == pageSize
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getLogInfos().isEmpty()
//    }
//
//    /**
//     * Test getLogByModule method when logServiceProxy throws exception
//     */
//    def "testGetLogByModuleWithExceptionTest"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 10
//        def pageNumber = 1
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            ModifyRecord modifyRecord = new ModifyRecord()
//            modifyRecord.setLogInfo(info)
//            return modifyRecord
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == module
//            assert arg.getCondition().get("pageSize") == pageSize
//            assert arg.getCondition().get("pageNum") == pageNumber
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults([])
//            searchResult.setTotalCount(0)
//            searchResult.setPageSize(pageSize)
//            searchResult.setPage(pageNumber)
//            searchResult
//        }
//        result != null
//        result.getLogInfos() != null
//        result.getLogInfos().isEmpty()
//        result.getPageInfo() != null
//        result.getPageInfo().getPageSize() == pageSize
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getTotalCount() == 0
//        noExceptionThrown()
//    }
//
//    /**
//     * Test getLogByModule method with multiple results
//     */
//    def "testGetLogByModuleWithMultipleResultsTest"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 10
//        def pageNumber = 1
//        def expectedLogInfos = [
//                LogInfo.builder()
//                        .corpId("testTenant")
//                        .module(module)
//                        .bizOperationName(ActionType.Modify.getId())
//                        .logId("LOG-001")
//                        .build(),
//                LogInfo.builder()
//                        .corpId("testTenant")
//                        .module(module)
//                        .bizOperationName(ActionType.Add.getId())
//                        .logId("LOG-002")
//                        .build()
//        ]
//        def mockProvider = Mock(OldDataModelModifyRecordProvider)
//
//        user.getTenantId() >> "testTenant"
//        modifyRecordInitManager.getProvider("OldDataModel") >> mockProvider
//        mockProvider.getModifyRecord(_ as LogInfo) >> { LogInfo info ->
//            ModifyRecord modifyRecord = new ModifyRecord()
//            modifyRecord.setLogInfo(info)
//            return modifyRecord
//        }
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        1 * logServiceProxy.webSearch(_, _) >> { header, arg ->
//            assert header.get("x-fs-ei") == "testTenant"
//            assert arg.getCondition().get("module") == module
//            assert arg.getCondition().get("pageSize") == pageSize
//            assert arg.getCondition().get("pageNum") == pageNumber
//            assert arg.getCondition().get("corpId") == "testTenant"
//            assert arg.getCondition().get("hidden") == false
//            def searchResult = new SearchModel.Result()
//            searchResult.setResults(expectedLogInfos)
//            searchResult.setTotalCount(2)
//            searchResult.setPageSize(pageSize)
//            searchResult.setPage(pageNumber)
//            searchResult
//        }
//        result.getPageInfo().getPageSize() == pageSize
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getLogInfos().size() == 2
//        result.getLogInfos().collect { it.getLogInfo().getLogId() } == ["LOG-001", "LOG-002"]
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataList method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : "TestObject",
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: "TestObject"]),
//                new ObjectData([_id: "TEST-002", name: "Test Data 2", describe_api_name: "TestObject"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == "TestObject"
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.objectId in ["TEST-001", "TEST-002"]
//            true
//        }
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataListAndModule method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListAndModuleTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def module = "CustomModule"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : module,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: module]),
//                new ObjectData([_id: "TEST-002", name: "Test Data 2", describe_api_name: module])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == module
//            assert info.module == module
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.objectId in ["TEST-001", "TEST-002"]
//            true
//        }
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataListAndModuleAndDataId method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListAndModuleAndDataIdTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def module = "CustomModule"
//        def dataId = "TEST-001"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : module,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: "TestObject"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == module
//            assert info.module == module
//            assert info.objectId == dataId
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            true
//        }
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribe method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def module = "CustomModule"
//        def dataId = "TEST-001"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : module,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: "TestObject"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == module
//            assert info.module == module
//            assert info.objectId == dataId
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            true
//        }
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeAndOptionalFeatures method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeAndOptionalFeaturesTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def module = "CustomModule"
//        def dataId = "TEST-001"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : module,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: "TestObject"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == module
//            assert info.module == module
//            assert info.objectId == dataId
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//            true
//        }
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeAndOptionalFeaturesDisabled method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeAndOptionalFeaturesDisabledTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def module = "CustomModule"
//        def dataId = "TEST-001"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: false)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : module,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: "TestObject"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(user, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        0 * asyncLogSender.offer(_)  // 当isModifyRecordEnabled为false时，不应该调用offer方法
//    }
//
//    /**
//     * Test logWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeAndOutUser method
//     */
//    def "testLogWithInternationalCustomMessageAndDataListAndModuleAndDataIdAndDescribeAndOutUserTest"() {
//        given:
//        def outUser = Mock(User)
//        def realUser = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def module = "CustomModule"
//        def dataId = "TEST-001"
//        def customMessage = "Custom message"
//        def internationalMessage = InternationalItem.builder()
//                .internationalKey("test.key")
//                .defaultInternationalValue("Test Value")
//                .build()
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : module,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: "TestObject"])
//        ]
//
//        outUser.getTenantId() >> "testTenant"
//        outUser.getUserId() >> "outUser"
//        outUser.getUserName() >> null
//        outUser.isOutUser() >> true
//        outUser.getOutTenantId() >> "outTenantId"
//        outUser.getUserIdOrOutUserIdIfOutUser() >> "outUser"
//
//        realUser.getTenantId() >> "testTenant"
//        realUser.getUserId() >> "realUser"
//        realUser.getUserName() >> "Real User"
//        realUser.isOutUser() >> false
//        realUser.getOutTenantId() >> null
//        realUser.getUserIdOrOutUserIdIfOutUser() >> "realUser"
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.getUser(_, _) >> realUser
//        orgService.fillUserName(outUser) >> realUser
//
//        when:
//        dataList.each { data ->
//            logService.logWithInternationalCustomMessage(outUser, eventType, actionType, describe, data, customMessage, internationalMessage)
//        }
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "realUser"
//            assert info.userName == "Real User"
//            assert info.outTenantId == "outTenantId"
//            assert info.objectName == module
//            assert info.module == module
//            assert info.objectId == dataId
//            assert info.textMessage == customMessage
//            assert info.internationalTextMessage == internationalMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.appId == "CRM"
//            true
//        }
//    }
//
//    /**
//     * Test log method with object snapshot
//     */
//    def "testLogWithObjectSnapshotTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def textMessage = "Test message"
//        def objectSnapshot = [
//                "field1": "value1",
//                "field2": "value2",
//                "field3": 123
//        ]
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectApiName, objectSnapshot, textMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.textMessage == textMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.snapshot.textMsg[0].text == textMessage
//            assert info.snapshot.textMsg[0].objectApiName == objectApiName
//            assert info.snapshot.snapshot.field1 == "value1"
//            assert info.snapshot.snapshot.field2 == "value2"
//            assert info.snapshot.snapshot.field3 == 123
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with normal case
//     */
//    def "testLogWithCustomMessageTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with optional features disabled
//     */
//    def "testLogWithCustomMessageWithOptionalFeaturesDisabledTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: false)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage)
//
//        then:
//        0 * asyncLogSender.offer(_)  // 当isModifyRecordEnabled为false时，不应该调用offer方法
//    }
//
//    /**
//     * Test logWithCustomMessage method with out user
//     */
//    def "testLogWithCustomMessageWithOutUserTest"() {
//        given:
//        def outUser = Mock(User)
//        def realUser = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        outUser.getTenantId() >> "testTenant"
//        outUser.getUserId() >> "outUser"
//        outUser.getUserName() >> null
//        outUser.isOutUser() >> true
//        outUser.getOutTenantId() >> "outTenantId"
//        outUser.getUserIdOrOutUserIdIfOutUser() >> "outUser"
//
//        realUser.getTenantId() >> "testTenant"
//        realUser.getUserId() >> "realUser"
//        realUser.getUserName() >> "Real User"
//        realUser.isOutUser() >> false
//        realUser.getOutTenantId() >> null
//        realUser.getUserIdOrOutUserIdIfOutUser() >> "realUser"
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.getUser(_, _) >> realUser
//        orgService.fillUserName(outUser) >> realUser
//
//        when:
//        logService.logWithCustomMessage(outUser, eventType, actionType, describe, data, customMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "realUser"
//            assert info.userName == "Real User"
//            assert info.outTenantId == "outTenantId"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with null custom message
//     */
//    def "testLogWithCustomMessageWithNullMessageTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = null
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == null
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with empty custom message
//     */
//    def "testLogWithCustomMessageWithEmptyMessageTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = ""
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == ""
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with team member messages
//     */
//    def "testLogWithCustomMessageWithTeamMemberMessagesTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create team member messages
//        def msgs = [
//                new TeamMemberInfo.Msg(
//                        userId: "user1",
//                        userName: "User One",
//                        roleName: "Role One",
//                        roleId: "role1"
//                ),
//                new TeamMemberInfo.Msg(
//                        userId: "user2",
//                        userName: "User Two",
//                        roleName: "Role Two",
//                        roleId: "role2"
//                )
//        ]
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage, msgs)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.snapshot.teamMemberInfo.msgs.size() == 2
//            assert info.snapshot.teamMemberInfo.msgs[0].userId == "user1"
//            assert info.snapshot.teamMemberInfo.msgs[0].userName == "User One"
//            assert info.snapshot.teamMemberInfo.msgs[0].roleName == "Role One"
//            assert info.snapshot.teamMemberInfo.msgs[0].roleId == "role1"
//            assert info.snapshot.teamMemberInfo.msgs[1].userId == "user2"
//            assert info.snapshot.teamMemberInfo.msgs[1].userName == "User Two"
//            assert info.snapshot.teamMemberInfo.msgs[1].roleName == "Role Two"
//            assert info.snapshot.teamMemberInfo.msgs[1].roleId == "role2"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with empty team member messages
//     */
//    def "testLogWithCustomMessageWithEmptyTeamMemberMessagesTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create empty team member messages
//        def msgs = []
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage, msgs)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.snapshot.teamMemberInfo.msgs.isEmpty()
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with null team member messages
//     */
//    def "testLogWithCustomMessageWithNullTeamMemberMessagesTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create null team member messages
//        def msgs = null
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage, msgs)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.snapshot.teamMemberInfo == null
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with team member messages and optional features disabled
//     */
//    def "testLogWithCustomMessageWithTeamMemberMessagesAndOptionalFeaturesDisabledTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: false)
//
//        // Create team member messages
//        def msgs = [
//                new TeamMemberInfo.Msg(
//                        userId: "user1",
//                        userName: "User One",
//                        roleName: "Role One",
//                        roleId: "role1"
//                )
//        ]
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, data, customMessage, msgs)
//
//        then:
//        0 * asyncLogSender.offer(_)  // 当isModifyRecordEnabled为false时，不应该调用offer方法
//    }
//
//    /**
//     * Test logWithCustomMessage method with team member messages and out user
//     */
//    def "testLogWithCustomMessageWithTeamMemberMessagesAndOutUserTest"() {
//        given:
//        def outUser = Mock(User)
//        def realUser = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create team member messages
//        def msgs = [
//                new TeamMemberInfo.Msg(
//                        userId: "user1",
//                        userName: "User One",
//                        roleName: "Role One",
//                        roleId: "role1"
//                )
//        ]
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        outUser.getTenantId() >> "testTenant"
//        outUser.getUserId() >> "outUser"
//        outUser.getUserName() >> null
//        outUser.isOutUser() >> true
//        outUser.getOutTenantId() >> "outTenantId"
//        outUser.getUserIdOrOutUserIdIfOutUser() >> "outUser"
//
//        realUser.getTenantId() >> "testTenant"
//        realUser.getUserId() >> "realUser"
//        realUser.getUserName() >> "Real User"
//        realUser.isOutUser() >> false
//        realUser.getOutTenantId() >> null
//        realUser.getUserIdOrOutUserIdIfOutUser() >> "realUser"
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.getUser(_, _) >> realUser
//        orgService.fillUserName(outUser) >> realUser
//
//        when:
//        logService.logWithCustomMessage(outUser, eventType, actionType, describe, data, customMessage, msgs)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "realUser"
//            assert info.userName == "Real User"
//            assert info.outTenantId == "outTenantId"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.snapshot.teamMemberInfo.msgs.size() == 1
//            assert info.snapshot.teamMemberInfo.msgs[0].userId == "user1"
//            assert info.snapshot.teamMemberInfo.msgs[0].userName == "User One"
//            assert info.snapshot.teamMemberInfo.msgs[0].roleName == "Role One"
//            assert info.snapshot.teamMemberInfo.msgs[0].roleId == "role1"
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method with data list
//     */
//    def "testLogWithCustomMessageWithDataListTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: objectApiName]),
//                new ObjectData([_id: "TEST-002", name: "Test Data 2", describe_api_name: objectApiName]),
//                new ObjectData([_id: "TEST-003", name: "Test Data 3", describe_api_name: objectApiName])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, describe, dataList, customMessage)
//
//        then:
//        3 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.objectId in ["TEST-001", "TEST-002", "TEST-003"]
//            true
//        }
//    }
//
//    /**
//     * Test logCustomMessageOnly method with normal case
//     */
//    def "testLogCustomMessageOnlyTest"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def customMessage = "Custom message"
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        // Create describe using Map
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create data using Map
//        def containerDocument = Maps.newHashMap()
//        containerDocument.put("_id", "TEST-001")
//        containerDocument.put("name", "Test Data")
//        containerDocument.put("describe_api_name", objectApiName)
//        def data = new ObjectData(containerDocument)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logCustomMessageOnly(user, eventType, actionType, describe, data, customMessage)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "TEST-001"
//            assert info.textMessage == customMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.snapshot == null  // 验证只记录自定义消息，不记录快照
//        }
//    }
//
//    /**
//     * Test log method with IObjectDescribe and List<IObjectData> (normal case)
//     */
//    def "testLogWithDescribeAndDataListNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: objectApiName]),
//                new ObjectData([_id: "TEST-002", name: "Test Data 2", describe_api_name: objectApiName])
//        ]
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, describe, dataList)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId in ["TEST-001", "TEST-002"]
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            true
//        }
//    }
//
//    /**
//     * Test log method with IObjectDescribe and List<IObjectData> (slave object case)
//     */
//    def "testLogWithDescribeAndDataListSlaveObjectCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//        def dataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "DETAIL-002", name: "Detail Data 2", describe_api_name: detailObjectApiName, masterId: "MASTER-002"])
//        ]
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, detailDescribe, dataList)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == detailObjectApiName
//            assert info.objectId in ["DETAIL-001", "DETAIL-002"]
//            assert info.masterId in ["MASTER-001", "MASTER-002"]
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            true
//        }
//    }
//
//    /**
//     * Test log method with IObjectDescribe, List<IObjectData> and extendsInfo (normal case)
//     */
//    def "testLogWithDescribeAndDataListAndExtendsInfoNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: objectApiName]),
//                new ObjectData([_id: "TEST-002", name: "Test Data 2", describe_api_name: objectApiName])
//        ]
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2",
//                "timestamp"   : 1234567890L
//        ]
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, describe, dataList, extendsInfo)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId in ["TEST-001", "TEST-002"]
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot.snapshot.customField1 == "customValue1"
//            assert info.snapshot.snapshot.customField2 == "customValue2"
//            assert info.snapshot.snapshot.timestamp == 1234567890L
//            true
//        }
//    }
//
//    /**
//     * Test log method with IObjectDescribe, List<IObjectData> and extendsInfo (slave object case)
//     */
//    def "testLogWithDescribeAndDataListAndExtendsInfoSlaveObjectCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//        def dataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "DETAIL-002", name: "Detail Data 2", describe_api_name: detailObjectApiName, masterId: "MASTER-002"])
//        ]
//        def extendsInfo = [
//                "source"       : "import",
//                "batchId"      : "BATCH-001",
//                "operationType": "bulk_update"
//        ]
//        def optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO(isModifyRecordEnabled: true)
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitch
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, detailDescribe, dataList, extendsInfo)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == detailObjectApiName
//            assert info.objectId in ["DETAIL-001", "DETAIL-002"]
//            assert info.masterId in ["MASTER-001", "MASTER-002"]
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot.snapshot.source == "import"
//            assert info.snapshot.snapshot.batchId == "BATCH-001"
//            assert info.snapshot.snapshot.operationType == "bulk_update"
//            true
//        }
//    }
//
//    /**
//     * Test log method with Map<String, IObjectDescribe> and List<IObjectData> (normal case)
//     */
//    def "testLogWithObjectDescribesAndDataListNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with mixed object types
//        def dataList = [
//                new ObjectData([_id: "MASTER-001", name: "Master Data 1", describe_api_name: masterObjectApiName]),
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "MASTER-002", name: "Master Data 2", describe_api_name: masterObjectApiName])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribes, dataList)
//
//        then:
//        3 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId in ["MASTER-001", "MASTER-002"]
//                assert info.masterId == null
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId == "DETAIL-001"
//                assert info.masterId == "MASTER-001"
//            }
//            true
//        }
//    }
//
//    /**
//     * Test log method with Map<String, IObjectDescribe> and List<IObjectData> (master objects only)
//     */
//    def "testLogWithObjectDescribesAndDataListMasterObjectsOnly"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.ADD
//        def actionType = ActionType.Add
//        def objectApiName = "TestObject"
//
//        // Create describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def describe = new ObjectDescribe(describeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (objectApiName): describe
//        ]
//
//        // Create data list with only master objects
//        def dataList = [
//                new ObjectData([_id: "TEST-001", name: "Test Data 1", describe_api_name: objectApiName]),
//                new ObjectData([_id: "TEST-002", name: "Test Data 2", describe_api_name: objectApiName]),
//                new ObjectData([_id: "TEST-003", name: "Test Data 3", describe_api_name: objectApiName])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribes, dataList)
//
//        then:
//        3 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId in ["TEST-001", "TEST-002", "TEST-003"]
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.masterId == null
//            true
//        }
//    }
//
//    /**
//     * Test log method with Map<String, IObjectDescribe> and List<IObjectData> (detail objects only)
//     */
//    def "testLogWithObjectDescribesAndDataListDetailObjectsOnly"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.DELETE
//        def actionType = ActionType.Delete
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with only detail objects
//        def dataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "DETAIL-002", name: "Detail Data 2", describe_api_name: detailObjectApiName, masterId: "MASTER-002"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribes, dataList)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == detailObjectApiName
//            assert info.objectId in ["DETAIL-001", "DETAIL-002"]
//            assert info.masterId in ["MASTER-001", "MASTER-002"]
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            true
//        }
//    }
//
//    /**
//     * Test log method with Map<String, IObjectDescribe>, List<IObjectData> and masterLogId
//     */
//    def "testLogWithObjectDescribesAndDataListAndMasterLogId"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def masterLogId = "MASTER-LOG-001"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with mixed object types
//        def dataList = [
//                new ObjectData([_id: "MASTER-001", name: "Master Data 1", describe_api_name: masterObjectApiName]),
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribes, dataList, masterLogId)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId == "MASTER-001"
//                assert info.masterId == null
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId == "DETAIL-001"
//                assert info.masterId == "MASTER-001"
//                assert info.masterLogId == masterLogId
//            }
//            true
//        }
//    }
//
//    /**
//     * Test log method with Map<String, IObjectDescribe>, List<IObjectData>, masterLogId and ConvertSourceContainer
//     */
//    def "testLogWithObjectDescribesAndDataListAndMasterLogIdAndConvertSourceContainer"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def masterLogId = "MASTER-LOG-001"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with mixed object types
//        def dataList = [
//                new ObjectData([_id: "MASTER-001", name: "Master Data 1", describe_api_name: masterObjectApiName]),
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//
//        // Create ConvertSourceContainer mock
//        def convertSourceContainer = Mock(ConvertSourceContainer)
//        convertSourceContainer.getEventId() >> "CONVERT-EVENT-001"
//        convertSourceContainer.getSourceObjectDescribe() >> masterDescribe
//        convertSourceContainer.getSourceObjectDataList() >> [new ObjectData([_id: "SOURCE-001", name: "Source Data", describe_api_name: masterObjectApiName])]
//        convertSourceContainer.getSourceDetailDescribes() >> [:]
//        convertSourceContainer.getSourceDetailList() >> [:]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribes, dataList, masterLogId, convertSourceContainer)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId == "MASTER-001"
//                assert info.masterId == null
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId == "DETAIL-001"
//                assert info.masterId == "MASTER-001"
//                assert info.masterLogId == masterLogId
//            }
//            true
//        }
//    }
//
//    /**
//     * Test masterDetailLog method with Map<String, IObjectDescribe> and List<IObjectData>
//     */
//    def "testMasterDetailLogWithObjectDescribesAndDataList"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with mixed object types
//        def dataList = [
//                new ObjectData([_id: "MASTER-001", name: "Master Data 1", describe_api_name: masterObjectApiName]),
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "DETAIL-002", name: "Detail Data 2", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.masterDetailLog(user, eventType, actionType, objectDescribes, dataList)
//
//        then:
//        3 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId == "MASTER-001"
//                assert info.masterId == null
//                assert info.masterLogId != null
//                assert info.snapshot.detailInfo.size() == 1
//                assert info.snapshot.detailInfo[0].objectApiName == detailObjectApiName
//                assert info.snapshot.detailInfo[0].objectLabel == "Detail Object"
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId in ["DETAIL-001", "DETAIL-002"]
//                assert info.masterId == "MASTER-001"
//                assert info.masterLogId != null
//            }
//            true
//        }
//    }
//
//    /**
//     * Test masterDetailLog method with Map<String, IObjectDescribe>, List<IObjectData>, peerName and peerDisplayName
//     */
//    def "testMasterDetailLogWithObjectDescribesAndDataListAndPeerInfo"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def peerName = "PeerObject"
//        def peerDisplayName = "Peer Display Name"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with mixed object types
//        def dataList = [
//                new ObjectData([_id: "MASTER-001", name: "Master Data 1", describe_api_name: masterObjectApiName]),
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "DETAIL-002", name: "Detail Data 2", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.masterDetailLog(user, eventType, actionType, objectDescribes, dataList, peerName, peerDisplayName)
//
//        then:
//        3 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.peerName == peerName
//            assert info.snapshot.peerDisplayName == peerDisplayName
//
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId == "MASTER-001"
//                assert info.masterId == null
//                assert info.masterLogId != null
//                assert info.snapshot.detailInfo.size() == 1
//                assert info.snapshot.detailInfo[0].objectApiName == detailObjectApiName
//                assert info.snapshot.detailInfo[0].objectLabel == "Detail Object"
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId in ["DETAIL-001", "DETAIL-002"]
//                assert info.masterId == "MASTER-001"
//                assert info.masterLogId != null
//            }
//            true
//        }
//    }
//
//    /**
//     * Test masterDetailLog method with Map<String, IObjectDescribe>, List<IObjectData>, peerName, peerDisplayName, extendsInfo and ConvertSourceContainer
//     */
//    def "testMasterDetailLogWithObjectDescribesAndDataListAndPeerInfoAndExtendsInfoAndConvertSourceContainer"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def peerName = "PeerObject"
//        def peerDisplayName = "Peer Display Name"
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2",
//                "timestamp"   : 1234567890L
//        ]
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create data list with mixed object types
//        def dataList = [
//                new ObjectData([_id: "MASTER-001", name: "Master Data 1", describe_api_name: masterObjectApiName]),
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"]),
//                new ObjectData([_id: "DETAIL-002", name: "Detail Data 2", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//
//        // Create ConvertSourceContainer mock
//        def convertSourceContainer = Mock(ConvertSourceContainer)
//        convertSourceContainer.getEventId() >> "CONVERT-EVENT-001"
//        convertSourceContainer.getSourceObjectDescribe() >> masterDescribe
//        convertSourceContainer.getSourceObjectDataList() >> [new ObjectData([_id: "SOURCE-001", name: "Source Data", describe_api_name: masterObjectApiName])]
//        convertSourceContainer.getSourceDetailDescribes() >> [:]
//        convertSourceContainer.getSourceDetailList() >> [:]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.masterDetailLog(user, eventType, actionType, objectDescribes, dataList, peerName, peerDisplayName, extendsInfo, convertSourceContainer)
//
//        then:
//        3 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//            assert info.appId == "CRM"
//            assert info.peerName == peerName
//            assert info.snapshot.peerDisplayName == peerDisplayName
//            assert info.snapshot.snapshot.customField1 == "customValue1"
//            assert info.snapshot.snapshot.customField2 == "customValue2"
//            assert info.snapshot.snapshot.timestamp == 1234567890L
//            assert info.snapshot.convertEventId == "CONVERT-EVENT-001"
//            assert info.snapshot.sourceSnapshot != null
//
//            if (info.objectName == masterObjectApiName) {
//                assert info.objectId == "MASTER-001"
//                assert info.masterId == null
//                assert info.masterLogId != null
//                assert info.snapshot.detailInfo.size() == 1
//                assert info.snapshot.detailInfo[0].objectApiName == detailObjectApiName
//                assert info.snapshot.detailInfo[0].objectLabel == "Detail Object"
//            } else if (info.objectName == detailObjectApiName) {
//                assert info.objectId in ["DETAIL-001", "DETAIL-002"]
//                assert info.masterId == "MASTER-001"
//                assert info.masterLogId != null
//            }
//            true
//        }
//    }
//
//    /**
//     * Test detailModifyLog method with normal case
//     */
//    def "testDetailModifyLogNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def masterLogId = "MASTER-LOG-001"
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create dataList (待修改的明细数据)
//        def dataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//        // Create dbDetailDataList (数据库原始明细数据)
//        def dbDetailDataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1 Old", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//        // updatedFieldMap (本次变更的字段)
//        def updatedFieldMap = [
//                "DETAIL-001": ["name": "Detail Data 1"]
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.detailModifyLog(user, detailDescribe, dataList, updatedFieldMap, dbDetailDataList, masterLogId)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == detailObjectApiName
//            assert info.objectId == "DETAIL-001"
//            assert info.masterId == "MASTER-001"
//            assert info.masterLogId == masterLogId
//            assert info.operation == EventType.MODIFY.getId()
//            assert info.bizOperationName == ActionType.Modify.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot.name == "Detail Data 1"
//            true
//        }
//    }
//
//    /**
//     * Test detailModifyLog method with ActionType parameter (normal case)
//     */
//    def "testDetailModifyLogWithActionTypeNormalCase"() {
//        given:
//        def user = Mock(User)
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//        def masterLogId = "MASTER-LOG-001"
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create dataList (待修改的明细数据)
//        def dataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//        // Create dbDetailDataList (数据库原始明细数据)
//        def dbDetailDataList = [
//                new ObjectData([_id: "DETAIL-001", name: "Detail Data 1 Old", describe_api_name: detailObjectApiName, masterId: "MASTER-001"])
//        ]
//        // updatedFieldMap (本次变更的字段)
//        def updatedFieldMap = [
//                "DETAIL-001": ["name": "Detail Data 1"]
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.detailModifyLog(user, actionType, detailDescribe, dataList, updatedFieldMap, dbDetailDataList, masterLogId)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == detailObjectApiName
//            assert info.objectId == "DETAIL-001"
//            assert info.masterId == "MASTER-001"
//            assert info.masterLogId == masterLogId
//            assert info.operation == EventType.MODIFY.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot.name == "Detail Data 1"
//            true
//        }
//    }
//
//    /**
//     * Test masterModifyLog method with normal case
//     */
//    def "testMasterModifyLogNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create detailChangeMap
//        def detailChangeMap = [
//                (detailObjectApiName): [
//                        "UPDATE": [
//                                "DETAIL-001": ["name": "Detail Data"]
//                        ]
//                ]
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.masterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, detailChangeMap)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == masterObjectApiName
//            assert info.objectId == "MASTER-001"
//            assert info.operation == EventType.MODIFY.getId()
//            assert info.bizOperationName == ActionType.Modify.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot.name == "Master Data"
//            assert info.snapshot.detailInfo.size() == 1
//            assert info.snapshot.detailInfo[0].objectApiName == detailObjectApiName
//            assert info.snapshot.detailInfo[0].objectLabel == "Detail Object"
//            true
//        }
//        result != null
//        result.length() > 0
//    }
//
//    /**
//     * Test masterModifyLog method with List<String> changeDetailApiName parameter (normal case)
//     */
//    def "testMasterModifyLogWithChangeDetailApiNameNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create changeDetailApiName list
//        def changeDetailApiName = [detailObjectApiName]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.masterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == masterObjectApiName
//            assert info.objectId == "MASTER-001"
//            assert info.operation == EventType.MODIFY.getId()
//            assert info.bizOperationName == ActionType.Modify.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot.name == "Master Data"
//            assert info.snapshot.detailInfo.size() == 1
//            assert info.snapshot.detailInfo[0].objectApiName == detailObjectApiName
//            assert info.snapshot.detailInfo[0].objectLabel == "Detail Object"
//            true
//        }
//        result != null
//        result.length() > 0
//    }
//
//    /**
//     * Test sendLog method with normal case
//     */
//    def "testSendLogNormalCase"() {
//        given:
//        def logList = [
//                LogInfo.builder()
//                        .logId("LOG-001")
//                        .corpId("testTenant")
//                        .userId("testUser")
//                        .userName("Test User")
//                        .objectName("TestObject")
//                        .objectId("TEST-001")
//                        .operation(EventType.MODIFY.getId())
//                        .bizOperationName(ActionType.Modify.getId())
//                        .build(),
//                LogInfo.builder()
//                        .logId("LOG-002")
//                        .corpId("testTenant")
//                        .userId("testUser")
//                        .userName("Test User")
//                        .objectName("TestObject")
//                        .objectId("TEST-002")
//                        .operation(EventType.ADD.getId())
//                        .bizOperationName(ActionType.Add.getId())
//                        .build()
//        ]
//
//        when:
//        logService.sendLog(logList)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == "TestObject"
//            assert info.objectId in ["TEST-001", "TEST-002"]
//            assert info.operation in [EventType.MODIFY.getId(), EventType.ADD.getId()]
//            assert info.bizOperationName in [ActionType.Modify.getId(), ActionType.Add.getId()]
//            true
//        }
//    }
//
//    /**
//     * Test fillMasterModifyLog method with Map<String, Object> detailChangeMap parameter (normal case)
//     */
//    def "testFillMasterModifyLogWithDetailChangeMapNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create detailChangeMap
//        def detailChangeMap = [
//                (detailObjectApiName): [
//                        "UPDATE": [
//                                "DETAIL-001": ["name": "Detail Data"]
//                        ]
//                ]
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, detailChangeMap)
//
//        then:
//        result != null
//        result.masterLogId != null
//        result.logList != null
//        result.logList.size() == 1
//        result.logList[0].corpId == "testTenant"
//        result.logList[0].userId == "testUser"
//        result.logList[0].userName == "Test User"
//        result.logList[0].objectName == masterObjectApiName
//        result.logList[0].objectId == "MASTER-001"
//        result.logList[0].operation == EventType.MODIFY.getId()
//        result.logList[0].bizOperationName == ActionType.Modify.getId()
//        result.logList[0].snapshot != null
//        result.logList[0].snapshot.snapshot.name == "Master Data"
//        result.logList[0].snapshot.detailInfo.size() == 1
//        result.logList[0].snapshot.detailInfo[0].objectApiName == detailObjectApiName
//        result.logList[0].snapshot.detailInfo[0].objectLabel == "Detail Object"
//    }
//
//    /**
//     * Test fillMasterModifyLog method with Map<String, Object> extendsInfo parameter (normal case)
//     */
//    def "testFillMasterModifyLogWithExtendsInfoNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create detailChangeMap
//        def detailChangeMap = [
//                (detailObjectApiName): [
//                        "UPDATE": [
//                                "DETAIL-001": ["name": "Detail Data"]
//                        ]
//                ]
//        ]
//
//        // Create extendsInfo
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2",
//                "timestamp"   : 1234567890L,
//                "source"      : "import",
//                "batchId"     : "BATCH-001"
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, detailChangeMap, extendsInfo)
//
//        then:
//        result != null
//        result.masterLogId != null
//        result.logList != null
//        result.logList.size() == 1
//        result.logList[0].corpId == "testTenant"
//        result.logList[0].userId == "testUser"
//        result.logList[0].userName == "Test User"
//        result.logList[0].objectName == masterObjectApiName
//        result.logList[0].objectId == "MASTER-001"
//        result.logList[0].operation == EventType.MODIFY.getId()
//        result.logList[0].bizOperationName == ActionType.Modify.getId()
//        result.logList[0].snapshot != null
//        result.logList[0].snapshot.snapshot.name == "Master Data"
//        result.logList[0].snapshot.snapshot.customField1 == "customValue1"
//        result.logList[0].snapshot.snapshot.customField2 == "customValue2"
//        result.logList[0].snapshot.snapshot.timestamp == 1234567890L
//        result.logList[0].snapshot.snapshot.source == "import"
//        result.logList[0].snapshot.snapshot.batchId == "BATCH-001"
//        result.logList[0].snapshot.detailInfo.size() == 1
//        result.logList[0].snapshot.detailInfo[0].objectApiName == detailObjectApiName
//        result.logList[0].snapshot.detailInfo[0].objectLabel == "Detail Object"
//    }
//
//    /**
//     * Test fillMasterModifyLog method with Map<String, Object> extendsInfo and ConvertSourceContainer parameters (normal case)
//     */
//    def "testFillMasterModifyLogWithExtendsInfoAndConvertSourceContainerNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create detailChangeMap
//        def detailChangeMap = [
//                (detailObjectApiName): [
//                        "UPDATE": [
//                                "DETAIL-001": ["name": "Detail Data"]
//                        ]
//                ]
//        ]
//
//        // Create extendsInfo
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2",
//                "timestamp"   : 1234567890L,
//                "source"      : "import",
//                "batchId"     : "BATCH-001"
//        ]
//
//        // Create ConvertSourceContainer mock
//        def convertSourceContainer = Mock(ConvertSourceContainer)
//        convertSourceContainer.getEventId() >> "CONVERT-EVENT-001"
//        convertSourceContainer.getSourceObjectDescribe() >> masterDescribe
//        convertSourceContainer.getSourceObjectDataList() >> [new ObjectData([_id: "SOURCE-001", name: "Source Data", describe_api_name: masterObjectApiName])]
//        convertSourceContainer.getSourceDetailDescribes() >> [:]
//        convertSourceContainer.getSourceDetailList() >> [:]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.fillMasterModifyLog(user, objectDescribes, masterData, updatedFieldMap, dbMasterData, detailChangeMap, extendsInfo, convertSourceContainer)
//
//        then:
//        result != null
//        result.masterLogId != null
//        result.logList != null
//        result.logList.size() == 1
//        result.logList[0].corpId == "testTenant"
//        result.logList[0].userId == "testUser"
//        result.logList[0].userName == "Test User"
//        result.logList[0].objectName == masterObjectApiName
//        result.logList[0].objectId == "MASTER-001"
//        result.logList[0].operation == EventType.MODIFY.getId()
//        result.logList[0].bizOperationName == ActionType.Modify.getId()
//        result.logList[0].snapshot != null
//        result.logList[0].snapshot.snapshot.name == "Master Data"
//        result.logList[0].snapshot.snapshot.customField1 == "customValue1"
//        result.logList[0].snapshot.snapshot.customField2 == "customValue2"
//        result.logList[0].snapshot.snapshot.timestamp == 1234567890L
//        result.logList[0].snapshot.snapshot.source == "import"
//        result.logList[0].snapshot.snapshot.batchId == "BATCH-001"
//        result.logList[0].snapshot.convertEventId == "CONVERT-EVENT-001"
//        result.logList[0].snapshot.sourceSnapshot != null
//        result.logList[0].snapshot.detailInfo.size() == 1
//        result.logList[0].snapshot.detailInfo[0].objectApiName == detailObjectApiName
//        result.logList[0].snapshot.detailInfo[0].objectLabel == "Detail Object"
//    }
//
//    /**
//     * Test fillMasterModifyLog method with ActionType and Map<String, Object> extendsInfo parameters (normal case)
//     */
//    def "testFillMasterModifyLogWithActionTypeAndExtendsInfoNormalCase"() {
//        given:
//        def user = Mock(User)
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create detailChangeMap
//        def detailChangeMap = [
//                (detailObjectApiName): [
//                        "UPDATE": [
//                                "DETAIL-001": ["name": "Detail Data"]
//                        ]
//                ]
//        ]
//
//        // Create extendsInfo
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2",
//                "timestamp"   : 1234567890L,
//                "source"      : "import",
//                "batchId"     : "BATCH-001"
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.fillMasterModifyLog(user, actionType, objectDescribes, masterData, updatedFieldMap, dbMasterData, detailChangeMap, extendsInfo)
//
//        then:
//        result != null
//        result.masterLogId != null
//        result.logList != null
//        result.logList.size() == 1
//        result.logList[0].corpId == "testTenant"
//        result.logList[0].userId == "testUser"
//        result.logList[0].userName == "Test User"
//        result.logList[0].objectName == masterObjectApiName
//        result.logList[0].objectId == "MASTER-001"
//        result.logList[0].operation == EventType.MODIFY.getId()
//        result.logList[0].bizOperationName == actionType.getId()
//        result.logList[0].snapshot != null
//        result.logList[0].snapshot.snapshot.name == "Master Data"
//        result.logList[0].snapshot.snapshot.customField1 == "customValue1"
//        result.logList[0].snapshot.snapshot.customField2 == "customValue2"
//        result.logList[0].snapshot.snapshot.timestamp == 1234567890L
//        result.logList[0].snapshot.snapshot.source == "import"
//        result.logList[0].snapshot.snapshot.batchId == "BATCH-001"
//        result.logList[0].snapshot.detailInfo.size() == 1
//        result.logList[0].snapshot.detailInfo[0].objectApiName == detailObjectApiName
//        result.logList[0].snapshot.detailInfo[0].objectLabel == "Detail Object"
//    }
//
//    /**
//     * Test fillMasterModifyLog method with ActionType and List<String> changeDetailApiName parameters (normal case)
//     */
//    def "testFillMasterModifyLogWithActionTypeAndChangeDetailApiNameNormalCase"() {
//        given:
//        def user = Mock(User)
//        def actionType = ActionType.Modify
//        def masterObjectApiName = "MasterObject"
//        def detailObjectApiName = "DetailObject"
//
//        // Create master describe
//        def masterDescribeMap = [
//                "api_name"    : masterObjectApiName,
//                "display_name": "Master Object",
//                "object_type" : "MASTER",
//                "fields"      : [:],
//                "tenant_id"   : "testTenant"
//        ]
//        def masterDescribe = new ObjectDescribe(masterDescribeMap)
//
//        // Create detail describe
//        def detailDescribeMap = [
//                "api_name"              : detailObjectApiName,
//                "display_name"          : "Detail Object",
//                "object_type"           : "DETAIL",
//                "parent_object_api_name": masterObjectApiName,
//                "fields"                : ["masterId": ["api_name": "masterId"]],
//                "tenant_id"             : "testTenant"
//        ]
//        def detailDescribe = new ObjectDescribe(detailDescribeMap)
//
//        // Create object describes map
//        def objectDescribes = [
//                (masterObjectApiName): masterDescribe,
//                (detailObjectApiName): detailDescribe
//        ]
//
//        // Create master data
//        def masterData = new ObjectData([_id: "MASTER-001", name: "Master Data", describe_api_name: masterObjectApiName])
//        def dbMasterData = new ObjectData([_id: "MASTER-001", name: "Master Data Old", describe_api_name: masterObjectApiName])
//
//        // Create updatedFieldMap
//        def updatedFieldMap = [
//                (masterObjectApiName): [
//                        "MASTER-001": ["name": "Master Data"]
//                ]
//        ]
//
//        // Create changeDetailApiName list
//        def changeDetailApiName = [detailObjectApiName]
//
//        // Create extendsInfo
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2",
//                "timestamp"   : 1234567890L,
//                "source"      : "import",
//                "batchId"     : "BATCH-001"
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        def result = logService.fillMasterModifyLog(user, actionType, objectDescribes, masterData, updatedFieldMap, dbMasterData, changeDetailApiName, extendsInfo)
//
//        then:
//        result != null
//        result.masterLogId != null
//        result.logList != null
//        result.logList.size() == 1
//        result.logList[0].corpId == "testTenant"
//        result.logList[0].userId == "testUser"
//        result.logList[0].userName == "Test User"
//        result.logList[0].objectName == masterObjectApiName
//        result.logList[0].objectId == "MASTER-001"
//        result.logList[0].operation == EventType.MODIFY.getId()
//        result.logList[0].bizOperationName == actionType.getId()
//        result.logList[0].snapshot != null
//        result.logList[0].snapshot.snapshot.name == "Master Data"
//        result.logList[0].snapshot.snapshot.customField1 == "customValue1"
//        result.logList[0].snapshot.snapshot.customField2 == "customValue2"
//        result.logList[0].snapshot.snapshot.timestamp == 1234567890L
//        result.logList[0].snapshot.snapshot.source == "import"
//        result.logList[0].snapshot.snapshot.batchId == "BATCH-001"
//        result.logList[0].snapshot.detailInfo.size() == 1
//        result.logList[0].snapshot.detailInfo[0].objectApiName == detailObjectApiName
//        result.logList[0].snapshot.detailInfo[0].objectLabel == "Detail Object"
//    }
//
//    /**
//     * Test bulkRecordEditLog method (normal case)
//     * 测试第833行的bulkRecordEditLog方法
//     */
//    def "testBulkRecordEditLogNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "TestObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Test Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"  : ["api_name": "name", "field_type": "TEXT"],
//                        "status": ["api_name": "status", "field_type": "TEXT"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def data1 = new ObjectData([_id: "DATA-001", name: "Updated Name 1", status: "Active", describe_api_name: objectApiName])
//        def data2 = new ObjectData([_id: "DATA-002", name: "Updated Name 2", status: "Inactive", describe_api_name: objectApiName])
//        def dataList = [data1, data2]
//
//        // Create db data list
//        def dbData1 = new ObjectData([_id: "DATA-001", name: "Original Name 1", status: "Inactive", describe_api_name: objectApiName])
//        def dbData2 = new ObjectData([_id: "DATA-002", name: "Original Name 2", status: "Active", describe_api_name: objectApiName])
//        def dbDataList = [dbData1, dbData2]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.bulkRecordEditLog(user, eventType, actionType, objectDescribe, dataList, dbDataList)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot != null
//            assert info.snapshot.snapshot.name != null
//            assert info.snapshot.snapshot.status != null
//        }
//    }
//
//    /**
//     * Test updateImportLog method (normal case)
//     * 测试第839行的updateImportLog方法
//     */
//    def "testUpdateImportLogNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "ImportObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Import Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name" : ["api_name": "name", "field_type": "TEXT"],
//                        "email": ["api_name": "email", "field_type": "TEXT"],
//                        "phone": ["api_name": "phone", "field_type": "TEXT"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def data1 = new ObjectData([_id: "IMPORT-001", name: "John Doe", email: "<EMAIL>", phone: "1234567890", describe_api_name: objectApiName])
//        def data2 = new ObjectData([_id: "IMPORT-002", name: "Jane Smith", email: "<EMAIL>", phone: "0987654321", describe_api_name: objectApiName])
//        def dataList = [data1, data2]
//
//        // Create db data map
//        def dbData1 = new ObjectData([_id: "IMPORT-001", name: "John Doe Old", email: "<EMAIL>", phone: "1111111111", describe_api_name: objectApiName])
//        def dbData2 = new ObjectData([_id: "IMPORT-002", name: "Jane Smith Old", email: "<EMAIL>", phone: "2222222222", describe_api_name: objectApiName])
//        def dbDataMap = [
//                "IMPORT-001": dbData1,
//                "IMPORT-002": dbData2
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.updateImportLog(user, eventType, actionType, objectDescribe, dataList, dbDataMap)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot != null
//            assert info.snapshot.snapshot.name != null
//            assert info.snapshot.snapshot.email != null
//            assert info.snapshot.snapshot.phone != null
//        }
//    }
//
//    /**
//     * Test updateImportLog method with extendsInfo parameter (normal case)
//     * 测试第839行的updateImportLog方法重载版本（带extendsInfo参数）
//     */
//    def "testUpdateImportLogWithExtendsInfoNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "ImportObjectWithExtends"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Import Object With Extends",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"      : ["api_name": "name", "field_type": "TEXT"],
//                        "email"     : ["api_name": "email", "field_type": "TEXT"],
//                        "phone"     : ["api_name": "phone", "field_type": "TEXT"],
//                        "department": ["api_name": "department", "field_type": "TEXT"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def data1 = new ObjectData([_id: "IMPORT-EXT-001", name: "John Doe Updated", email: "<EMAIL>", phone: "1234567890", department: "Engineering", describe_api_name: objectApiName])
//        def data2 = new ObjectData([_id: "IMPORT-EXT-002", name: "Jane Smith Updated", email: "<EMAIL>", phone: "0987654321", department: "Marketing", describe_api_name: objectApiName])
//        def dataList = [data1, data2]
//
//        // Create db data map
//        def dbData1 = new ObjectData([_id: "IMPORT-EXT-001", name: "John Doe", email: "<EMAIL>", phone: "1111111111", department: "Sales", describe_api_name: objectApiName])
//        def dbData2 = new ObjectData([_id: "IMPORT-EXT-002", name: "Jane Smith", email: "<EMAIL>", phone: "2222222222", department: "HR", describe_api_name: objectApiName])
//        def dbDataMap = [
//                "IMPORT-EXT-001": dbData1,
//                "IMPORT-EXT-002": dbData2
//        ]
//
//        // Create extendsInfo
//        def extendsInfo = [
//                "importSource"   : "Excel File",
//                "importBatchId"  : "BATCH-2024-001",
//                "importTimestamp": 1704067200000L,
//                "importUser"     : "admin",
//                "importVersion"  : "1.0",
//                "customField1"   : "customValue1",
//                "customField2"   : "customValue2"
//        ]
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.updateImportLog(user, eventType, actionType, objectDescribe, dataList, dbDataMap, extendsInfo)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot != null
//            assert info.snapshot.snapshot.name != null
//            assert info.snapshot.snapshot.email != null
//            assert info.snapshot.snapshot.phone != null
//            assert info.snapshot.snapshot.department != null
//            // Verify extendsInfo fields are included in snapshot
//            assert info.snapshot.snapshot.importSource == "Excel File"
//            assert info.snapshot.snapshot.importBatchId == "BATCH-2024-001"
//            assert info.snapshot.snapshot.importTimestamp == 1704067200000L
//            assert info.snapshot.snapshot.importUser == "admin"
//            assert info.snapshot.snapshot.importVersion == "1.0"
//            assert info.snapshot.snapshot.customField1 == "customValue1"
//            assert info.snapshot.snapshot.customField2 == "customValue2"
//        }
//    }
//
//    /**
//     * Test bulkRecordEditLog method with updateFields and logExtendInfo parameters (normal case)
//     * 测试第844行的bulkRecordEditLog方法重载版本（带updateFields和logExtendInfo参数）
//     */
//    def "testBulkRecordEditLogWithUpdateFieldsAndLogExtendInfoNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "BulkEditObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Bulk Edit Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"      : ["api_name": "name", "field_type": "TEXT"],
//                        "email"     : ["api_name": "email", "field_type": "TEXT"],
//                        "phone"     : ["api_name": "phone", "field_type": "TEXT"],
//                        "department": ["api_name": "department", "field_type": "TEXT"],
//                        "status"    : ["api_name": "status", "field_type": "TEXT"],
//                        "salary"    : ["api_name": "salary", "field_type": "NUMBER"],
//                        "hireDate"  : ["api_name": "hireDate", "field_type": "DATE"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def data1 = new ObjectData([_id: "BULK-001", name: "John Doe Updated", email: "<EMAIL>", phone: "1234567890", department: "Engineering", status: "Active", salary: 80000, hireDate: "2024-01-15", describe_api_name: objectApiName])
//        def data2 = new ObjectData([_id: "BULK-002", name: "Jane Smith Updated", email: "<EMAIL>", phone: "0987654321", department: "Marketing", status: "Active", salary: 75000, hireDate: "2024-02-20", describe_api_name: objectApiName])
//        def dataList = [data1, data2]
//
//        // Create db data list
//        def dbData1 = new ObjectData([_id: "BULK-001", name: "John Doe", email: "<EMAIL>", phone: "1111111111", department: "Sales", status: "Inactive", salary: 70000, hireDate: "2023-06-01", describe_api_name: objectApiName])
//        def dbData2 = new ObjectData([_id: "BULK-002", name: "Jane Smith", email: "<EMAIL>", phone: "2222222222", department: "HR", status: "Inactive", salary: 65000, hireDate: "2023-08-15", describe_api_name: objectApiName])
//        def dbDataList = [dbData1, dbData2]
//
//        // Create updateFields list (only specific fields should be logged)
//        def updateFields = ["name", "email", "department", "status"]
//
//        // Create logExtendInfo
//        def extendsInfo = [
//                "bulkOperationId"       : "BULK-OP-2024-001",
//                "bulkOperationType"     : "Employee Update",
//                "bulkOperationSource"   : "HR System",
//                "bulkOperationTimestamp": 1704067200000L,
//                "bulkOperationUser"     : "hr_admin",
//                "bulkOperationReason"   : "Annual Review",
//                "customField1"          : "customValue1",
//                "customField2"          : "customValue2"
//        ]
//        def logExtendInfo = LogExtendInfo.builder().extendsInfo(extendsInfo).build()
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.bulkRecordEditLog(user, eventType, actionType, objectDescribe, dataList, dbDataList, updateFields, logExtendInfo)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot != null
//            // Verify only updateFields are included in snapshot
//            assert info.snapshot.snapshot.name != null
//            assert info.snapshot.snapshot.email != null
//            assert info.snapshot.snapshot.department != null
//            assert info.snapshot.snapshot.status != null
//            // Verify fields not in updateFields are not included
//            assert info.snapshot.snapshot.phone == null
//            assert info.snapshot.snapshot.salary == null
//            assert info.snapshot.snapshot.hireDate == null
//            // Verify extendsInfo fields are included in snapshot
//            assert info.snapshot.snapshot.bulkOperationId == "BULK-OP-2024-001"
//            assert info.snapshot.snapshot.bulkOperationType == "Employee Update"
//            assert info.snapshot.snapshot.bulkOperationSource == "HR System"
//            assert info.snapshot.snapshot.bulkOperationTimestamp == 1704067200000L
//            assert info.snapshot.snapshot.bulkOperationUser == "hr_admin"
//            assert info.snapshot.snapshot.bulkOperationReason == "Annual Review"
//            assert info.snapshot.snapshot.customField1 == "customValue1"
//            assert info.snapshot.snapshot.customField2 == "customValue2"
//        }
//    }
//
//    /**
//     * Test log method with updatedFieldMap and dbData parameters (normal case)
//     * 测试第850行的log方法重载版本（带updatedFieldMap和dbData参数）
//     */
//    def "testLogWithUpdatedFieldMapAndDbDataNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "LogObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Log Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"  : ["api_name": "name", "field_type": "TEXT"],
//                        "email" : ["api_name": "email", "field_type": "TEXT"],
//                        "phone" : ["api_name": "phone", "field_type": "TEXT"],
//                        "status": ["api_name": "status", "field_type": "TEXT"],
//                        "age"   : ["api_name": "age", "field_type": "NUMBER"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create modify data
//        def modifyData = new ObjectData([_id: "LOG-001", name: "John Doe Updated", email: "<EMAIL>", phone: "1234567890", status: "Active", age: 30, describe_api_name: objectApiName])
//
//        // Create updated field map
//        def updatedFieldMap = [
//                "name"  : "John Doe Updated",
//                "email" : "<EMAIL>",
//                "status": "Active"
//        ]
//
//        // Create db data
//        def dbData = new ObjectData([_id: "LOG-001", name: "John Doe", email: "<EMAIL>", phone: "1111111111", status: "Inactive", age: 29, describe_api_name: objectApiName])
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribe, modifyData, updatedFieldMap, dbData)
//
//        then:
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.objectId == "LOG-001"
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot != null
//            // Verify updated fields are included in snapshot
//            assert info.snapshot.snapshot.name == "John Doe Updated"
//            assert info.snapshot.snapshot.email == "<EMAIL>"
//            assert info.snapshot.snapshot.status == "Active"
//            // Verify other fields are not included in updatedFieldMap
//            assert info.snapshot.snapshot.phone == null
//            assert info.snapshot.snapshot.age == null
//        }
//    }
//
//    /**
//     * Test logWithCustomMessage method (normal case)
//     * 测试第896行的logWithCustomMessage方法
//     */
//    def "testLogWithCustomMessageNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "CustomMessageObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Custom Message Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"      : ["api_name": "name", "field_type": "TEXT"],
//                        "email"     : ["api_name": "email", "field_type": "TEXT"],
//                        "phone"     : ["api_name": "phone", "field_type": "TEXT"],
//                        "status"    : ["api_name": "status", "field_type": "TEXT"],
//                        "department": ["api_name": "department", "field_type": "TEXT"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create data list
//        def data1 = new ObjectData([_id: "CUSTOM-001", name: "John Doe Updated", email: "<EMAIL>", phone: "1234567890", status: "Active", department: "Engineering", describe_api_name: objectApiName])
//        def data2 = new ObjectData([_id: "CUSTOM-002", name: "Jane Smith Updated", email: "<EMAIL>", phone: "0987654321", status: "Active", department: "Marketing", describe_api_name: objectApiName])
//        def dataList = [data1, data2]
//
//        // Create updated field map
//        def updatedFieldMap = [
//                "CUSTOM-001": [
//                        "name"  : "John Doe Updated",
//                        "email" : "<EMAIL>",
//                        "status": "Active"
//                ],
//                "CUSTOM-002": [
//                        "name"  : "Jane Smith Updated",
//                        "email" : "<EMAIL>",
//                        "status": "Active"
//                ]
//        ]
//
//        // Create db data list
//        def dbData1 = new ObjectData([_id: "CUSTOM-001", name: "John Doe", email: "<EMAIL>", phone: "1111111111", status: "Inactive", department: "Sales", describe_api_name: objectApiName])
//        def dbData2 = new ObjectData([_id: "CUSTOM-002", name: "Jane Smith", email: "<EMAIL>", phone: "2222222222", status: "Inactive", department: "HR", describe_api_name: objectApiName])
//        def dbDataList = [dbData1, dbData2]
//
//        // Create custom message and peer info
//        def peerName = "HR Manager"
//        def peerDisplayName = "人力资源经理"
//        def customMessage = "Annual performance review completed"
//
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithCustomMessage(user, eventType, actionType, objectDescribe, dataList, updatedFieldMap, dbDataList, peerName, peerDisplayName, customMessage)
//
//        then:
//        2 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.snapshot != null
//            assert info.snapshot.snapshot != null
//            // Verify updated fields are included in snapshot
//            assert info.snapshot.snapshot.name != null
//            assert info.snapshot.snapshot.email != null
//            assert info.snapshot.snapshot.status != null
//            // Verify peer info is set
//            assert info.peerName == peerName
//            assert info.snapshot.peerDisplayName == peerDisplayName
//            // Verify custom message is included in textMsg
//            assert info.snapshot.textMsg != null
//            assert info.snapshot.textMsg.size() == 3
//            assert info.snapshot.textMsg[0].text == "Custom Message Object" // object display name
//            assert info.snapshot.textMsg[1].text != null // data name
//            assert info.snapshot.textMsg[2].text == customMessage // custom message
//        }
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method (normal case)
//     * 测试第1404行的webSearchModifyRecordForMaster方法
//     */
//    def "testWebSearchModifyRecordForMasterNormalCase"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//        def sortField = "operationTime"
//        def sortType = 1 // 升序
//        def filters = [
//                new Filter(fieldName: "operationTime", comparison: 4, filterValue: "1000000000000"), // 大于等于
//                new Filter(fieldName: "operationTime", comparison: 6, filterValue: "2000000000000"), // 小于等于
//                new Filter(fieldName: "userId", comparison: 1, filterValue: "testUser")
//        ]
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo list
//        def mockLogInfos = [
//                createMockLogInfo("masterLog1", "MODIFY", module, "data1"),
//                createMockLogInfo("masterLog2", "ADD", module, "data2"),
//                createMockLogInfo("masterLog3", "DELETE", module, "data3")
//        ]
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 150
//        mockSearchResult.getTotalPage() >> 8
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> mockLogInfos
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(user, module, pageSize, pageNumber, filters, sortField, sortType)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 150
//        result.getPageInfo().getPageCount() == 8
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 3
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//            condition.getFromOperationTime() == 1000000000000L
//            condition.getToOperationTime() == 2000000000000L
//            condition.get("userId") == "testUser"
//            // Verify sort info
//            condition.getSortInfos() != null
//            condition.getSortInfos().size() == 1
//            condition.getSortInfos()[0].getField() == sortField
//            condition.getSortInfos()[0].isAsc() == true
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with null filters and sortField (normal case)
//     * 测试第1404行的webSearchModifyRecordForMaster方法，filters和sortField为null的情况
//     */
//    def "testWebSearchModifyRecordForMasterWithNullFiltersAndSort"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 50
//        mockSearchResult.getTotalPage() >> 3
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> []
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(user, module, pageSize, pageNumber, null, null, 0)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 50
//        result.getPageInfo().getPageCount() == 3
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 0
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//            // Should not have sort info when sortField is null
//            condition.getSortInfos() == null || condition.getSortInfos().isEmpty()
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with RestProxyInvokeException (normal case)
//     * 测试第1404行的webSearchModifyRecordForMaster方法抛出RestProxyInvokeException的情况
//     */
//    def "testWebSearchModifyRecordForMasterWithException"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy to throw exception
//        logServiceProxy.webSearch(_, _) >> { throw new RestProxyInvokeException("Test exception") }
//
//        when:
//        logService.webSearchModifyRecordForMaster(user, module, pageSize, pageNumber, null, null, 0)
//
//        then:
//        thrown(NotElementPresentException)
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with descending sort (normal case)
//     * 测试第1404行的webSearchModifyRecordForMaster方法，降序排序的情况
//     */
//    def "testWebSearchModifyRecordForMasterWithDescendingSort"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 30
//        def pageNumber = 2
//        def sortField = "userName"
//        def sortType = 2 // 降序
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 200
//        mockSearchResult.getTotalPage() >> 7
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("descLog1", "MODIFY", module, "data1"),
//                createMockLogInfo("descLog2", "ADD", module, "data2")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(user, module, pageSize, pageNumber, null, sortField, sortType)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 200
//        result.getPageInfo().getPageCount() == 7
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 2
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//            // Verify descending sort info
//            condition.getSortInfos() != null
//            condition.getSortInfos().size() == 1
//            condition.getSortInfos()[0].getField() == sortField
//            condition.getSortInfos()[0].isAsc() == false
//        })
//    }
//
//    /**
//     * Test getLogById method (normal case)
//     * 测试第1412行的getLogById方法
//     */
//    def "testGetLogByIdNormalCase"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "LOG-001"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo
//        def mockLogInfo = createMockLogInfo(logId, "MODIFY", "TestModule", "TestData")
//
//        // Mock logServiceProxy
//        logServiceProxy.searchDetail(_, _) >> [mockLogInfo]
//
//        when:
//        def result = logService.getLogById(apiName, logId, user)
//
//        then:
//        result != null
//        result.getLogId() == logId
//        result.getOperation() == "MODIFY"
//        result.getObjectName() == "TestModule"
//        result.getObjectId() == "TestData"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getLogId() == logId
//            condition.getObjectName() == apiName
//        })
//    }
//
//    /**
//     * Test getLogById method with null result (normal case)
//     * 测试第1412行的getLogById方法，返回结果为null的情况
//     */
//    def "testGetLogByIdWithNullResult"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "LOG-001"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy to return null
//        logServiceProxy.searchDetail(_, _) >> null
//
//        when:
//        logService.getLogById(apiName, logId, user)
//
//        then:
//        thrown(ValidateException)
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getLogId() == logId
//            condition.getObjectName() == apiName
//        })
//    }
//
//    /**
//     * Test getLogById method with empty result (normal case)
//     * 测试第1412行的getLogById方法，返回结果为空的情况
//     */
//    def "testGetLogByIdWithEmptyResult"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "LOG-001"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy to return empty list
//        logServiceProxy.searchDetail(_, _) >> []
//
//        when:
//        logService.getLogById(apiName, logId, user)
//
//        then:
//        thrown(ValidateException)
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getLogId() == logId
//            condition.getObjectName() == apiName
//        })
//    }
//
//    /**
//     * Test getLogByModule method (normal case)
//     * 测试第1432行的getLogByModule方法
//     */
//    def "testGetLogByModuleNormalCase"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo list
//        def mockLogInfos = [
//                createMockLogInfo("moduleLog1", "MODIFY", module, "data1"),
//                createMockLogInfo("moduleLog2", "ADD", module, "data2"),
//                createMockLogInfo("moduleLog3", "DELETE", module, "data3")
//        ]
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 150
//        mockSearchResult.getTotalPage() >> 8
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> mockLogInfos
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 150
//        result.getPageInfo().getPageCount() == 8
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 3
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getLogByModule method with empty results (normal case)
//     * 测试第1432行的getLogByModule方法，返回结果为空的情况
//     */
//    def "testGetLogByModuleWithEmptyResults"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result with empty results
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 0
//        mockSearchResult.getTotalPage() >> 0
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> []
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 0
//        result.getPageInfo().getPageCount() == 0
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 0
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getLogByModule method with null results (normal case)
//     * 测试第1432行的getLogByModule方法，返回结果为null的情况
//     */
//    def "testGetLogByModuleWithNullResults"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy to return null
//        logServiceProxy.webSearch(_, _) >> null
//
//        when:
//        logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        thrown(ValidateException)
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getManageLogList method (normal case)
//     * 测试第1452行的getManageLogList方法
//     */
//    def "testGetManageLogListNormalCase"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 25
//        def pageNumber = 3
//        def sortField = "operationTime"
//        def sortType = 1 // 升序
//        def filters = [
//                new Filter(fieldName: "operationTime", comparison: 4, filterValue: "1000000000000"), // 大于等于
//                new Filter(fieldName: "operationTime", comparison: 6, filterValue: "2000000000000"), // 小于等于
//                new Filter(fieldName: "userId", comparison: 1, filterValue: "testUser")
//        ]
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo list
//        def mockLogInfos = [
//                createMockLogInfo("manageLog1", "MODIFY", module, "data1"),
//                createMockLogInfo("manageLog2", "ADD", module, "data2"),
//                createMockLogInfo("manageLog3", "DELETE", module, "data3")
//        ]
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 150
//        mockSearchResult.getTotalPage() >> 6
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> mockLogInfos
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getManageLogList(user, module, pageSize, pageNumber, filters, sortField, sortType)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 150
//        result.getPageInfo().getPageCount() == 6
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 3
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//            condition.getFromOperationTime() == 1000000000000L
//            condition.getToOperationTime() == 2000000000000L
//            condition.get("userId") == "testUser"
//            // Verify sort info
//            condition.getSortInfos() != null
//            condition.getSortInfos().size() == 1
//            condition.getSortInfos()[0].getField() == sortField
//            condition.getSortInfos()[0].isAsc() == true
//        })
//    }
//
//    /**
//     * Test getManageLogList method with null filters and sortField (normal case)
//     * 测试第1452行的getManageLogList方法，filters和sortField为null的情况
//     */
//    def "testGetManageLogListWithNullFiltersAndSort"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 50
//        mockSearchResult.getTotalPage() >> 3
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> []
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getManageLogList(user, module, pageSize, pageNumber, null, null, 0)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 50
//        result.getPageInfo().getPageCount() == 3
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 0
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//            // Should not have sort info when sortField is null
//            condition.getSortInfos() == null || condition.getSortInfos().isEmpty()
//        })
//    }
//
//    /**
//     * Test getManageLogList method with RestProxyInvokeException (normal case)
//     * 测试第1452行的getManageLogList方法抛出RestProxyInvokeException的情况
//     */
//    def "testGetManageLogListWithException"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy to throw exception
//        logServiceProxy.webSearch(_, _) >> { throw new RestProxyInvokeException("Test exception") }
//
//        when:
//        logService.getManageLogList(user, module, pageSize, pageNumber, null, null, 0)
//
//        then:
//        thrown(NotElementPresentException)
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getManageLogList method with descending sort (normal case)
//     * 测试第1452行的getManageLogList方法，降序排序的情况
//     */
//    def "testGetManageLogListWithDescendingSort"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule"
//        def pageSize = 30
//        def pageNumber = 2
//        def sortField = "userName"
//        def sortType = 2 // 降序
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 200
//        mockSearchResult.getTotalPage() >> 7
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("descLog1", "MODIFY", module, "data1"),
//                createMockLogInfo("descLog2", "ADD", module, "data2")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getManageLogList(user, module, pageSize, pageNumber, null, sortField, sortType)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 200
//        result.getPageInfo().getPageCount() == 7
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 2
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getCorpId() == "testTenant"
//            condition.getAppId() == "crm"
//            condition.getPageSize() == pageSize
//            condition.getPageNum() == pageNumber
//            condition.getModule() == module
//            condition.getHidden() == false
//            // Verify descending sort info
//            condition.getSortInfos() != null
//            condition.getSortInfos().size() == 1
//            condition.getSortInfos()[0].getField() == sortField
//            condition.getSortInfos()[0].isAsc() == false
//        })
//    }
//
//    /**
//     * Helper method to create a mock LogInfo object
//     * 辅助方法，用于创建一个模拟的LogInfo对象
//     */
//    def createMockLogInfo(String logId, String operation, String module, String objectId) {
//        def mockLogInfo = Mock(LogInfo)
//        mockLogInfo.getLogId() >> logId
//        mockLogInfo.getOperation() >> operation
//        mockLogInfo.getObjectName() >> module
//        mockLogInfo.getObjectId() >> objectId
//        mockLogInfo
//    }
//
//    /**
//     * Test log method with complete parameter list (normal case)
//     * 测试第964行的log方法重载版本（带完整参数列表）
//     */
//    def "testLogWithCompleteParameterListNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "CompleteLogObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Complete Log Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"      : ["api_name": "name", "field_type": "TEXT"],
//                        "email"     : ["api_name": "email", "field_type": "TEXT"],
//                        "phone"     : ["api_name": "phone", "field_type": "TEXT"],
//                        "status"    : ["api_name": "status", "field_type": "TEXT"],
//                        "department": ["api_name": "department", "field_type": "TEXT"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create modify data
//        def modifyData = new ObjectData([
//                "id"        : "test123",
//                "name"      : "Updated Name",
//                "email"     : "<EMAIL>",
//                "phone"     : "1234567890",
//                "status"    : "Active",
//                "department": "Engineering"
//        ])
//
//        // Create updated field map
//        def updatedFieldMap = [
//                "name" : "Updated Name",
//                "email": "<EMAIL>",
//                "phone": "1234567890"
//        ]
//
//        // Create database data
//        def dbData = new ObjectData([
//                "id"        : "test123",
//                "name"      : "Original Name",
//                "email"     : "<EMAIL>",
//                "phone"     : "0987654321",
//                "status"    : "Inactive",
//                "department": "Sales"
//        ])
//
//        // Create peer information
//        def peerName = "TestPeer"
//        def peerDisplayName = "Test Peer Display"
//        def internationalPeerDisplayName = Mock(InternationalItem)
//        def bizId = "testBizId"
//        def extendsInfo = [
//                "customField1": "customValue1",
//                "customField2": "customValue2"
//        ]
//
//        // Mock fillFieldInfoHandle
//        fillFieldInfoHandle.asyncFillFieldInfo(_, _, _) >> { args ->
//            // Mock implementation
//        }
//
//        when:
//        logService.log(user, eventType, actionType, objectDescribe, modifyData, updatedFieldMap, dbData, peerName, peerDisplayName, internationalPeerDisplayName, bizId, extendsInfo)
//
//        then:
//        // Verify that asyncLogSender.offer is called once
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.tenantId == "testTenant"
//            assert info.userId == user.id
//            assert info.userName == user.name
//            assert info.objectName == objectApiName
//            assert info.objectId == "test123"
//            assert info.actionType == actionType
//            assert info.bizId == bizId
//            assert info.peerName == peerName
//            assert info.snapshot.peerDisplayName == peerDisplayName
//            assert info.snapshot.internationalPeerDisplayName == internationalPeerDisplayName
//
//            // Verify snapshot contains updated fields
//            assert info.snapshot.snapshot.name == "Updated Name"
//            assert info.snapshot.snapshot.email == "<EMAIL>"
//            assert info.snapshot.snapshot.phone == "1234567890"
//
//            // Verify extendsInfo is included
//            assert info.snapshot.snapshot.customField1 == "customValue1"
//            assert info.snapshot.snapshot.customField2 == "customValue2"
//        }
//    }
//
//    /**
//     * Test logByActionType method (normal case)
//     * 测试第987行的logByActionType方法
//     */
//    def "testLogByActionTypeNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.AddEmployee
//        def objectApiName = "TeamMemberObject"
//
//        // Create object describe
//        def describeMap = [
//                "api_name"    : objectApiName,
//                "display_name": "Team Member Object",
//                "object_type" : "OBJECT",
//                "fields"      : [
//                        "name"      : ["api_name": "name", "field_type": "TEXT"],
//                        "email"     : ["api_name": "email", "field_type": "TEXT"],
//                        "role"      : ["api_name": "role", "field_type": "TEXT"],
//                        "department": ["api_name": "department", "field_type": "TEXT"]
//                ],
//                "tenant_id"   : "testTenant"
//        ]
//        def objectDescribe = new ObjectDescribe(describeMap)
//
//        // Create old data list (existing team members)
//        def oldDataList = [
//                new ObjectData([
//                        "id"        : "old1",
//                        "name"      : "Old Member 1",
//                        "email"     : "<EMAIL>",
//                        "role"      : "Developer",
//                        "department": "Engineering"
//                ]),
//                new ObjectData([
//                        "id"        : "old2",
//                        "name"      : "Old Member 2",
//                        "email"     : "<EMAIL>",
//                        "role"      : "Designer",
//                        "department": "Design"
//                ])
//        ]
//
//        // Create new data list (updated team members)
//        def dataList = [
//                new ObjectData([
//                        "id"        : "new1",
//                        "name"      : "New Member 1",
//                        "email"     : "<EMAIL>",
//                        "role"      : "Senior Developer",
//                        "department": "Engineering"
//                ]),
//                new ObjectData([
//                        "id"        : "new2",
//                        "name"      : "New Member 2",
//                        "email"     : "<EMAIL>",
//                        "role"      : "Product Manager",
//                        "department": "Product"
//                ])
//        ]
//
//        // Mock fillFieldInfoHandle
//        fillFieldInfoHandle.asyncFillFieldInfo(_, _, _) >> { args ->
//            // Mock implementation
//        }
//
//        when:
//        logService.logByActionType(user, eventType, actionType, oldDataList, dataList, objectDescribe)
//
//        then:
//        // Verify that logTeamMember method is called for AddEmployee action type
//        // The method should process team member changes and log them
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.tenantId == "testTenant"
//            assert info.userId == user.id
//            assert info.userName == user.name
//            assert info.objectName == objectApiName
//            assert info.actionType == actionType
//        }
//    }
//
//    /**
//     * Test logWithUpdateLayout method with InternationalItem parameter (normal case)
//     * 测试第1006行的logWithUpdateLayout方法（带InternationalItem参数）
//     */
//    def "testLogWithUpdateLayoutWithInternationalItemNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "LayoutUpdateObject"
//        def textMessage = "Layout updated successfully"
//        def internationalTextMessage = Mock(InternationalItem)
//
//        // Create before layout (original layout)
//        def beforeLayout = Mock(ILayout)
//        def beforeLayoutMap = [
//                "field1": "value1",
//                "field2": "value2",
//                "field3": "value3"
//        ]
//
//        // Create after layout (updated layout)
//        def afterLayout = Mock(ILayout)
//        def afterLayoutMap = [
//                "field1": "updatedValue1",
//                "field2": "value2",
//                "field4": "newValue4"
//        ]
//
//        // Mock LayoutExt.toMap() for before layout
//        LayoutExt.of(beforeLayout).toMap() >> beforeLayoutMap
//
//        // Mock LayoutExt.toMap() for after layout
//        LayoutExt.of(afterLayout).toMap() >> afterLayoutMap
//
//        when:
//        logService.logWithUpdateLayout(user, eventType, actionType, beforeLayout, afterLayout, objectApiName, textMessage, internationalTextMessage)
//
//        then:
//        // Verify that asyncLogSender.offer is called once
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.tenantId == user.tenantId
//            assert info.userId == user.id
//            assert info.userName == user.name
//            assert info.objectName == objectApiName
//            assert info.actionType == actionType
//            assert info.textMessage == textMessage
//            assert info.internationalTextMessage == internationalTextMessage
//
//            // Verify snapshot contains text message with international item
//            assert info.snapshot.textMsg.size() == 1
//            assert info.snapshot.textMsg[0].text == textMessage
//            assert info.snapshot.textMsg[0].internationalTextMessage == internationalTextMessage
//
//            // Verify diff data contains layout changes
//            assert info.snapshot.diffData.size() == 3
//
//            // Verify field1 was updated
//            def field1Diff = info.snapshot.diffData.find { it.field == "field1" }
//            assert field1Diff != null
//            assert field1Diff.before == "value1"
//            assert field1Diff.after == "updatedValue1"
//
//            // Verify field3 was removed
//            def field3Diff = info.snapshot.diffData.find { it.field == "field3" }
//            assert field3Diff != null
//            assert field3Diff.before == "value3"
//            assert field3Diff.after == null
//
//            // Verify field4 was added
//            def field4Diff = info.snapshot.diffData.find { it.field == "field4" }
//            assert field4Diff != null
//            assert field4Diff.before == null
//            assert field4Diff.after == "newValue4"
//        }
//    }
//
//    /**
//     * Test logWithUpdateLayout method (normal case)
//     * 测试第1047行的logWithUpdateLayout方法
//     */
//    def "testLogWithUpdateLayoutNormalCase"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.Modify
//        def objectApiName = "LayoutUpdateObject"
//        def textMessage = "Layout updated successfully"
//
//        // Create before layout (original layout)
//        def beforeLayout = Mock(ILayout)
//        def beforeLayoutMap = [
//                "field1": "value1",
//                "field2": "value2",
//                "field3": "value3"
//        ]
//
//        // Create after layout (updated layout)
//        def afterLayout = Mock(ILayout)
//        def afterLayoutMap = [
//                "field1": "updatedValue1",
//                "field2": "value2",
//                "field4": "newValue4"
//        ]
//
//        // Mock LayoutExt.toMap() for before layout
//        LayoutExt.of(beforeLayout).toMap() >> beforeLayoutMap
//
//        // Mock LayoutExt.toMap() for after layout
//        LayoutExt.of(afterLayout).toMap() >> afterLayoutMap
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock orgService.fillUserName
//        orgService.fillUserName(user) >> user
//
//        when:
//        logService.logWithUpdateLayout(user, eventType, actionType, beforeLayout, afterLayout, objectApiName, textMessage)
//
//        then:
//        // Verify that asyncLogSender.offer is called once
//        1 * asyncLogSender.offer(_) >> { LogInfo info ->
//            assert info.corpId == "testTenant"
//            assert info.userId == "testUser"
//            assert info.userName == "Test User"
//            assert info.objectName == objectApiName
//            assert info.textMessage == textMessage
//            assert info.operation == eventType.getId()
//            assert info.bizOperationName == actionType.getId()
//            assert info.module == "UserDefineObj"
//
//            // Verify snapshot contains text message
//            assert info.snapshot.textMsg.size() == 1
//            assert info.snapshot.textMsg[0].text == textMessage
//            assert info.snapshot.textMsg[0].objectApiName == objectApiName
//
//            // Verify diff data contains layout changes
//            assert info.snapshot.diffData.size() == 3
//
//            // Verify field1 was updated
//            def field1Diff = info.snapshot.diffData.find { it.field == "field1" }
//            assert field1Diff != null
//            assert field1Diff.before == "value1"
//            assert field1Diff.after == "updatedValue1"
//
//            // Verify field3 was removed
//            def field3Diff = info.snapshot.diffData.find { it.field == "field3" }
//            assert field3Diff != null
//            assert field3Diff.before == "value3"
//            assert field3Diff.after == null
//
//            // Verify field4 was added
//            def field4Diff = info.snapshot.diffData.find { it.field == "field4" }
//            assert field4Diff != null
//            assert field4Diff.before == null
//            assert field4Diff.after == "newValue4"
//        }
//    }
//
//    /**
//     * Test mobSearchModifyRecord method (normal case)
//     * 测试第1090行的mobSearchModifyRecord方法
//     */
//    def "testMobSearchModifyRecordNormalCase"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def dataId = "test123"
//        def limit = 10
//        def operationTime = System.currentTimeMillis()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 5
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                new LogInfo(
//                        logId: "log1",
//                        operationTime: System.currentTimeMillis(),
//                        bizOperationName: "ADD",
//                        corpId: "testTenant",
//                        userId: "testUser",
//                        userName: "Test User",
//                        objectName: apiName,
//                        objectId: dataId
//                ),
//                new LogInfo(
//                        logId: "log2",
//                        operationTime: System.currentTimeMillis() - 1000,
//                        bizOperationName: "MODIFY",
//                        corpId: "testTenant",
//                        userId: "testUser",
//                        userName: "Test User",
//                        objectName: apiName,
//                        objectId: dataId
//                )
//        ]
//
//        // Mock logServiceProxy.mobSearch
//        logServiceProxy.mobSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecord(apiName, dataId, limit, operationTime, user)
//
//        then:
//        // Verify the result
//        assert result != null
//        assert result.totalCount == 5
//        assert result.hasMore == false
//        assert result.modifyRecordList != null
//        assert result.modifyRecordList.size() == 2
//
//        // Verify the first modify record
//        def firstRecord = result.modifyRecordList[0]
//        assert firstRecord.logID == "log1"
//        assert firstRecord.operationType == "ADD"
//        assert firstRecord.logInfo.objectName == apiName
//        assert firstRecord.logInfo.objectId == dataId
//
//        // Verify the second modify record
//        def secondRecord = result.modifyRecordList[1]
//        assert secondRecord.logID == "log2"
//        assert secondRecord.operationType == "MODIFY"
//        assert secondRecord.logInfo.objectName == apiName
//        assert secondRecord.logInfo.objectId == dataId
//
//        // Verify logServiceProxy.mobSearch was called with correct parameters
//        1 * logServiceProxy.mobSearch(_, _) >> { Map<String, String> header, SearchModel.Arg arg ->
//            assert header["x-fs-ei"] == "testTenant"
//            assert header["FSR-GRAY_VALUE"] == "testTenant"
//            assert arg.condition.appId == "CRM"
//            assert arg.condition.module == apiName
//            assert arg.condition.lastOperationTime == operationTime
//            assert arg.condition.pageSize == limit
//            assert arg.condition.corpId == "testTenant"
//            assert arg.condition.objectId == dataId
//            assert arg.condition.hidden == false
//            return mockSearchResult
//        }
//    }
//
//    /**
//     * Test mobSearchModifyRecord method with LogCondition (normal case)
//     * 测试第1095行的mobSearchModifyRecord(User user, LogCondition condition)方法
//     */
//    def "testMobSearchModifyRecordWithLogConditionNormalCase"() {
//        given:
//        def user = Mock(User)
//        def logCondition = Mock(LogCondition)
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogCondition properties
//        logCondition.getObjectId() >> "test123"
//        logCondition.getBizIds() >> ["biz1", "biz2"]
//        logCondition.getOtherBizIds() >> null
//        logCondition.getOperationalType() >> "MODIFY"
//        logCondition.getModule() >> "TestObject"
//        logCondition.getPageNumber() >> 1
//        logCondition.getPageSize() >> 10
//        logCondition.getOperationTimeFrom() >> 1000L
//        logCondition.getOperationTimeTo() >> 2000L
//        logCondition.getOperationTime() >> 1500L
//        logCondition.getNeedReturnCount() >> true
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 3
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                Mock(LogInfo) {
//                    getLogId() >> "log1"
//                    getObjectId() >> "obj1"
//                    getObjectName() >> "TestObject"
//                    getBizOperationName() >> "MODIFY"
//                    getOperationTime() >> 1500L
//                    getUserId() >> "user1"
//                    getUserName() >> "User One"
//                    getTextMessage() >> "Modified test object"
//                },
//                Mock(LogInfo) {
//                    getLogId() >> "log2"
//                    getObjectId() >> "obj2"
//                    getObjectName() >> "TestObject"
//                    getBizOperationName() >> "ADD"
//                    getOperationTime() >> 1400L
//                    getUserId() >> "user2"
//                    getUserName() >> "User Two"
//                    getTextMessage() >> "Added test object"
//                },
//                Mock(LogInfo) {
//                    getLogId() >> "log3"
//                    getObjectId() >> "obj3"
//                    getObjectName() >> "TestObject"
//                    getBizOperationName() >> "DELETE"
//                    getOperationTime() >> 1300L
//                    getUserId() >> "user3"
//                    getUserName() >> "User Three"
//                    getTextMessage() >> "Deleted test object"
//                }
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.mobSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecord(user, logCondition)
//
//        then:
//        result != null
//        result.getTotalCount() == 3
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 3
//
//        // Verify the first modify record
//        def firstRecord = result.getModifyRecordList().get(0)
//        firstRecord.getLogID() == "log1"
//        firstRecord.getObjectId() == "obj1"
//        firstRecord.getObjectName() == "TestObject"
//        firstRecord.getOperationType() == "MODIFY"
//
//        // Verify the second modify record
//        def secondRecord = result.getModifyRecordList().get(1)
//        secondRecord.getLogID() == "log2"
//        secondRecord.getObjectId() == "obj2"
//        secondRecord.getObjectName() == "TestObject"
//        secondRecord.getOperationType() == "ADD"
//
//        // Verify the third modify record
//        def thirdRecord = result.getModifyRecordList().get(2)
//        thirdRecord.getLogID() == "log3"
//        thirdRecord.getObjectId() == "obj3"
//        thirdRecord.getObjectName() == "TestObject"
//        thirdRecord.getOperationType() == "DELETE"
//
//        // Verify that logServiceProxy was called with correct parameters
//        1 * logServiceProxy.mobSearch({ Map<String, String> header ->
//            header.get("x-fs-ei") == "testTenant" &&
//                    header.get("FSR-GRAY_VALUE") == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getObjectId() == "test123" &&
//                    arg.getCondition().getBizId() == ["biz1", "biz2"] &&
//                    arg.getCondition().getOtherBizId() == null &&
//                    arg.getCondition().getModule() == "TestObject" &&
//                    arg.getCondition().getCorpId() == "testTenant" &&
//                    arg.getCondition().getOperationType() == "MODIFY" &&
//                    arg.getCondition().getPageNum() == 1 &&
//                    arg.getCondition().getPageSize() == 10 &&
//                    arg.getCondition().getFromOperationTime() == 1000L &&
//                    arg.getCondition().getToOperationTime() == 2000L &&
//                    arg.getCondition().getLastOperationTime() == 1500L &&
//                    arg.getCondition().getNeedReturnCount() == true
//        })
//    }
//
//    /**
//     * Test mobSearchModifyRecordForMaster method with basic parameters (normal case)
//     * 测试第1109行的mobSearchModifyRecordForMaster(String masterId, String detailApiName, int limit, long operationTime, User user)方法
//     */
//    def "testMobSearchModifyRecordForMasterBasicParamsNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterId = "master123"
//        def detailApiName = "DetailObject"
//        def limit = 20
//        def operationTime = System.currentTimeMillis()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 8
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("log1", "MODIFY", "DetailObject", "detail1"),
//                createMockLogInfo("log2", "ADD", "DetailObject", "detail2"),
//                createMockLogInfo("log3", "DELETE", "DetailObject", "detail3")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.mobSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecordForMaster(masterId, detailApiName, limit, operationTime, user)
//
//        then:
//        result != null
//        result.getTotalCount() == 8
//        result.getHasMore() == true
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "log1"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "DetailObject"
//        modifyRecords[0].getObjectId() == "detail1"
//
//        modifyRecords[1].getLogID() == "log2"
//        modifyRecords[1].getOperationType() == "ADD"
//        modifyRecords[1].getObjectName() == "DetailObject"
//        modifyRecords[1].getObjectId() == "detail2"
//
//        modifyRecords[2].getLogID() == "log3"
//        modifyRecords[2].getOperationType() == "DELETE"
//        modifyRecords[2].getObjectName() == "DetailObject"
//        modifyRecords[2].getObjectId() == "detail3"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.mobSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == masterId
//            arg.getCondition().getModule() == detailApiName
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getLastOperationTime() == operationTime
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test mobSearchModifyRecordForMaster method with operationType parameter (normal case)
//     * 测试第1121行的mobSearchModifyRecordForMaster(String masterId, String detailApiName, String operationType, int limit, long operationTime, User user)方法
//     */
//    def "testMobSearchModifyRecordForMasterWithOperationTypeNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterId = "master456"
//        def detailApiName = "DetailObject2"
//        def operationType = "MODIFY"
//        def limit = 15
//        def operationTime = System.currentTimeMillis()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 12
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("log4", "MODIFY", "DetailObject2", "detail4"),
//                createMockLogInfo("log5", "MODIFY", "DetailObject2", "detail5"),
//                createMockLogInfo("log6", "MODIFY", "DetailObject2", "detail6")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.mobSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecordForMaster(masterId, detailApiName, operationType, limit, operationTime, user)
//
//        then:
//        result != null
//        result.getTotalCount() == 12
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "log4"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "DetailObject2"
//        modifyRecords[0].getObjectId() == "detail4"
//
//        modifyRecords[1].getLogID() == "log5"
//        modifyRecords[1].getOperationType() == "MODIFY"
//        modifyRecords[1].getObjectName() == "DetailObject2"
//        modifyRecords[1].getObjectId() == "detail5"
//
//        modifyRecords[2].getLogID() == "log6"
//        modifyRecords[2].getOperationType() == "MODIFY"
//        modifyRecords[2].getObjectName() == "DetailObject2"
//        modifyRecords[2].getObjectId() == "detail6"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.mobSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == masterId
//            arg.getCondition().getModule() == detailApiName
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getLastOperationTime() == operationTime
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == operationType
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test mobSearchModifyRecordForMaster method with masterLogId and bizId parameters (normal case)
//     * 测试第1126行的mobSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationType, User user, List<String> bizId, List<String> otherBizIds)方法
//     */
//    def "testMobSearchModifyRecordForMasterWithMasterLogIdAndBizIdNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterId = "master789"
//        def detailApiName = "DetailObject3"
//        def masterLogId = "masterLog123"
//        def operationType = "ADD"
//        def bizId = ["biz1", "biz2"]
//        def otherBizIds = ["otherBiz1"]
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 6
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("log7", "ADD", "DetailObject3", "detail7"),
//                createMockLogInfo("log8", "ADD", "DetailObject3", "detail8")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.searchDetail(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecordForMaster(masterId, detailApiName, masterLogId, operationType, user, bizId, otherBizIds)
//
//        then:
//        result != null
//        result.getTotalCount() == 6
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 2
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "log7"
//        modifyRecords[0].getOperationType() == "ADD"
//        modifyRecords[0].getObjectName() == "DetailObject3"
//        modifyRecords[0].getObjectId() == "detail7"
//
//        modifyRecords[1].getLogID() == "log8"
//        modifyRecords[1].getOperationType() == "ADD"
//        modifyRecords[1].getObjectName() == "DetailObject3"
//        modifyRecords[1].getObjectId() == "detail8"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == masterId
//            arg.getCondition().getModule() == detailApiName
//            arg.getCondition().getMasterLogId() == masterLogId
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == operationType
//            arg.getCondition().getBizId() == bizId
//            arg.getCondition().getOtherBizId() == otherBizIds
//        })
//    }
//
//    /**
//     * Test mobSearchModifyRecordForMaster method with all parameters including limit and operationTime (normal case)
//     * 测试第1133行的mobSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationType, User user, List<String> bizId, List<String> otherBizIds, int limit, long operationTime)方法
//     */
//    def "testMobSearchModifyRecordForMasterWithAllParamsNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterId = "master999"
//        def detailApiName = "DetailObject4"
//        def masterLogId = "masterLog456"
//        def operationType = "DELETE"
//        def bizId = ["biz3", "biz4"]
//        def otherBizIds = ["otherBiz2", "otherBiz3"]
//        def limit = 25
//        def operationTime = System.currentTimeMillis()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 18
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("log9", "DELETE", "DetailObject4", "detail9"),
//                createMockLogInfo("log10", "DELETE", "DetailObject4", "detail10"),
//                createMockLogInfo("log11", "DELETE", "DetailObject4", "detail11")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.mobSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecordForMaster(masterId, detailApiName, masterLogId, operationType, user, bizId, otherBizIds, limit, operationTime)
//
//        then:
//        result != null
//        result.getTotalCount() == 18
//        result.getHasMore() == true
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "log9"
//        modifyRecords[0].getOperationType() == "DELETE"
//        modifyRecords[0].getObjectName() == "DetailObject4"
//        modifyRecords[0].getObjectId() == "detail9"
//
//        modifyRecords[1].getLogID() == "log10"
//        modifyRecords[1].getOperationType() == "DELETE"
//        modifyRecords[1].getObjectName() == "DetailObject4"
//        modifyRecords[1].getObjectId() == "detail10"
//
//        modifyRecords[2].getLogID() == "log11"
//        modifyRecords[2].getOperationType() == "DELETE"
//        modifyRecords[2].getObjectName() == "DetailObject4"
//        modifyRecords[2].getObjectId() == "detail11"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.mobSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == masterId
//            arg.getCondition().getModule() == detailApiName
//            arg.getCondition().getMasterLogId() == masterLogId
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getLastOperationTime() == operationTime
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == operationType
//            arg.getCondition().getBizId() == bizId
//            arg.getCondition().getOtherBizId() == otherBizIds
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test mobSearchModifyRecordForMaster method with LogCondition parameter (normal case)
//     * 测试第1147行的mobSearchModifyRecordForMaster(User user, LogCondition condition)方法
//     */
//    def "testMobSearchModifyRecordForMasterWithLogConditionNormalCase"() {
//        given:
//        def user = Mock(User)
//        def logCondition = Mock(LogCondition)
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogCondition properties
//        logCondition.getMasterId() >> "masterCondition123"
//        logCondition.getMasterLogId() >> "masterLogCondition789"
//        logCondition.getOperationalType() >> "MODIFY"
//        logCondition.getModule() >> "ConditionObject"
//        logCondition.getBizIds() >> ["conditionBiz1", "conditionBiz2"]
//        logCondition.getOtherBizIds() >> ["conditionOtherBiz1"]
//        logCondition.getPageNumber() >> 2
//        logCondition.getPageSize() >> 30
//        logCondition.getOperationTimeFrom() >> System.currentTimeMillis() - 86400000 // 1 day ago
//        logCondition.getOperationTimeTo() >> System.currentTimeMillis()
//        logCondition.getOperationTime() >> System.currentTimeMillis() - 43200000 // 12 hours ago
//        logCondition.getNeedReturnCount() >> true
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 15
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("log12", "MODIFY", "ConditionObject", "conditionDetail1"),
//                createMockLogInfo("log13", "MODIFY", "ConditionObject", "conditionDetail2"),
//                createMockLogInfo("log14", "MODIFY", "ConditionObject", "conditionDetail3")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.mobSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.mobSearchModifyRecordForMaster(user, logCondition)
//
//        then:
//        result != null
//        result.getTotalCount() == 15
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "log12"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "ConditionObject"
//        modifyRecords[0].getObjectId() == "conditionDetail1"
//
//        modifyRecords[1].getLogID() == "log13"
//        modifyRecords[1].getOperationType() == "MODIFY"
//        modifyRecords[1].getObjectName() == "ConditionObject"
//        modifyRecords[1].getObjectId() == "conditionDetail2"
//
//        modifyRecords[2].getLogID() == "log14"
//        modifyRecords[2].getOperationType() == "MODIFY"
//        modifyRecords[2].getObjectName() == "ConditionObject"
//        modifyRecords[2].getObjectId() == "conditionDetail3"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.mobSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == "masterCondition123"
//            arg.getCondition().getMasterLogId() == "masterLogCondition789"
//            arg.getCondition().getModule() == "ConditionObject"
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == "MODIFY"
//            arg.getCondition().getBizId() == ["conditionBiz1", "conditionBiz2"]
//            arg.getCondition().getOtherBizId() == ["conditionOtherBiz1"]
//            arg.getCondition().getHidden() == false
//            arg.getCondition().getPageNum() == 2
//            arg.getCondition().getPageSize() == 30
//            arg.getCondition().getFromOperationTime() == logCondition.getOperationTimeFrom()
//            arg.getCondition().getToOperationTime() == logCondition.getOperationTimeTo()
//            arg.getCondition().getLastOperationTime() == logCondition.getOperationTime()
//            arg.getCondition().getNeedReturnCount() == true
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with LogCondition parameter (normal case)
//     * 测试第1404行的webSearchModifyRecordForMaster(User user, LogCondition condition)方法
//     */
//    def "testWebSearchModifyRecordForMasterWithLogConditionLine1404"() {
//        given:
//        def user = Mock(User)
//        def logCondition = Mock(LogCondition)
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogCondition properties
//        logCondition.getMasterId() >> "masterCondition123"
//        logCondition.getMasterLogId() >> "masterLogCondition789"
//        logCondition.getOperationalType() >> "MODIFY"
//        logCondition.getModule() >> "ConditionObject"
//        logCondition.getBizIds() >> ["conditionBiz1", "conditionBiz2"]
//        logCondition.getOtherBizIds() >> ["conditionOtherBiz1"]
//        logCondition.getPageNumber() >> 2
//        logCondition.getPageSize() >> 30
//        logCondition.getOperationTimeFrom() >> System.currentTimeMillis() - 86400000 // 1 day ago
//        logCondition.getOperationTimeTo() >> System.currentTimeMillis()
//        logCondition.getOperationTime() >> System.currentTimeMillis() - 43200000 // 12 hours ago
//        logCondition.getNeedReturnCount() >> true
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 15
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("log12", "MODIFY", "ConditionObject", "conditionDetail1"),
//                createMockLogInfo("log13", "MODIFY", "ConditionObject", "conditionDetail2"),
//                createMockLogInfo("log14", "MODIFY", "ConditionObject", "conditionDetail3")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.searchDetail(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(user, logCondition)
//
//        then:
//        result != null
//        result.getTotalCount() == 15
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "log12"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "ConditionObject"
//        modifyRecords[0].getObjectId() == "conditionDetail1"
//
//        modifyRecords[1].getLogID() == "log13"
//        modifyRecords[1].getOperationType() == "MODIFY"
//        modifyRecords[1].getObjectName() == "ConditionObject"
//        modifyRecords[1].getObjectId() == "conditionDetail2"
//
//        modifyRecords[2].getLogID() == "log14"
//        modifyRecords[2].getOperationType() == "MODIFY"
//        modifyRecords[2].getObjectName() == "ConditionObject"
//        modifyRecords[2].getObjectId() == "conditionDetail3"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == "masterCondition123"
//            arg.getCondition().getMasterLogId() == "masterLogCondition789"
//            arg.getCondition().getModule() == "ConditionObject"
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == "MODIFY"
//            arg.getCondition().getBizId() == ["conditionBiz1", "conditionBiz2"]
//            arg.getCondition().getOtherBizId() == ["conditionOtherBiz1"]
//            arg.getCondition().getHidden() == false
//            arg.getCondition().getPageNum() == 2
//            arg.getCondition().getPageSize() == 30
//            arg.getCondition().getFromOperationTime() == logCondition.getOperationTimeFrom()
//            arg.getCondition().getToOperationTime() == logCondition.getOperationTimeTo()
//            arg.getCondition().getLastOperationTime() == logCondition.getOperationTime()
//            arg.getCondition().getNeedReturnCount() == true
//        })
//    }
//
//    /**
//     * Test getLogByModule method with null results (line 1432)
//     * 测试第1432行的getLogByModule方法返回null结果的情况
//     */
//    def "testGetLogByModuleLine1432WithNullResults"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule1432"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result with null results
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 0
//        mockSearchResult.getTotalPage() >> 0
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> null
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 0
//        result.getPageInfo().getPageCount() == 0
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getLogInfos() != null
//        result.getLogInfos().size() == 0
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getAppId() == "crm"
//            condition.getModule() == module
//            condition.getPageNum() == pageNumber
//            condition.getPageSize() == pageSize
//            condition.getCorpId() == "testTenant"
//            condition.getHidden() == false
//        })
//    }
//
//
//    /**
//     * Test mobSearchModifyRecordForMaster method with master and detail objects (normal case)
//     * 测试第1157行的mobSearchModifyRecordForMaster(String masterId, String masterApiName, List<String> detailApiNameList, int limit, long operationTime, User user)方法
//     */
//    def "testMobSearchModifyRecordForMasterWithMasterAndDetailObjectsNormalCase"() {
//        given:
//        def user = Mock(User)
//        def masterId = "masterRich123"
//        def masterApiName = "MasterObject"
//        def detailApiNameList = ["DetailObject1", "DetailObject2", "DetailObject3"]
//        def limit = 35
//        def operationTime = System.currentTimeMillis()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result for master object
//        def mockMasterSearchResult = Mock(SearchModel.Result)
//        mockMasterSearchResult.getTotalCount() >> 8
//        mockMasterSearchResult.getHasMore() >> false
//        mockMasterSearchResult.getResults() >> [
//                createMockLogInfo("masterLog1", "MODIFY", "MasterObject", "masterRich123"),
//                createMockLogInfo("masterLog2", "ADD", "MasterObject", "masterRich123")
//        ]
//
//        // Mock SearchModel.Result for detail objects
//        def mockDetail1SearchResult = Mock(SearchModel.Result)
//        mockDetail1SearchResult.getTotalCount() >> 5
//        mockDetail1SearchResult.getHasMore() >> false
//        mockDetail1SearchResult.getResults() >> [
//                createMockLogInfo("detail1Log1", "MODIFY", "DetailObject1", "detail1"),
//                createMockLogInfo("detail1Log2", "DELETE", "DetailObject1", "detail2")
//        ]
//
//        def mockDetail2SearchResult = Mock(SearchModel.Result)
//        mockDetail2SearchResult.getTotalCount() >> 3
//        mockDetail2SearchResult.getHasMore() >> false
//        mockDetail2SearchResult.getResults() >> [
//                createMockLogInfo("detail2Log1", "ADD", "DetailObject2", "detail3")
//        ]
//
//        def mockDetail3SearchResult = Mock(SearchModel.Result)
//        mockDetail3SearchResult.getTotalCount() >> 7
//        mockDetail3SearchResult.getHasMore() >> true
//        mockDetail3SearchResult.getResults() >> [
//                createMockLogInfo("detail3Log1", "MODIFY", "DetailObject3", "detail4"),
//                createMockLogInfo("detail3Log2", "ADD", "DetailObject3", "detail5"),
//                createMockLogInfo("detail3Log3", "DELETE", "DetailObject3", "detail6")
//        ]
//
//        // Mock logServiceProxy for multiple calls
//        logServiceProxy.mobSearch(_, _) >>> [mockMasterSearchResult, mockDetail1SearchResult, mockDetail2SearchResult, mockDetail3SearchResult]
//
//        when:
//        def result = logService.mobSearchModifyRecordForMaster(masterId, masterApiName, detailApiNameList, limit, operationTime, user)
//
//        then:
//        result != null
//        result.getTotalCountMap() != null
//        result.getAllModifyRecordListMap() != null
//
//        // Verify master object results
//        result.getTotalCountMap().get("MasterObject") == 8
//        def masterRecords = result.getAllModifyRecordListMap().get("MasterObject")
//        masterRecords.size() == 2
//        masterRecords[0].getLogID() == "masterLog1"
//        masterRecords[0].getOperationType() == "MODIFY"
//        masterRecords[0].getObjectName() == "MasterObject"
//        masterRecords[0].getObjectId() == "masterRich123"
//
//        masterRecords[1].getLogID() == "masterLog2"
//        masterRecords[1].getOperationType() == "ADD"
//        masterRecords[1].getObjectName() == "MasterObject"
//        masterRecords[1].getObjectId() == "masterRich123"
//
//        // Verify detail object 1 results
//        result.getTotalCountMap().get("DetailObject1") == 5
//        def detail1Records = result.getAllModifyRecordListMap().get("DetailObject1")
//        detail1Records.size() == 2
//        detail1Records[0].getLogID() == "detail1Log1"
//        detail1Records[0].getOperationType() == "MODIFY"
//        detail1Records[0].getObjectName() == "DetailObject1"
//        detail1Records[0].getObjectId() == "detail1"
//
//        detail1Records[1].getLogID() == "detail1Log2"
//        detail1Records[1].getOperationType() == "DELETE"
//        detail1Records[1].getObjectName() == "DetailObject1"
//        detail1Records[1].getObjectId() == "detail2"
//
//        // Verify detail object 2 results
//        result.getTotalCountMap().get("DetailObject2") == 3
//        def detail2Records = result.getAllModifyRecordListMap().get("DetailObject2")
//        detail2Records.size() == 1
//        detail2Records[0].getLogID() == "detail2Log1"
//        detail2Records[0].getOperationType() == "ADD"
//        detail2Records[0].getObjectName() == "DetailObject2"
//        detail2Records[0].getObjectId() == "detail3"
//
//        // Verify detail object 3 results
//        result.getTotalCountMap().get("DetailObject3") == 7
//        def detail3Records = result.getAllModifyRecordListMap().get("DetailObject3")
//        detail3Records.size() == 3
//        detail3Records[0].getLogID() == "detail3Log1"
//        detail3Records[0].getOperationType() == "MODIFY"
//        detail3Records[0].getObjectName() == "DetailObject3"
//        detail3Records[0].getObjectId() == "detail4"
//
//        detail3Records[1].getLogID() == "detail3Log2"
//        detail3Records[1].getOperationType() == "ADD"
//        detail3Records[1].getObjectName() == "DetailObject3"
//        detail3Records[1].getObjectId() == "detail5"
//
//        detail3Records[2].getLogID() == "detail3Log3"
//        detail3Records[2].getOperationType() == "DELETE"
//        detail3Records[2].getObjectName() == "DetailObject3"
//        detail3Records[2].getObjectId() == "detail6"
//
//        // Verify proxy call parameters (4 calls total: 1 for master + 3 for details)
//        4 * logServiceProxy.mobSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getLastOperationTime() == operationTime
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecord method with basic parameters (normal case)
//     * 测试第1183行的webSearchModifyRecord(String apiName, String dataId, int limit, int pageNumber, User user)方法
//     */
//    def "testWebSearchModifyRecordBasicParamsNormalCase"() {
//        given:
//        def user = Mock(User)
//        def apiName = "WebObject"
//        def dataId = "webData123"
//        def limit = 40
//        def pageNumber = 3
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 120
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("webLog1", "MODIFY", "WebObject", "webData123"),
//                createMockLogInfo("webLog2", "ADD", "WebObject", "webData123"),
//                createMockLogInfo("webLog3", "DELETE", "WebObject", "webData123")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.modifyLog(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecord(apiName, dataId, limit, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 120
//        result.getHasMore() == true
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "webLog1"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "WebObject"
//        modifyRecords[0].getObjectId() == "webData123"
//
//        modifyRecords[1].getLogID() == "webLog2"
//        modifyRecords[1].getOperationType() == "ADD"
//        modifyRecords[1].getObjectName() == "WebObject"
//        modifyRecords[1].getObjectId() == "webData123"
//
//        modifyRecords[2].getLogID() == "webLog3"
//        modifyRecords[2].getOperationType() == "DELETE"
//        modifyRecords[2].getObjectName() == "WebObject"
//        modifyRecords[2].getObjectId() == "webData123"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.modifyLog({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getObjectId() == dataId
//            arg.getCondition().getModule() == apiName
//            arg.getCondition().getPageNum() == pageNumber
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == null
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecord method with operationalType and bizId parameters
//     * 测试第1261行的webSearchModifyRecord(String apiName, String dataId, String operationalType, int limit, int pageNumber, User user, List<String> bizId, List<String> otherBizIds)方法
//     */
//    def "testWebSearchModifyRecordWithOperationalTypeAndBizId"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def dataId = "testData456"
//        def operationalType = "MODIFY"
//        def limit = 25
//        def pageNumber = 2
//        def bizId = ["biz1", "biz2"]
//        def otherBizIds = ["other1"]
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 85
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("testLog1", "MODIFY", "TestObject", "testData456"),
//                createMockLogInfo("testLog2", "MODIFY", "TestObject", "testData456")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.modifyLog(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecord(apiName, dataId, operationalType, limit, pageNumber, user, bizId, otherBizIds)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 85
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 2
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "testLog1"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "TestObject"
//        modifyRecords[0].getObjectId() == "testData456"
//
//        modifyRecords[1].getLogID() == "testLog2"
//        modifyRecords[1].getOperationType() == "MODIFY"
//        modifyRecords[1].getObjectName() == "TestObject"
//        modifyRecords[1].getObjectId() == "testData456"
//
//        // Verify proxy call parameters - should remove hidden when bizId is not empty
//        1 * logServiceProxy.modifyLog({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getObjectId() == dataId
//            arg.getCondition().getModule() == apiName
//            arg.getCondition().getPageNum() == pageNumber
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == operationalType
//            arg.getCondition().getBizId() == bizId
//            arg.getCondition().getOtherBizId() == otherBizIds
//            // When bizId is not empty, hidden should be removed (set to false)
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecord method with LogCondition parameter
//     * 测试第1266行的webSearchModifyRecord(User user, LogCondition condition)方法
//     */
//    def "testWebSearchModifyRecordWithLogCondition"() {
//        given:
//        def user = Mock(User)
//        def logCondition = LogCondition.builder()
//                .module("LogConditionObject")
//                .objectId("conditionData789")
//                .operationalType("ADD")
//                .bizIds(["conditionBiz1", "conditionBiz2"])
//                .otherBizIds(null)
//                .pageNumber(3)
//                .pageSize(15)
//                .operationTimeFrom(1000L)
//                .operationTimeTo(2000L)
//                .operationTime(1500L)
//                .needReturnCount(true)
//                .build()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 45
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("conditionLog1", "ADD", "LogConditionObject", "conditionData789"),
//                createMockLogInfo("conditionLog2", "ADD", "LogConditionObject", "conditionData789"),
//                createMockLogInfo("conditionLog3", "ADD", "LogConditionObject", "conditionData789")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.modifyLog(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecord(user, logCondition)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 45
//        result.getHasMore() == true
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "conditionLog1"
//        modifyRecords[0].getOperationType() == "ADD"
//        modifyRecords[0].getObjectName() == "LogConditionObject"
//        modifyRecords[0].getObjectId() == "conditionData789"
//
//        modifyRecords[1].getLogID() == "conditionLog2"
//        modifyRecords[1].getOperationType() == "ADD"
//        modifyRecords[1].getObjectName() == "LogConditionObject"
//        modifyRecords[1].getObjectId() == "conditionData789"
//
//        modifyRecords[2].getLogID() == "conditionLog3"
//        modifyRecords[2].getOperationType() == "ADD"
//        modifyRecords[2].getObjectName() == "LogConditionObject"
//        modifyRecords[2].getObjectId() == "conditionData789"
//
//        // Verify proxy call parameters - should remove hidden when bizIds is not empty
//        1 * logServiceProxy.modifyLog({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getObjectId() == logCondition.getObjectId()
//            arg.getCondition().getModule() == logCondition.getModule()
//            arg.getCondition().getPageNum() == logCondition.getPageNumber()
//            arg.getCondition().getPageSize() == logCondition.getPageSize()
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == logCondition.getOperationalType()
//            arg.getCondition().getBizId() == logCondition.getBizIds()
//            arg.getCondition().getOtherBizId() == logCondition.getOtherBizIds()
//            arg.getCondition().getFromOperationTime() == logCondition.getOperationTimeFrom()
//            arg.getCondition().getToOperationTime() == logCondition.getOperationTimeTo()
//            arg.getCondition().getLastOperationTime() == logCondition.getOperationTime()
//            arg.getCondition().getNeedReturnCount() == logCondition.getNeedReturnCount()
//            // When bizIds is not empty, hidden should be removed (set to false)
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecord method with LogCondition parameter (line 1281)
//     * 测试第1281行的webSearchModifyRecord(User user, LogCondition condition)方法
//     */
//    def "testWebSearchModifyRecordWithLogConditionLine1281"() {
//        given:
//        def user = Mock(User)
//        def logCondition = LogCondition.builder()
//                .module("Line1281Object")
//                .objectId("line1281Data")
//                .operationalType("DELETE")
//                .bizIds(null)
//                .otherBizIds(["otherBiz1", "otherBiz2"])
//                .pageNumber(1)
//                .pageSize(20)
//                .operationTimeFrom(5000L)
//                .operationTimeTo(8000L)
//                .operationTime(6500L)
//                .needReturnCount(false)
//                .build()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 30
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("line1281Log1", "DELETE", "Line1281Object", "line1281Data"),
//                createMockLogInfo("line1281Log2", "DELETE", "Line1281Object", "line1281Data")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.modifyLog(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecord(user, logCondition)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 30
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 2
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "line1281Log1"
//        modifyRecords[0].getOperationType() == "DELETE"
//        modifyRecords[0].getObjectName() == "Line1281Object"
//        modifyRecords[0].getObjectId() == "line1281Data"
//
//        modifyRecords[1].getLogID() == "line1281Log2"
//        modifyRecords[1].getOperationType() == "DELETE"
//        modifyRecords[1].getObjectName() == "Line1281Object"
//        modifyRecords[1].getObjectId() == "line1281Data"
//
//        // Verify proxy call parameters - should set hidden to true when otherBizIds is not empty
//        1 * logServiceProxy.modifyLog({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getObjectId() == logCondition.getObjectId()
//            arg.getCondition().getModule() == logCondition.getModule()
//            arg.getCondition().getPageNum() == logCondition.getPageNumber()
//            arg.getCondition().getPageSize() == logCondition.getPageSize()
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == logCondition.getOperationalType()
//            arg.getCondition().getBizId() == logCondition.getBizIds()
//            arg.getCondition().getOtherBizId() == logCondition.getOtherBizIds()
//            arg.getCondition().getFromOperationTime() == logCondition.getOperationTimeFrom()
//            arg.getCondition().getToOperationTime() == logCondition.getOperationTimeTo()
//            arg.getCondition().getLastOperationTime() == logCondition.getOperationTime()
//            arg.getCondition().getNeedReturnCount() == logCondition.getNeedReturnCount()
//            // When otherBizIds is not empty, hidden should be set to true
//            arg.getCondition().getHidden() == true
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with operationalType parameter (line 1360)
//     * 测试第1360行的webSearchModifyRecordForMaster(String masterId, String detailApiName, String operationalType, int limit, int pageNumber, User user)方法
//     */
//    def "testWebSearchModifyRecordForMasterWithOperationalType"() {
//        given:
//        def user = Mock(User)
//        def masterId = "master1360"
//        def detailApiName = "DetailObject1360"
//        def operationalType = "MODIFY"
//        def limit = 30
//        def pageNumber = 2
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 120
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("master1360Log1", "MODIFY", "DetailObject1360", "detailData1"),
//                createMockLogInfo("master1360Log2", "MODIFY", "DetailObject1360", "detailData2"),
//                createMockLogInfo("master1360Log3", "MODIFY", "DetailObject1360", "detailData3")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(masterId, detailApiName, operationalType, limit, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 120
//        result.getHasMore() == true
//        result.getModifyRecordList().size() == 3
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "master1360Log1"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "DetailObject1360"
//        modifyRecords[0].getObjectId() == "detailData1"
//
//        modifyRecords[1].getLogID() == "master1360Log2"
//        modifyRecords[1].getOperationType() == "MODIFY"
//        modifyRecords[1].getObjectName() == "DetailObject1360"
//        modifyRecords[1].getObjectId() == "detailData2"
//
//        modifyRecords[2].getLogID() == "master1360Log3"
//        modifyRecords[2].getOperationType() == "MODIFY"
//        modifyRecords[2].getObjectName() == "DetailObject1360"
//        modifyRecords[2].getObjectId() == "detailData3"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == masterId
//            arg.getCondition().getModule() == detailApiName
//            arg.getCondition().getPageNum() == pageNumber
//            arg.getCondition().getPageSize() == limit
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == operationalType
//            arg.getCondition().getHidden() == false
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with masterLogId and bizId parameters
//     * 测试webSearchModifyRecordForMaster(String masterId, String detailApiName, String masterLogId, String operationalType, User user, List<String> bizId, List<String> otherBizIds)方法
//     */
//    def "testWebSearchModifyRecordForMasterWithMasterLogIdAndBizId"() {
//        given:
//        def user = Mock(User)
//        def masterId = "masterWithLogId"
//        def detailApiName = "DetailObjectWithLogId"
//        def masterLogId = "masterLogId123"
//        def operationalType = "ADD"
//        def bizId = ["bizId1", "bizId2"]
//        def otherBizIds = ["otherBiz1"]
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 75
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("masterLogIdLog1", "ADD", "DetailObjectWithLogId", "detailDataWithLogId1"),
//                createMockLogInfo("masterLogIdLog2", "ADD", "DetailObjectWithLogId", "detailDataWithLogId2")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.searchDetail(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(masterId, detailApiName, masterLogId, operationalType, user, bizId, otherBizIds)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 75
//        result.getHasMore() == false
//        result.getModifyRecordList().size() == 2
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "masterLogIdLog1"
//        modifyRecords[0].getOperationType() == "ADD"
//        modifyRecords[0].getObjectName() == "DetailObjectWithLogId"
//        modifyRecords[0].getObjectId() == "detailDataWithLogId1"
//
//        modifyRecords[1].getLogID() == "masterLogIdLog2"
//        modifyRecords[1].getOperationType() == "ADD"
//        modifyRecords[1].getObjectName() == "DetailObjectWithLogId"
//        modifyRecords[1].getObjectId() == "detailDataWithLogId2"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            arg.getCondition().getMasterId() == masterId
//            arg.getCondition().getModule() == detailApiName
//            arg.getCondition().getCorpId() == "testTenant"
//            arg.getCondition().getAppId() == "crm"
//            arg.getCondition().getOperationType() == operationalType
//            arg.getCondition().getMasterLogId() == masterLogId
//            arg.getCondition().getBizId() == bizId
//            arg.getCondition().getOtherBizId() == otherBizIds
//        })
//    }
//
//    /**
//     * Test webSearchModifyRecordForMaster method with LogCondition parameter
//     * 测试webSearchModifyRecordForMaster(User user, LogCondition condition)方法
//     */
//    def "testWebSearchModifyRecordForMasterWithLogCondition"() {
//        given:
//        def user = Mock(User)
//        def logCondition = LogCondition.builder()
//                .module("MasterDetailObject")
//                .objectId("masterDetailData")
//                .operationalType("MODIFY")
//                .bizIds(["bizId1", "bizId2"])
//                .otherBizIds(["otherBiz1"])
//                .pageNumber(2)
//                .pageSize(25)
//                .operationTimeFrom(10000L)
//                .operationTimeTo(20000L)
//                .operationTime(15000L)
//                .needReturnCount(true)
//                .masterId("masterId1387")
//                .masterLogId("masterLogId1387")
//                .build()
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 50
//        mockSearchResult.getTotalPage() >> 3
//        mockSearchResult.getPage() >> 2
//        mockSearchResult.getPageSize() >> 25
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> [
//                createMockLogInfo("masterDetailLog1", "MODIFY", "MasterDetailObject", "masterDetailData"),
//                createMockLogInfo("masterDetailLog2", "MODIFY", "MasterDetailObject", "masterDetailData")
//        ]
//
//        // Mock logServiceProxy
//        logServiceProxy.searchDetail(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.webSearchModifyRecordForMaster(user, logCondition)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 50
//        result.getPageInfo().getPageCount() == 3
//        result.getPageInfo().getPageNumber() == 2
//        result.getPageInfo().getPageSize() == 25
//        result.isHasMore() == true
//        result.getModifyRecordList() != null
//        result.getModifyRecordList().size() == 2
//
//        // Verify modify record details
//        def modifyRecords = result.getModifyRecordList()
//        modifyRecords[0].getLogID() == "masterDetailLog1"
//        modifyRecords[0].getOperationType() == "MODIFY"
//        modifyRecords[0].getObjectName() == "MasterDetailObject"
//        modifyRecords[0].getObjectId() == "masterDetailData"
//
//        modifyRecords[1].getLogID() == "masterDetailLog2"
//        modifyRecords[1].getOperationType() == "MODIFY"
//        modifyRecords[1].getObjectName() == "MasterDetailObject"
//        modifyRecords[1].getObjectId() == "masterDetailData"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.searchDetail({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getModule() == "MasterDetailObject"
//            condition.getAppId() == "crm"
//            condition.getCorpId() == "testTenant"
//            condition.getOperationType() == "MODIFY"
//            condition.getBizId() == ["bizId1", "bizId2"]
//            condition.getOtherBizId() == ["otherBiz1"]
//            condition.getPageNum() == 2
//            condition.getPageSize() == 25
//            condition.getFromOperationTime() == 10000L
//            condition.getToOperationTime() == 20000L
//            condition.getLastOperationTime() == 15000L
//            condition.getNeedReturnCount() == true
//            condition.getMasterId() == "masterId1387"
//            condition.getMasterLogId() == "masterLogId1387"
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getLogById method (line 1412)
//     * 测试第1412行的getLogById(String apiName, String logId, User user)方法
//     */
//    def "testGetLogByIdLine1412"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "testLog123"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo
//        def mockLogInfo = createMockLogInfo(logId, "MODIFY", apiName, "testData123")
//
//        // Mock logServiceProxy
//        logServiceProxy.getLogById(apiName, logId, user) >> mockLogInfo
//
//        when:
//        def result = logService.getLogById(apiName, logId, user)
//
//        then:
//        result != null
//        result.getLogID() == logId
//        result.getOperationType() == "MODIFY"
//        result.getObjectName() == apiName
//        result.getObjectId() == "testData123"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.getLogById(apiName, logId, user)
//    }
//
//    /**
//     * Test getLogById method with null result (line 1412)
//     * 测试第1412行的getLogById方法返回null结果的情况
//     */
//    def "testGetLogByIdLine1412WithNullResult"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "testLog123"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy
//        logServiceProxy.getLogById(apiName, logId, user) >> null
//
//        when:
//        def result = logService.getLogById(apiName, logId, user)
//
//        then:
//        result == null
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.getLogById(apiName, logId, user)
//    }
//
//    /**
//     * Test getLogById method with empty result (line 1412)
//     * 测试第1412行的getLogById方法返回空结果的情况
//     */
//    def "testGetLogByIdLine1412WithEmptyResult"() {
//        given:
//        def user = Mock(User)
//        def apiName = "TestObject"
//        def logId = "testLog123"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock logServiceProxy
//        logServiceProxy.getLogById(apiName, logId, user) >> ""
//
//        when:
//        def result = logService.getLogById(apiName, logId, user)
//
//        then:
//        thrown(ValidateException)
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.getLogById(apiName, logId, user)
//    }
//
//    /**
//     * Test getLogByModule method (line 1432)
//     * 测试第1432行的getLogByModule(String module, int pageSize, int pageNumber, User user)方法
//     */
//    def "testGetLogByModuleLine1432"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule1432"
//        def pageSize = 20
//        def pageNumber = 2
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo list
//        def mockLogInfos = [
//                createMockLogInfo("log1", "MODIFY", module, "data1"),
//                createMockLogInfo("log2", "ADD", module, "data2"),
//                createMockLogInfo("log3", "DELETE", module, "data3")
//        ]
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 100
//        mockSearchResult.getTotalPage() >> 5
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> mockLogInfos
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 100
//        result.getPageInfo().getPageCount() == 5
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getLogInfos() != null
//        result.getLogInfos().size() == 3
//
//        // Verify log infos details
//        def logInfos = result.getLogInfos()
//        logInfos[0].getLogID() == "log1"
//        logInfos[0].getOperationType() == "MODIFY"
//        logInfos[0].getObjectName() == module
//        logInfos[0].getObjectId() == "data1"
//
//        logInfos[1].getLogID() == "log2"
//        logInfos[1].getOperationType() == "ADD"
//        logInfos[1].getObjectName() == module
//        logInfos[1].getObjectId() == "data2"
//
//        logInfos[2].getLogID() == "log3"
//        logInfos[2].getOperationType() == "DELETE"
//        logInfos[2].getObjectName() == module
//        logInfos[2].getObjectId() == "data3"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getAppId() == "crm"
//            condition.getModule() == module
//            condition.getPageNum() == pageNumber
//            condition.getPageSize() == pageSize
//            condition.getCorpId() == "testTenant"
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getLogByModule method with empty results (line 1432)
//     * 测试第1432行的getLogByModule方法返回空结果的情况
//     */
//    def "testGetLogByModuleLine1432WithEmptyResults"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule1432"
//        def pageSize = 20
//        def pageNumber = 1
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock SearchModel.Result with empty results
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 0
//        mockSearchResult.getTotalPage() >> 0
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> false
//        mockSearchResult.getResults() >> []
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getLogByModule(module, pageSize, pageNumber, user)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 0
//        result.getPageInfo().getPageCount() == 0
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getLogInfos() != null
//        result.getLogInfos().size() == 0
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getAppId() == "crm"
//            condition.getModule() == module
//            condition.getPageNum() == pageNumber
//            condition.getPageSize() == pageSize
//            condition.getCorpId() == "testTenant"
//            condition.getHidden() == false
//        })
//    }
//
//    /**
//     * Test getManageLogList method (line 1451)
//     * 测试第1451行的getManageLogList方法
//     */
//    def "testGetManageLogListLine1451"() {
//        given:
//        def user = Mock(User)
//        def module = "TestModule1451"
//        def pageSize = 20
//        def pageNumber = 2
//        def sortField = "operationTime"
//        def sortType = 1 // 升序
//        def filters = [
//                new Filter(fieldName: "operationTime", comparison: 4, filterValue: "1000000000000"), // 大于等于
//                new Filter(fieldName: "operationTime", comparison: 6, filterValue: "2000000000000"), // 小于等于
//                new Filter(fieldName: "userId", comparison: 1, filterValue: "testUser")
//        ]
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//        user.isOutUser() >> false
//        user.getOutTenantId() >> null
//
//        // Mock LogInfo list
//        def mockLogInfos = [
//                createMockLogInfo("manageLog1", "MODIFY", module, "data1"),
//                createMockLogInfo("manageLog2", "ADD", module, "data2"),
//                createMockLogInfo("manageLog3", "DELETE", module, "data3")
//        ]
//
//        // Mock SearchModel.Result
//        def mockSearchResult = Mock(SearchModel.Result)
//        mockSearchResult.getTotalCount() >> 150
//        mockSearchResult.getTotalPage() >> 6
//        mockSearchResult.getPage() >> pageNumber
//        mockSearchResult.getPageSize() >> pageSize
//        mockSearchResult.getHasMore() >> true
//        mockSearchResult.getResults() >> mockLogInfos
//
//        // Mock logServiceProxy
//        logServiceProxy.webSearch(_, _) >> mockSearchResult
//
//        when:
//        def result = logService.getManageLogList(user, module, pageSize, pageNumber, filters, sortField, sortType)
//
//        then:
//        result != null
//        result.getPageInfo() != null
//        result.getPageInfo().getTotalCount() == 150
//        result.getPageInfo().getPageCount() == 6
//        result.getPageInfo().getPageNumber() == pageNumber
//        result.getPageInfo().getPageSize() == pageSize
//        result.getMsgs() != null
//        result.getMsgs().size() == 3
//
//        // Verify log infos details
//        def msgs = result.getMsgs()
//        msgs[0].getLogId() == "manageLog1"
//        msgs[0].getBizOperationName() == "MODIFY"
//        msgs[0].getModule() == module
//        msgs[0].getObjectId() == "data1"
//
//        msgs[1].getLogId() == "manageLog2"
//        msgs[1].getBizOperationName() == "ADD"
//        msgs[1].getModule() == module
//        msgs[1].getObjectId() == "data2"
//
//        msgs[2].getLogId() == "manageLog3"
//        msgs[2].getBizOperationName() == "DELETE"
//        msgs[2].getModule() == module
//        msgs[2].getObjectId() == "data3"
//
//        // Verify proxy call parameters
//        1 * logServiceProxy.webSearch({ Map<String, String> header ->
//            header["x-fs-ei"] == "testTenant"
//            header["FSR-GRAY_VALUE"] == "testTenant"
//        }, { SearchModel.Arg arg ->
//            def condition = arg.getCondition()
//            condition.getAppId() == "crm"
//            condition.getModule() == module
//            condition.getPageNum() == pageNumber
//            condition.getPageSize() == pageSize
//            condition.getCorpId() == "testTenant"
//            condition.getHidden() == false
//            condition.getFromOperationTime() == 1000000000000L
//            condition.getToOperationTime() == 2000000000000L
//            condition.get("userId") == "testUser"
//            condition.getSortInfos() != null
//            condition.getSortInfos().size() == 1
//            condition.getSortInfos()[0].getFieldName() == sortField
//            condition.getSortInfos()[0].isAsc() == true
//        })
//    }
//
//    /**
//     * Test getFunctionLog method (line 1493)
//     * 测试第1493行的getFunctionLog方法
//     */
//    def "testGetFunctionLogLine1493"() {
//        given:
//        def user = Mock(User)
//        def logId = "testLog123"
//        def functionApiName = "testFunction"
//        def operationTimeFrom = new Date(1000000000000L)
//        def operationTimeTo = new Date(2000000000000L)
//        def success = true
//        def sorts = ["operationTime": true, "functionApiName": false]
//        def traceId = "trace123"
//        def name = "testFunctionName"
//        def pageNumber = 2
//        def pageSize = 15
//
//        // Mock user properties
//        user.getTenantId() >> "testTenant"
//        user.getUserId() >> "testUser"
//        user.getUserName() >> "Test User"
//
//        // Mock SearchFunctionModel.Result
//        def mockResult = Mock(SearchFunctionModel.Result)
//        def mockLogs = [
//                createMockFunctionLogInfo("log1", "function1", true, "trace1", new Date()),
//                createMockFunctionLogInfo("log2", "function2", false, "trace2", new Date())
//        ]
//        mockResult.getLogs() >> mockLogs
//        mockResult.getTotal() >> 2
//        mockResult.getPage() >> pageNumber
//        mockResult.getPageSize() >> pageSize
//
//        // Mock logServiceProxy.searchFunctionLog
//        logServiceProxy.searchFunctionLog(_, _) >> mockResult
//
//        when:
//        def result = logService.getFunctionLog(user, logId, functionApiName, operationTimeFrom, success, operationTimeTo, sorts, traceId, name, pageNumber, pageSize)
//
//        then:
//        result != null
//        result.getLogs().size() == 2
//        result.getTotal() == 2
//        result.getPage() == pageNumber
//        result.getPageSize() == pageSize
//
//        // Verify logServiceProxy.searchFunctionLog was called with correct parameters
//        1 * logServiceProxy.searchFunctionLog({ Map<String, String> header ->
//            header.containsKey("tenantId") && header.get("tenantId") == "testTenant"
//        }, { SearchFunctionModel.Arg arg ->
//            arg.getFunctionApiName() == functionApiName &&
//                    arg.getTenantId() == "testTenant" &&
//                    arg.getOperationTimeFrom() == operationTimeFrom &&
//                    arg.getOperationTimeTo() == operationTimeTo &&
//                    arg.getSuccess() == success &&
//                    arg.getSorts() == sorts &&
//                    arg.getPage() == pageNumber &&
//                    arg.getPageSize() == pageSize &&
//                    arg.getTraceId() == traceId &&
//                    arg.getName() == name
//        })
//
//        // Verify log content
//        result.getLogs()[0].getLogId() == "log1"
//        result.getLogs()[0].getFunctionApiName() == "function1"
//        result.getLogs()[0].getSuccess() == true
//        result.getLogs()[0].getTraceId() == "trace1"
//        result.getLogs()[1].getLogId() == "log2"
//        result.getLogs()[1].getFunctionApiName() == "function2"
//        result.getLogs()[1].getSuccess() == false
//        result.getLogs()[1].getTraceId() == "trace2"
//    }
//
//    /**
//     * Test getFunctionLogV2 method (line 1511)
//     * 测试第1511行的getFunctionLogV2方法
//     */
//    def "testGetFunctionLogV2Line1511"() {
//        given:
//        def user = Mock(User)
//        def logId = "testLogV2_123"
//        def functionApiName = "testFunctionV2"
//        def traceId = "traceV2_123"
//        def operationTimeFrom = new Date(1000000000000L)
//        def success = true
//        def operationTimeTo = new Date(2000000000000L)
//        def sorts = ["operationTime": true, "functionApiName": false]
//        def name = "testFunctionNameV2"
//        def pageSize = 20
//        def nextPage = true
//
//        // Mock user properties
//        user.getTenantId() >> "testTenantV2"
//        user.getUserId() >> "testUserV2"
//        user.getUserName() >> "Test User V2"
//
//        // Mock SearchFunctionLogV2.Result
//        def mockResult = Mock(SearchFunctionLogV2.Result)
//        def mockLogs = [
//                createMockFunctionLogInfo("logV2_1", "functionV2_1", true, "traceV2_1", new Date()),
//                createMockFunctionLogInfo("logV2_2", "functionV2_2", false, "traceV2_2", new Date())
//        ]
//        mockResult.getLogs() >> mockLogs
//        mockResult.getTotal() >> 2
//        mockResult.getPageSize() >> pageSize
//        mockResult.getNextPage() >> nextPage
//
//        // Mock logServiceProxy.searchFunctionLogV2
//        logServiceProxy.searchFunctionLogV2(_, _) >> mockResult
//
//        when:
//        def result = logService.getFunctionLogV2(user, logId, functionApiName, traceId, operationTimeFrom, success, operationTimeTo, sorts, name, pageSize, nextPage)
//
//        then:
//        result != null
//        result.getLogs().size() == 2
//        result.getTotal() == 2
//        result.getPageSize() == pageSize
//        result.getNextPage() == nextPage
//
//        // Verify logServiceProxy.searchFunctionLogV2 was called with correct parameters
//        1 * logServiceProxy.searchFunctionLogV2({ Map<String, String> header ->
//            header.containsKey("tenantId") && header.get("tenantId") == "testTenantV2"
//        }, { SearchFunctionLogV2.Arg arg ->
//            arg.getTenantId() == "testTenantV2" &&
//                    arg.getLogId() == logId &&
//                    arg.getFunctionApiName() == functionApiName &&
//                    arg.getTraceId() == traceId &&
//                    arg.getOperationTimeFrom() == operationTimeFrom &&
//                    arg.getOperationTimeTo() == operationTimeTo &&
//                    arg.getSuccess() == success &&
//                    arg.getSorts() == sorts &&
//                    arg.getName() == name &&
//                    arg.getPageSize() == pageSize &&
//                    arg.getNextPage() == nextPage
//        })
//
//        // Verify log content
//        result.getLogs()[0].getLogId() == "logV2_1"
//        result.getLogs()[0].getFunctionApiName() == "functionV2_1"
//        result.getLogs()[0].getSuccess() == true
//        result.getLogs()[0].getTraceId() == "traceV2_1"
//        result.getLogs()[1].getLogId() == "logV2_2"
//        result.getLogs()[1].getFunctionApiName() == "functionV2_2"
//        result.getLogs()[1].getSuccess() == false
//        result.getLogs()[1].getTraceId() == "traceV2_2"
//    }
//
//    /**
//     * Helper method to create mock FunctionLogInfo
//     * 创建模拟FunctionLogInfo的辅助方法
//     */
//    private FunctionLogInfo createMockFunctionLogInfo(String logId, String functionApiName, Boolean success, String traceId, Date operationTime) {
//        def logInfo = Mock(FunctionLogInfo)
//        logInfo.getLogId() >> logId
//        logInfo.getFunctionApiName() >> functionApiName
//        logInfo.getSuccess() >> success
//        logInfo.getTraceId() >> traceId
//        logInfo.getOperationTime() >> operationTime
//        return logInfo
//    }
//
//    /**
//     * Test getFunctionLogDetail method (line 1529)
//     * 测试第1529行的getFunctionLogDetail方法
//     */
//    def "testGetFunctionLogDetailLine1529"() {
//        given:
//        def user = Mock(User)
//        def traceId = "traceDetail123"
//        def logId = "logDetail123"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenantDetail"
//        user.getUserId() >> "testUserDetail"
//        user.getUserName() >> "Test User Detail"
//
//        // Mock SearchFunctionDetailModel.Result
//        def mockResult = Mock(SearchFunctionDetailModel.Result)
//        def mockDetailLogs = [
//                createMockFunctionLogInfo("detailLog1", "functionDetail1", "traceDetail1", new Date()),
//                createMockFunctionLogInfo("detailLog2", "functionDetail2", "traceDetail2", new Date())
//        ]
//        mockResult.getLogs() >> mockDetailLogs
//        mockResult.getTotal() >> 2
//
//        // Mock logServiceProxy.searchFunctionDetailLog
//        logServiceProxy.searchFunctionDetailLog(_, _) >> mockResult
//
//        when:
//        def result = logService.getFunctionLogDetail(user, traceId, logId)
//
//        then:
//        result != null
//        result.getLogs().size() == 2
//        result.getTotal() == 2
//
//        // Verify logServiceProxy.searchFunctionDetailLog was called with correct parameters
//        1 * logServiceProxy.searchFunctionDetailLog({ Map<String, String> header ->
//            header.containsKey("tenantId") && header.get("tenantId") == "testTenantDetail"
//        }, { SearchFunctionDetailModel.Arg arg ->
//            arg.getLogId() == logId &&
//                    arg.getTenantId() == "testTenantDetail"
//        })
//
//        // Verify log content
//        result.getLogs()[0].getLogId() == "detailLog1"
//        result.getLogs()[0].getFunctionApiName() == "functionDetail1"
//        result.getLogs()[0].getTraceId() == "traceDetail1"
//        result.getLogs()[1].getLogId() == "detailLog2"
//        result.getLogs()[1].getFunctionApiName() == "functionDetail2"
//        result.getLogs()[1].getTraceId() == "traceDetail2"
//    }
//
///**
// * Test getNewFunctionLogDetail method (line 1540)
// * 测试第1540行的getNewFunctionLogDetail方法
// */
//    def "testGetNewFunctionLogDetailLine1540"() {
//        given:
//        def user = Mock(User)
//        def traceId = "traceNewDetail123"
//        def logId = "logNewDetail123"
//
//        // Mock user properties
//        user.getTenantId() >> "testTenantNewDetail"
//        user.getUserId() >> "testUserNewDetail"
//        user.getUserName() >> "Test User New Detail"
//
//        // Mock SearchFunctionDetailModel.Result
//        def mockResult = Mock(SearchFunctionDetailModel.Result)
//        def mockNewDetailLogs = [
//                createMockFunctionLogInfo("newDetailLog1", "functionNewDetail1", "traceNewDetail1", new Date()),
//                createMockFunctionLogInfo("newDetailLog2", "functionNewDetail2", "traceNewDetail2", new Date())
//        ]
//        mockResult.getLogs() >> mockNewDetailLogs
//        mockResult.getTotal() >> 2
//
//        // Mock logServiceProxy.getFunctionEsLogDetail
//        logServiceProxy.getFunctionEsLogDetail(_, _) >> mockResult
//
//        when:
//        def result = logService.getNewFunctionLogDetail(user, traceId, logId)
//
//        then:
//        result != null
//        result.getLogs().size() == 2
//        result.getTotal() == 2
//
//        // Verify logServiceProxy.getFunctionEsLogDetail was called with correct parameters
//        1 * logServiceProxy.getFunctionEsLogDetail({ Map<String, String> header ->
//            header.containsKey("tenantId") && header.get("tenantId") == "testTenantNewDetail"
//        }, { SearchFunctionDetailModel.Arg arg ->
//            arg.getLogId() == logId &&
//                    arg.getTenantId() == "testTenantNewDetail"
//        })
//
//        // Verify log content
//        result.getLogs()[0].getLogId() == "newDetailLog1"
//        result.getLogs()[0].getFunctionApiName() == "functionNewDetail1"
//        result.getLogs()[0].getTraceId() == "traceNewDetail1"
//        result.getLogs()[1].getLogId() == "newDetailLog2"
//        result.getLogs()[1].getFunctionApiName() == "functionNewDetail2"
//        result.getLogs()[1].getTraceId() == "traceNewDetail2"
//    }
//
//    /**
//     * 1550
//     */
//    def "testGetNewFunctionLogDetail"() {
//        given:
//        def user = Mock(User)
//        def traceId = "test-trace-id"
//        def logId = "test-log-id"
//        def tenantId = "test-tenant-id"
//
//        def expectedResult = Mock(SearchFunctionDetailModel.Result)
//        def expectedArg = new SearchFunctionDetailModel.Arg()
//        expectedArg.setLogId(logId)
//        expectedArg.setTenantId(tenantId)
//
//        def expectedHeader = ["x-tenant-id": tenantId, "x-user-id": "test-user-id"]
//
//        user.getTenantId() >> tenantId
//        user.getUserId() >> "test-user-id"
//
//        when:
//        def result = logService.getNewFunctionLogDetail(user, traceId, logId)
//
//        then:
//        1 * logServiceProxy.getFunctionEsLogDetail(expectedHeader, expectedArg) >> expectedResult
//        result == expectedResult
//
//        where:
//        traceId     | logId     | tenantId
//        "trace-001" | "log-001" | "tenant-001"
//        "trace-002" | "log-002" | "tenant-002"
//        "trace-003" | "log-003" | "tenant-003"
//    }
//
//    /**
//     * 1584
//     */
//    def "testSearchFunctionEsLog"() {
//        given:
//        def user = Mock(User)
//        def arg = Mock(SearchFunctionEsLog.Arg)
//        def tenantId = "test-tenant-id"
//        def tenantIdInt = 12345
//
//        def expectedHeader = ["x-tenant-id": tenantId, "x-user-id": "test-user-id"]
//
//        def functionLogInfo1 = Mock(FunctionLogInfo)
//        def functionLogInfo2 = Mock(FunctionLogInfo)
//        def logs = [functionLogInfo1, functionLogInfo2]
//
//        def expectedResult = Mock(SearchFunctionEsLog.Result)
//        def objectDescribe1 = Mock(IObjectDescribe)
//        def objectDescribe2 = Mock(IObjectDescribe)
//        def objectDescribes = [objectDescribe1, objectDescribe2]
//
//        user.getTenantId() >> tenantId
//        user.getTenantIdInt() >> tenantIdInt
//        user.getUserId() >> "test-user-id"
//
//        arg.setTenantId(tenantIdInt)
//
//        functionLogInfo1.getBindingApiName() >> "bindingApi1"
//        functionLogInfo2.getBindingApiName() >> "bindingApi2"
//
//        expectedResult.getLogs() >> logs
//
//        objectDescribe1.getApiName() >> "bindingApi1"
//        objectDescribe1.getDisplayName() >> "Display Name 1"
//        objectDescribe2.getApiName() >> "bindingApi2"
//        objectDescribe2.getDisplayName() >> "Display Name 2"
//
//        when:
//        def result = logService.searchFunctionEsLog(user, arg)
//
//        then:
//        1 * logServiceProxy.searchFunctionEsLog(expectedHeader, arg) >> expectedResult
//        1 * objectDescribeService.findDescribeListByApiNames(tenantId, ["bindingApi1", "bindingApi2"]) >> objectDescribes
//        1 * functionLogInfo1.setBindingObjName("Display Name 1")
//        1 * functionLogInfo2.setBindingObjName("Display Name 2")
//        result == expectedResult
//
//        where:
//        tenantId        | tenantIdInt
//        "tenant-001"    | 1001
//        "tenant-002"    | 1002
//        "tenant-003"    | 1003
//    }
//
//    /**
//     * 1610
//     */
//    def "testLogUdefFunction"() {
//        given:
//        def user = Mock(User)
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.UPDATE_FUNCTION_REFERENCE
//        def objectApiName = "test_object"
//        def apiName = "test_function"
//        def function = Mock(IUdefFunction)
//        def tenantId = "test-tenant-id"
//        def userId = "test-user-id"
//        def userName = "test-user-name"
//
//        def objectDescribe = Mock(IObjectDescribe)
//        def displayName = "Test Display Name"
//        def functionName = "Test Function"
//        def textMessage = "处理自定义函数 Test Function 在 Test Display Name 中"
//
//        user.getTenantId() >> tenantId
//        user.getUserId() >> userId
//        user.getUserName() >> userName
//        user.getUserIdOrOutUserIdIfOutUser() >> userId
//
//        function.getFunctionName() >> functionName
//
//        objectDescribe.getDisplayName() >> displayName
//
//        when:
//        logService.logUdefFunction(user, eventType, actionType, objectApiName, apiName, function)
//
//        then:
//        1 * orgService.getUser(tenantId, userId) >> user
//        1 * udefFunctionService.findFunctionByApiName(tenantId, apiName, objectApiName) >> function
//        1 * objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, objectApiName) >> objectDescribe
//        1 * asyncLogSender.offer(_) >> { LogInfo logInfo ->
//            assert logInfo.getCorpId() == tenantId
//            assert logInfo.getUserId() == userId
//            assert logInfo.getUserName() == userName
//            assert logInfo.getAppId() == "CRM"
//            assert logInfo.getModule() == "UserDefineFunc"
//            assert logInfo.getObjectName() == objectApiName
//            assert logInfo.getTextMessage() == textMessage
//            assert logInfo.getOperation() == eventType.getId()
//            assert logInfo.getBizOperationName() == actionType.getId()
//        }
//
//        where:
//        tenantId        | userId          | userName        | objectApiName    | apiName           | functionName
//        "tenant-001"    | "user-001"      | "User One"      | "object_001"     | "function_001"    | "Function One"
//        "tenant-002"    | "user-002"      | "User Two"      | "object_002"     | "function_002"    | "Function Two"
//        "tenant-003"    | "user-003"      | "User Three"    | "object_003"     | "function_003"    | "Function Three"
//    }
//
//    /**
//     * 1648
//     */
//    def "test logTemporaryRights method"() {
//        given: "准备测试数据"
//        def user = new User()
//        user.setTenantId("test_tenant")
//        user.setUserId("test_user")
//
//        def eventType = EventType.ADD
//        def actionType = ActionType.CREATE_TEMPORARY_PRIVILEGE
//        def objectApiName = "test_object"
//        def jsonStr = '{"key":"value","enabled":true}'
//
//        def mockObjectDescribe = Mock(IObjectDescribe)
//        mockObjectDescribe.getDisplayName() >> "测试对象"
//
//        and: "设置Mock行为"
//        objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), objectApiName) >> mockObjectDescribe
//        mockI18NClientResource.getText(I18NKey.TEMPORARY_PRIVILEGE_MANAGER, "测试对象") >> "临时权限管理-测试对象"
//
//        when: "调用logTemporaryRights方法"
//        logService.logTemporaryRights(user, eventType, actionType, objectApiName, jsonStr)
//
//        then: "验证方法调用"
//        1 * asyncLogSender.offer(_ as LogInfo) >> { LogInfo logInfo ->
//            assert logInfo.getTenantId() == user.getTenantId()
//            assert logInfo.getUserId() == user.getUserId()
//            assert logInfo.getEventType() == eventType
//            assert logInfo.getBizOperationName() == actionType.getId()
//            assert logInfo.getObjectApiName() == objectApiName
//            assert logInfo.getModule() == "44" // DATA_PRIVILEGE_MANAGEMENT.getCode()
//            assert logInfo.getTextMessage() == "临时权限管理-测试对象"
//            assert logInfo.getSnapshot() != null
//            assert logInfo.getSnapshot().getTextMsg().size() == 1
//            assert logInfo.getSnapshot().getTextMsg().get(0).getMessage() == "临时权限管理-测试对象"
//            assert logInfo.getSnapshot().getTextMsg().get(0).getObjectApiName() == objectApiName
//            assert logInfo.getSnapshot().getSnapshot() != null
//            assert logInfo.getSnapshot().getSnapshot().getString("key") == "value"
//            assert logInfo.getSnapshot().getSnapshot().getBoolean("enabled") == true
//        }
//    }
//
//    /**
//     * 测试 1660 logDataPermission方法 - 记录数据权限日志
//     */
//    def "testLogDataPermission"() {
//        given: "准备测试数据"
//        def user = new User(tenantId: "test_tenant", userId: "test_user")
//        def eventType = EventType.MODIFY
//        def actionType = ActionType.ADD
//        def snapshots = [
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage("测试消息1", "data1", "object1")])
//                        .snapshot([name: "测试对象1"])
//                        .build(),
//                LogInfo.ObjectSnapshot.builder()
//                        .textMsg([new LogInfo.LintMessage("测试消息2", "data2", "object2")])
//                        .snapshot([name: "测试对象2"])
//                        .build()
//        ]
//
//        when: "调用logDataPermission方法"
//        logService.logDataPermission(user, eventType, actionType, snapshots)
//
//        then: "验证asyncLogSender.offer被调用两次，每次调用都传递正确的LogInfo"
//        2 * asyncLogSender.offer(_ as LogInfo) >> { LogInfo logInfo ->
//            assert logInfo.getCorpId() == user.getTenantId()
//            assert logInfo.getUserId() == user.getUserId()
//            assert logInfo.getOperation() == eventType.getId()
//            assert logInfo.getBizOperationName() == actionType.getId()
//            assert logInfo.getModule() == "44" // DATA_PRIVILEGE_MANAGEMENT.getCode()
//            assert logInfo.getSnapshot() != null
//            assert logInfo.getTextMessage() != null
//        }
//    }
//
//}