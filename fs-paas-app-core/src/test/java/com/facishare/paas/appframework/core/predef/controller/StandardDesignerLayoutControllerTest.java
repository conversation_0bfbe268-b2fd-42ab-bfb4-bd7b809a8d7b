package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.fxiaoke.i18n.client.api.Localization;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

/**
 * StandardDesignerLayoutController 单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class StandardDesignerLayoutControllerTest {

        @Mock
        private ControllerContext controllerContext;

        @Mock
        private IObjectDescribe describe;

        @Mock
        private User user;

        @InjectMocks
        private StandardDesignerLayoutController controller;

        private StandardDesignerLayoutController.Arg arg;
        private LayoutExt layoutExt;
        private LayoutExt mobileLayout;
        private List<RelatedObjectDescribeStructure> detailObjects;
        private List<RelatedObjectDescribeStructure> lookupObjects;
        private Map<String, Localization> localizationMap;
        private ObjectDescribeExt describeExt;
        private List<IComponent> mockComponents;

        @BeforeEach
        void setUp() {
                // 初始化测试数据
                arg = new StandardDesignerLayoutController.Arg();
                arg.describeApiName = "TestObject__c";
                arg.layoutApiName = "test_layout";

                // 设置控制器上下文 - 使用直接反射避免访问权限问题
                try {
                        // 设置 controllerContext 字段（来自父类）
                        Field controllerContextField = findFieldInHierarchy(StandardDesignerLayoutController.class, "controllerContext");
                        controllerContextField.setAccessible(true);
                        controllerContextField.set(controller, controllerContext);

                        // 设置 arg 字段（来自父类）
                        Field argField = findFieldInHierarchy(StandardDesignerLayoutController.class, "arg");
                        argField.setAccessible(true);
                        argField.set(controller, arg);

                        // 设置 describe 字段（当前类）
                        Field describeField = findFieldInHierarchy(StandardDesignerLayoutController.class, "describe");
                        describeField.setAccessible(true);
                        describeField.set(controller, describe);
                } catch (Exception e) {
                        throw new RuntimeException("Failed to set up test fields", e);
                }

                // 初始化Mock对象
                layoutExt = mock(LayoutExt.class);
                mobileLayout = mock(LayoutExt.class);
                detailObjects = new ArrayList<>();
                lookupObjects = new ArrayList<>();
                localizationMap = new HashMap<>();
                describeExt = mock(ObjectDescribeExt.class);

                // 创建Mock组件列表
                mockComponents = createMockComponents();

                // 设置基本Mock行为
                when(controllerContext.getUser()).thenReturn(user);
                when(controllerContext.getTenantId()).thenReturn("74255");
                when(describe.getApiName()).thenReturn("TestObject__c");
        }

        /**
         * 在类层次结构中查找字段
         */
        private Field findFieldInHierarchy(Class<?> clazz, String fieldName) {
                Class<?> currentClass = clazz;
                while (currentClass != null) {
                        try {
                                return currentClass.getDeclaredField(fieldName);
                        } catch (NoSuchFieldException ignored) {
                                currentClass = currentClass.getSuperclass();
                        }
                }
                throw new RuntimeException("Field '" + fieldName + "' not found in class hierarchy of " + clazz.getName());
        }

        /**
         * 创建Mock组件列表
         */
        private List<IComponent> createMockComponents() {
                List<IComponent> components = new ArrayList<>();

                // 创建普通组件
                IComponent normalComponent = new CommonComponent();
                normalComponent.setName("normal_component");
                components.add(normalComponent);

                // 创建主从详情组件
                IComponent masterDetailComponent = new CommonComponent();
                masterDetailComponent.setName("master_detail_component");
                components.add(masterDetailComponent);

                // 创建关联列表组件
                IComponent relatedListComponent = new CommonComponent();
                relatedListComponent.setName("related_list_component");
                components.add(relatedListComponent);

                return components;
        }

        /**
         * GenerateByAI
         * 测试内容描述：测试processMobileLayout方法 - 移动端布局未启用场景
         */
        @Test
        @DisplayName("正常场景 - 移动端布局未启用时直接返回")
        void testProcessMobileLayout_MobileLayoutDisabled() throws Exception {
                // 准备测试数据
                when(layoutExt.isEnableMobileLayout()).thenReturn(false);

                // 获取私有方法
                Method processMobileLayoutMethod = StandardDesignerLayoutController.class
                                .getDeclaredMethod("processMobileLayout", LayoutExt.class, List.class, List.class,
                                                Map.class,
                                                ObjectDescribeExt.class);
                processMobileLayoutMethod.setAccessible(true);

                // 执行被测试方法
                assertDoesNotThrow(() -> {
                        processMobileLayoutMethod.invoke(controller, layoutExt, detailObjects, lookupObjects,
                                        localizationMap,
                                        describeExt);
                });

                // 验证结果 - 由于移动端布局未启用，不应该有任何后续操作
                verify(layoutExt, times(1)).isEnableMobileLayout();
                verify(layoutExt, never()).getMobileLayout();
        }

        /**
         * GenerateByAI
         * 测试内容描述：测试processMobileLayout方法 - 移动端布局启用的正常场景
         */
        @Test
        @DisplayName("正常场景 - 移动端布局启用时完整处理流程")
        void testProcessMobileLayout_MobileLayoutEnabled() throws Exception {
                // 准备测试数据
                when(layoutExt.isEnableMobileLayout()).thenReturn(true);
                when(layoutExt.getMobileLayout()).thenReturn(new HashMap<>());
                when(mobileLayout.getComponentsSilently()).thenReturn(mockComponents);

                // Mock静态方法调用
                try (MockedStatic<LayoutExt> mockedLayoutExt = mockStatic(LayoutExt.class);
                                MockedStatic<ComponentExt> mockedComponentExt = mockStatic(ComponentExt.class);
                                MockedStatic<MasterDetailGroupComponentBuilder> mockedMasterBuilder = mockStatic(
                                                MasterDetailGroupComponentBuilder.class);
                                MockedStatic<RelatedObjectGroupComponentBuilder> mockedRelatedBuilder = mockStatic(
                                                RelatedObjectGroupComponentBuilder.class);
                                MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(
                                                ObjectDescribeExt.class);
                                MockedStatic<LayoutComponents> mockedLayoutComponents = mockStatic(
                                                LayoutComponents.class);
                                MockedStatic<LayoutStructure> mockedLayoutStructure = mockStatic(LayoutStructure.class);
                                MockedStatic<DefObjConstants> mockedDefObjConstants = mockStatic(DefObjConstants.class);
                                MockedStatic<AbstractLayoutProcessor> mockedAbstractLayoutProcessor = mockStatic(
                                                AbstractLayoutProcessor.class)) {

                        // 配置静态方法Mock
                        mockedLayoutExt.when(() -> LayoutExt.of(any(Map.class))).thenReturn(mobileLayout);
                        mockedComponentExt.when(() -> ComponentExt.of(any(IComponent.class)))
                                        .thenAnswer(invocation -> {
                                                ComponentExt mockComponentExt = mock(ComponentExt.class);
                                                IComponent component = invocation.getArgument(0);
                                                when(mockComponentExt.isMasterDetailComponent())
                                                                .thenReturn(component.getName()
                                                                                .contains("master_detail"));
                                                when(mockComponentExt.isRelatedList()).thenReturn(
                                                                component.getName().contains("related_list"));
                                                return mockComponentExt;
                                        });

                        // 简化Builder Mock - 直接Mock构建结果
                        MasterDetailGroupComponentBuilder mockMasterDetailBuilder = mock(
                                        MasterDetailGroupComponentBuilder.class);
                        when(mockMasterDetailBuilder.getComponentListForNewDesigner())
                                        .thenReturn(Arrays.asList(new CommonComponent()));

                        RelatedObjectGroupComponentBuilder mockRelatedBuilder = mock(
                                        RelatedObjectGroupComponentBuilder.class);
                        when(mockRelatedBuilder.getComponentListForDesigner())
                                        .thenReturn(Arrays.asList(new CommonComponent()));

                        // Mock Builder的静态方法返回
                        mockedMasterBuilder.when(() -> MasterDetailGroupComponentBuilder.builder()).thenReturn(
                                        MasterDetailGroupComponentBuilder.builder()
                                                        .layoutExt(mobileLayout)
                                                        .detailObjectsDescribeList(detailObjects)
                                                        .build());

                        mockedRelatedBuilder.when(() -> RelatedObjectGroupComponentBuilder.builder()).thenReturn(
                                        RelatedObjectGroupComponentBuilder.builder()
                                                        .layout(mobileLayout)
                                                        .objectDescribe(describeExt)
                                                        .user(user)
                                                        .relatedObjectDescribeList(lookupObjects)
                                                        .build());

                        mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
                        mockedLayoutComponents.when(
                                        () -> LayoutComponents.replaceOldAttachComponent(anyString(), any(), any()))
                                        .thenReturn(null);
                        mockedLayoutExt.when(() -> LayoutExt.fillNameComponent(any(), any(), any()))
                                        .thenReturn(null);
                        mockedLayoutStructure
                                        .when(() -> LayoutStructure.modifyLayoutStructure(any(), any(), anyBoolean()))
                                        .thenReturn(null);
                        mockedDefObjConstants.when(() -> DefObjConstants.isComponentInvisible(anyString(), anyString()))
                                        .thenReturn(false);
                        mockedLayoutStructure.when(() -> LayoutStructure.clearLayoutStructureByComponents(any(), any()))
                                        .thenReturn(null);
                        mockedAbstractLayoutProcessor
                                        .when(() -> AbstractLayoutProcessor.processFlowComponents(anyString(), any(),
                                                        any()))
                                        .thenReturn(null);

                        // Mock mobileLayout的其他方法
                        when(mobileLayout.filterComponents(any())).thenReturn(Arrays.asList(new CommonComponent()));
                        doNothing().when(mobileLayout).removeHiddenComponents(any());
                        doNothing().when(mobileLayout).setComponents(any());
                        when(mobileLayout.getSuspendedComponent()).thenReturn(Collections.emptyList());
                        when(mobileLayout.toMap()).thenReturn(new HashMap<>());

                        // 获取私有方法
                        Method processMobileLayoutMethod = StandardDesignerLayoutController.class
                                        .getDeclaredMethod("processMobileLayout", LayoutExt.class, List.class,
                                                        List.class, Map.class,
                                                        ObjectDescribeExt.class);
                        processMobileLayoutMethod.setAccessible(true);

                        // 执行被测试方法
                        assertDoesNotThrow(() -> {
                                processMobileLayoutMethod.invoke(controller, layoutExt, detailObjects, lookupObjects,
                                                localizationMap,
                                                describeExt);
                        });

                        // 验证关键方法调用
                        verify(layoutExt, times(1)).isEnableMobileLayout();
                        verify(layoutExt, times(1)).getMobileLayout();
                        verify(mobileLayout, times(1)).getComponentsSilently();
                        verify(layoutExt, times(1)).setMobileLayout(any());
                }
        }

        /**
         * GenerateByAI
         * 测试内容描述：测试processMobileLayout方法 - 空组件列表场景
         */
        @Test
        @DisplayName("边界场景 - 移动端布局组件列表为空")
        void testProcessMobileLayout_EmptyComponentList() throws Exception {
                // 准备测试数据
                when(layoutExt.isEnableMobileLayout()).thenReturn(true);
                when(layoutExt.getMobileLayout()).thenReturn(new HashMap<>());
                when(mobileLayout.getComponentsSilently()).thenReturn(Collections.emptyList());
                when(mobileLayout.filterComponents(any())).thenReturn(Collections.emptyList());
                when(mobileLayout.getSuspendedComponent()).thenReturn(Collections.emptyList());
                when(mobileLayout.toMap()).thenReturn(new HashMap<>());
                doNothing().when(mobileLayout).removeHiddenComponents(any());
                doNothing().when(mobileLayout).setComponents(any());

                // Mock静态方法调用
                try (MockedStatic<LayoutExt> mockedLayoutExt = mockStatic(LayoutExt.class);
                                MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(
                                                ObjectDescribeExt.class);
                                MockedStatic<LayoutComponents> mockedLayoutComponents = mockStatic(
                                                LayoutComponents.class);
                                MockedStatic<LayoutStructure> mockedLayoutStructure = mockStatic(LayoutStructure.class);
                                MockedStatic<DefObjConstants> mockedDefObjConstants = mockStatic(DefObjConstants.class);
                                MockedStatic<AbstractLayoutProcessor> mockedAbstractLayoutProcessor = mockStatic(
                                                AbstractLayoutProcessor.class)) {

                        // 配置静态方法Mock
                        mockedLayoutExt.when(() -> LayoutExt.of(any(Map.class))).thenReturn(mobileLayout);
                        mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
                        mockedLayoutComponents.when(
                                        () -> LayoutComponents.replaceOldAttachComponent(anyString(), any(), any()))
                                        .thenReturn(null);
                        mockedLayoutExt.when(() -> LayoutExt.fillNameComponent(any(), any(), any()))
                                        .thenReturn(null);
                        mockedLayoutStructure
                                        .when(() -> LayoutStructure.modifyLayoutStructure(any(), any(), anyBoolean()))
                                        .thenReturn(null);
                        mockedDefObjConstants.when(() -> DefObjConstants.isComponentInvisible(anyString(), anyString()))
                                        .thenReturn(false);
                        mockedLayoutStructure.when(() -> LayoutStructure.clearLayoutStructureByComponents(any(), any()))
                                        .thenReturn(null);
                        mockedAbstractLayoutProcessor
                                        .when(() -> AbstractLayoutProcessor.processFlowComponents(anyString(), any(),
                                                        any()))
                                        .thenReturn(null);

                        // 获取私有方法
                        Method processMobileLayoutMethod = StandardDesignerLayoutController.class
                                        .getDeclaredMethod("processMobileLayout", LayoutExt.class, List.class,
                                                        List.class, Map.class,
                                                        ObjectDescribeExt.class);
                        processMobileLayoutMethod.setAccessible(true);

                        // 执行被测试方法
                        assertDoesNotThrow(() -> {
                                processMobileLayoutMethod.invoke(controller, layoutExt, detailObjects, lookupObjects,
                                                localizationMap,
                                                describeExt);
                        });

                        // 验证空列表处理
                        verify(mobileLayout, times(1)).getComponentsSilently();
                        verify(mobileLayout, times(1)).setComponents(any());
                        verify(layoutExt, times(1)).setMobileLayout(any());
                }
        }

        /**
         * GenerateByAI
         * 测试内容描述：测试processMobileLayout方法 - 参数验证场景
         */
        @Test
        @DisplayName("异常场景 - 参数为null时的处理")
        void testProcessMobileLayout_NullParameters() throws Exception {
                // 获取私有方法
                Method processMobileLayoutMethod = StandardDesignerLayoutController.class
                                .getDeclaredMethod("processMobileLayout", LayoutExt.class, List.class, List.class,
                                                Map.class, ObjectDescribeExt.class);
                processMobileLayoutMethod.setAccessible(true);

                // 测试layoutExt为null的情况
                assertThrows(Exception.class, () -> {
                        processMobileLayoutMethod.invoke(controller, null, detailObjects, lookupObjects,
                                        localizationMap, describeExt);
                });

                // 测试其他参数为null的情况
                when(layoutExt.isEnableMobileLayout()).thenReturn(false);
                assertDoesNotThrow(() -> {
                        processMobileLayoutMethod.invoke(controller, layoutExt, null, null, null, null);
                });

        }

        /**
         * GenerateByAI
         * 测试内容描述：测试processMobileLayout方法 - 组件过滤场景
         */
        @Test
        @DisplayName("正常场景 - 过滤主从详情组件和关联列表组件")
        void testProcessMobileLayout_ComponentFiltering() throws Exception {
                // 准备测试数据 - 包含需要过滤的组件
                List<IComponent> allComponents = new ArrayList<>();

                // 普通组件 - 应该保留
                IComponent normalComponent = new CommonComponent();
                normalComponent.setName("normal_component");
                allComponents.add(normalComponent);

                // 主从详情组件 - 应该被过滤
                IComponent masterDetailComponent = new CommonComponent();
                masterDetailComponent.setName("master_detail_component");
                allComponents.add(masterDetailComponent);

                when(layoutExt.isEnableMobileLayout()).thenReturn(true);
                when(layoutExt.getMobileLayout()).thenReturn(new HashMap<>());
                when(mobileLayout.getComponentsSilently()).thenReturn(allComponents);
                when(mobileLayout.filterComponents(any())).thenReturn(Collections.emptyList());
                when(mobileLayout.getSuspendedComponent()).thenReturn(Collections.emptyList());
                when(mobileLayout.toMap()).thenReturn(new HashMap<>());
                doNothing().when(mobileLayout).removeHiddenComponents(any());
                doNothing().when(mobileLayout).setComponents(any());

                // Mock静态方法调用
                try (MockedStatic<LayoutExt> mockedLayoutExt = mockStatic(LayoutExt.class);
                                MockedStatic<ComponentExt> mockedComponentExt = mockStatic(ComponentExt.class);
                                MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
                                MockedStatic<LayoutComponents> mockedLayoutComponents = mockStatic(LayoutComponents.class);
                                MockedStatic<LayoutStructure> mockedLayoutStructure = mockStatic(LayoutStructure.class);
                                MockedStatic<DefObjConstants> mockedDefObjConstants = mockStatic(DefObjConstants.class);
                                MockedStatic<AbstractLayoutProcessor> mockedAbstractLayoutProcessor = mockStatic(AbstractLayoutProcessor.class)) {

                        // 配置静态方法Mock
                        mockedLayoutExt.when(() -> LayoutExt.of(any(Map.class))).thenReturn(mobileLayout);

                        // 配置ComponentExt.of()的Mock行为来模拟组件过滤逻辑
                        mockedComponentExt.when(() -> ComponentExt.of(any(IComponent.class)))
                                        .thenAnswer(invocation -> {
                                                ComponentExt mockComponentExt = mock(ComponentExt.class);
                                                IComponent component = invocation.getArgument(0);
                                                when(mockComponentExt.isMasterDetailComponent()).thenReturn(component.getName().contains("master_detail"));
                                                when(mockComponentExt.isRelatedList()).thenReturn(component.getName().contains("related_list"));
                                                return mockComponentExt;
                                        });

                        mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
                        mockedLayoutComponents.when(() -> LayoutComponents.replaceOldAttachComponent(anyString(), any(), any()))
                                        .thenReturn(null);
                        mockedLayoutExt.when(() -> LayoutExt.fillNameComponent(any(), any(), any()))
                                        .thenReturn(null);
                        mockedLayoutStructure.when(() -> LayoutStructure.modifyLayoutStructure(any(), any(), anyBoolean()))
                                        .thenReturn(null);
                        mockedDefObjConstants.when(() -> DefObjConstants.isComponentInvisible(anyString(), anyString()))
                                        .thenReturn(false);
                        mockedLayoutStructure.when(() -> LayoutStructure.clearLayoutStructureByComponents(any(), any()))
                                        .thenReturn(null);
                        mockedAbstractLayoutProcessor.when(() -> AbstractLayoutProcessor.processFlowComponents(anyString(), any(), any()))
                                        .thenReturn(null);

                        // 获取私有方法
                        Method processMobileLayoutMethod = StandardDesignerLayoutController.class
                                        .getDeclaredMethod("processMobileLayout", LayoutExt.class, List.class, List.class, Map.class, ObjectDescribeExt.class);
                        processMobileLayoutMethod.setAccessible(true);

                        // 执行被测试方法
                        assertDoesNotThrow(() -> {
                                processMobileLayoutMethod.invoke(controller, layoutExt, detailObjects, lookupObjects, localizationMap, describeExt);
                        });

                        // 验证组件过滤逻辑被调用
                        verify(mobileLayout, times(1)).getComponentsSilently();
                        // 验证ComponentExt.of()被调用了2次（对应2个组件）
                        mockedComponentExt.verify(() -> ComponentExt.of(any(IComponent.class)), times(2));
                }
        }

        /**
         * GenerateByAI
         * 测试内容描述：测试processMobileLayout方法 - 验证方法调用顺序
         */
        @Test
        @DisplayName("正常场景 - 验证方法调用顺序")
        void testProcessMobileLayout_MethodCallOrder() throws Exception {
                // 准备测试数据
                when(layoutExt.isEnableMobileLayout()).thenReturn(true);
                when(layoutExt.getMobileLayout()).thenReturn(new HashMap<>());
                when(mobileLayout.getComponentsSilently()).thenReturn(Collections.emptyList());
                when(mobileLayout.filterComponents(any())).thenReturn(Collections.emptyList());
                when(mobileLayout.getSuspendedComponent()).thenReturn(Collections.emptyList());
                when(mobileLayout.toMap()).thenReturn(new HashMap<>());
                doNothing().when(mobileLayout).removeHiddenComponents(any());
                doNothing().when(mobileLayout).setComponents(any());

            // Mock静态方法调用
            try (MockedStatic<LayoutExt> mockedLayoutExt = mockStatic(LayoutExt.class);
                        MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
                        MockedStatic<LayoutComponents> mockedLayoutComponents = mockStatic(LayoutComponents.class);
                        MockedStatic<LayoutStructure> mockedLayoutStructure = mockStatic(LayoutStructure.class);
                        MockedStatic<DefObjConstants> mockedDefObjConstants = mockStatic(DefObjConstants.class);
                        MockedStatic<AbstractLayoutProcessor> mockedAbstractLayoutProcessor = mockStatic(
                                        AbstractLayoutProcessor.class)) {

                // 配置静态方法Mock
                mockedLayoutExt.when(() -> LayoutExt.of(any(Map.class))).thenReturn(mobileLayout);
                mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(describeExt);
            mockedLayoutComponents.when(() -> LayoutComponents.replaceOldAttachComponent(anyString(), any(), any()))
                            .thenReturn(null);
            mockedLayoutExt.when(() -> LayoutExt.fillNameComponent(any(), any(), any())).thenReturn(null);
            mockedLayoutStructure.when(() -> LayoutStructure.modifyLayoutStructure(any(), any(), anyBoolean()))
                            .thenReturn(null);
            mockedDefObjConstants.when(() -> DefObjConstants.isComponentInvisible(anyString(), anyString()))
                            .thenReturn(false);
            mockedLayoutStructure.when(() -> LayoutStructure.clearLayoutStructureByComponents(any(), any()))
                            .thenReturn(null);
            mockedAbstractLayoutProcessor
                            .when(() -> AbstractLayoutProcessor.processFlowComponents(anyString(), any(), any()))
                            .thenReturn(null);

            // 获取私有方法
            Method processMobileLayoutMethod = StandardDesignerLayoutController.class
                            .getDeclaredMethod("processMobileLayout", LayoutExt.class, List.class, List.class,
                                            Map.class, ObjectDescribeExt.class);
            processMobileLayoutMethod.setAccessible(true);

            // 执行被测试方法
                            assertDoesNotThrow(() -> {
                    processMobileLayoutMethod.invoke(controller, layoutExt, detailObjects, lookupObjects,
                                    localizationMap, describeExt);
            });

            // 验证方法调用顺序
            verify(layoutExt, times(1)).isEnableMobileLayout();
            verify(layoutExt, times(1)).getMobileLayout();
            verify(mobileLayout, times(1)).getComponentsSilently();
            verify(layoutExt, times(1)).setMobileLayout(any());
        }
    }
}
