package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StandardCloneAction的单元测试类
 */
@ExtendWith(MockitoExtension.class)
class StandardCloneActionTest {

    @Mock(lenient = true)
    private ServiceFacade serviceFacade;

    @Mock(lenient = true)
    private InfraServiceFacade infraServiceFacade;

    @Mock(lenient = true)
    private IObjectDescribe objectDescribe;

    @Mock(lenient = true)
    private ActionContext actionContext;

    @Mock(lenient = true)
    private User user;

    private StandardCloneAction standardCloneAction;

    @BeforeEach
    void setUp() {
        standardCloneAction = spy(new StandardCloneAction());

        // 注入依赖
        Whitebox.setInternalState(standardCloneAction, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(standardCloneAction, "infraServiceFacade", infraServiceFacade);
        Whitebox.setInternalState(standardCloneAction, "objectDescribe", objectDescribe);
        Whitebox.setInternalState(standardCloneAction, "actionContext", actionContext);
        FsGrayReleaseBiz mockGrayReleaseBiz = mock(FsGrayReleaseBiz.class);
        Map<String, FsGrayReleaseBiz> grayMaps = Maps.newHashMap();
        grayMaps.put("metadata-2024", mockGrayReleaseBiz);
        Whitebox.setInternalState(FsGrayRelease.class, "BIZ_MAP", grayMaps);

        // 设置基本的mock行为
        when(actionContext.getUser()).thenReturn(user);
        when(actionContext.getTenantId()).thenReturn("74255");
        when(actionContext.getObjectApiName()).thenReturn("test_object__c");
        when(user.getUserIdOrOutUserIdIfOutUser()).thenReturn("1000");
        when(objectDescribe.getApiName()).thenReturn("test_object__c");
        when(objectDescribe.isPublicObject()).thenReturn(false);
        Map<String, Object> selectOneMap = new HashMap<>();
        selectOneMap.put("type", "select_one");
        SelectOneFieldDescribe selectOneField = new SelectOneFieldDescribe(selectOneMap);
        selectOneField.setApiName("select_one_field__c");
        when(objectDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList(selectOneField));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPreObjectData方法返回objectData
     */
    @Test
    @DisplayName("正常场景 - 获取前置对象数据")
    void testGetPreObjectDataSuccess() {
        // 准备测试数据
        IObjectData mockObjectData = new ObjectData();
        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);

        // 执行被测试方法
        IObjectData result = standardCloneAction.getPreObjectData();

        // 验证结果
        assertSame(mockObjectData, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPostObjectData方法返回objectData
     */
    @Test
    @DisplayName("正常场景 - 获取后置对象数据")
    void testGetPostObjectDataSuccess() {
        // 准备测试数据
        IObjectData mockObjectData = new ObjectData();
        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);

        // 执行被测试方法
        IObjectData result = standardCloneAction.getPostObjectData();

        // 验证结果
        assertSame(mockObjectData, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonApiName方法返回CLONE按钮API名称
     */
    @Test
    @DisplayName("正常场景 - 获取按钮API名称")
    void testGetButtonApiNameSuccess() {
        // 执行被测试方法
        String result = standardCloneAction.getButtonApiName();

        // 验证结果
        assertEquals(ObjectAction.CLONE.getButtonApiName(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isBatchAction方法返回false
     */
    @Test
    @DisplayName("正常场景 - 判断是否批量操作")
    void testIsBatchActionSuccess() {
        // 执行被测试方法
        boolean result = standardCloneAction.isBatchAction();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPreFunction方法当objectData为null时返回true
     */
    @Test
    @DisplayName("正常场景 - objectData为null时跳过前置函数")
    void testSkipPreFunctionWhenObjectDataIsNull() {
        // 准备测试数据 - objectData为null
        Whitebox.setInternalState(standardCloneAction, "objectData", (IObjectData) null);

        // 执行被测试方法
        boolean result = standardCloneAction.skipPreFunction();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPreFunction方法当objectData不为null时返回false
     */
    @Test
    @DisplayName("正常场景 - objectData不为null时不跳过前置函数")
    void testSkipPreFunctionWhenObjectDataIsNotNull() {
        // 准备测试数据 - objectData不为null
        IObjectData mockObjectData = new ObjectData();
        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);

        // 执行被测试方法
        boolean result = standardCloneAction.skipPreFunction();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPostFunction方法当objectData为null时返回true
     */
    @Test
    @DisplayName("正常场景 - objectData为null时跳过后置函数")
    void testSkipPostFunctionWhenObjectDataIsNull() {
        // 准备测试数据 - objectData为null
        Whitebox.setInternalState(standardCloneAction, "objectData", (IObjectData) null);

        // 执行被测试方法
        boolean result = standardCloneAction.skipPostFunction();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPostFunction方法当objectData不为null时返回false
     */
    @Test
    @DisplayName("正常场景 - objectData不为null时不跳过后置函数")
    void testSkipPostFunctionWhenObjectDataIsNotNull() {
        // 准备测试数据 - objectData不为null
        IObjectData mockObjectData = new ObjectData();
        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);

        // 执行被测试方法
        boolean result = standardCloneAction.skipPostFunction();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法返回Add权限代码
     */
    @Test
    @DisplayName("正常场景 - 获取功能权限代码")
    void testGetFuncPrivilegeCodesSuccess() {
        // 执行被测试方法
        List<String> result = standardCloneAction.getFuncPrivilegeCodes();

        // 验证结果
        assertEquals(StandardAction.Add.getFunPrivilegeCodes(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataPrivilegeIds方法返回null
     */
    @Test
    @DisplayName("正常场景 - 获取数据权限ID")
    void testGetDataPrivilegeIdsSuccess() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();

        // 执行被测试方法
        List<String> result = standardCloneAction.getDataPrivilegeIds(arg);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法正常初始化objectData和detailDescribes
     */
    @Test
    @DisplayName("正常场景 - 初始化方法")
    void testInitSuccess() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        arg.setObjectDataId("test_data_id");
        Whitebox.setInternalState(standardCloneAction, "arg", arg);

        IObjectData mockObjectData = new ObjectData();
        List<IObjectDescribe> mockDetailDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(serviceFacade.findObjectData(user, "test_data_id", "test_object__c")).thenReturn(mockObjectData);
        when(serviceFacade.findDetailDescribesCreateWithMaster("74255", "test_object__c")).thenReturn(mockDetailDescribes);

        // 执行被测试方法
        assertDoesNotThrow(() -> standardCloneAction.init());

        // 验证Mock交互
        verify(serviceFacade).findObjectData(user, "test_data_id", "test_object__c");
        verify(serviceFacade).findDetailDescribesCreateWithMaster("74255", "test_object__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试before方法正常设置默认值和配置 - 场景2：无返回值的super调用
     */
    @Test
    @DisplayName("正常场景 - before方法设置默认值（场景2：Mock super调用）")
    void testBeforeSuccess() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();

        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // 配置静态方法Mock
            Set<String> mockFieldTypes = Sets.newHashSet("text", "number");
            mockedConfig.when(() -> AppFrameworkConfig.getFieldEnableCloneFieldTypes("74255")).thenReturn(mockFieldTypes);

            // 创建spy对象以便mock辅助方法（不能对@InjectMocks对象使用spy）
            StandardCloneAction spyAction = spy(new StandardCloneAction());
            // 手动设置必要的依赖
            Whitebox.setInternalState(spyAction, "actionContext", actionContext);
            Whitebox.setInternalState(spyAction, "objectDescribe", objectDescribe);
            Whitebox.setInternalState(spyAction, "serviceFacade", serviceFacade);
            Whitebox.setInternalState(spyAction, "nullValueMap", new HashMap<>());
            Whitebox.setInternalState(spyAction, "systemDefaultValue", new HashMap<>());
            Whitebox.setInternalState(spyAction, "enableCloneFieldTypes", Sets.newHashSet());

            // 关键：mock辅助方法为doNothing（场景2处理策略）
            doNothing().when(spyAction).callSuperBefore(any());

            // 执行被测试方法
            assertDoesNotThrow(() -> spyAction.before(arg));

            // 验证辅助方法被调用
            verify(spyAction).callSuperBefore(arg);

            // 验证静态方法被调用
            mockedConfig.verify(() -> AppFrameworkConfig.getFieldEnableCloneFieldTypes("74255"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试before方法在公共对象场景下的特殊处理
     */
    @Test
    @DisplayName("正常场景 - before方法处理公共对象")
    void testBeforeSuccessWithPublicObject() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();

        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // 配置静态方法Mock
            Set<String> mockFieldTypes = Sets.newHashSet("text", "number");
            mockedConfig.when(() -> AppFrameworkConfig.getFieldEnableCloneFieldTypes("74255")).thenReturn(mockFieldTypes);

            // 创建spy对象以便mock辅助方法（不能对@InjectMocks对象使用spy）
            StandardCloneAction spyAction = spy(new StandardCloneAction());
            // 手动设置必要的依赖
            Whitebox.setInternalState(spyAction, "actionContext", actionContext);
            Whitebox.setInternalState(spyAction, "nullValueMap", new HashMap<>());
            Whitebox.setInternalState(spyAction, "systemDefaultValue", new HashMap<>());
            Whitebox.setInternalState(spyAction, "enableCloneFieldTypes", Sets.newHashSet());

            // Mock objectDescribe为公共对象
            IObjectDescribe mockPublicObjectDescribe = mock(IObjectDescribe.class);
            when(mockPublicObjectDescribe.isPublicObject()).thenReturn(true);
            Whitebox.setInternalState(spyAction, "objectDescribe", mockPublicObjectDescribe);

            // 关键：mock辅助方法为doNothing（场景2处理策略）
            doNothing().when(spyAction).callSuperBefore(any());

            // 执行被测试方法
            assertDoesNotThrow(() -> spyAction.before(arg));

            // 验证辅助方法被调用
            verify(spyAction).callSuperBefore(arg);
        }
    }


    /**
     * GenerateByAI
     * 测试内容描述：测试after方法使用辅助方法调用父类方法（场景1）
     */
    @Test
    @DisplayName("场景1 - 测试after方法调用辅助方法")
    void testAfterCallsHelperMethod() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        StandardCloneAction.Result inputResult = new StandardCloneAction.Result();
        StandardCloneAction.Result expectedResult = new StandardCloneAction.Result();

        // 关键：mock辅助方法返回值
        doReturn(expectedResult).when(standardCloneAction).getSuperAfterResult(arg, inputResult);

        // 执行被测试方法
        StandardCloneAction.Result actualResult = standardCloneAction.after(arg, inputResult);

        // 验证结果
        assertSame(expectedResult, actualResult);

        // 验证辅助方法被调用
        verify(standardCloneAction).getSuperAfterResult(arg, inputResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法的核心业务逻辑 - handleDetail返回空Map的场景
     */
    @Test
    @DisplayName("正常场景 - doAct方法执行克隆逻辑（无从对象）")
    void testDoActSuccessWithEmptyDetails() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        IObjectData mockObjectData = new ObjectData();
        mockObjectData.set("_id", "test_id");
        mockObjectData.set("name", "test_name");

        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);
        ObjectDescribe detailDescribe = new ObjectDescribe();
        detailDescribe.setApiName("detail_object__c");
        Map<String, Object> detailDataMap = new HashMap<>();
        detailDataMap.put("api_name", "detail_field__c");
        detailDataMap.put("type", IFieldType.TEXT);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(detailDataMap);
        detailDescribe.addFieldDescribe(fieldDescribe);
        Whitebox.setInternalState(standardCloneAction, "detailDescribes", Lists.newArrayList(detailDescribe)); // 空的从对象列表

        // 配置Mock行为 - 添加必要的组织信息Mock
        OrganizationInfo mockOrgInfo = mock(OrganizationInfo.class, withSettings().lenient());
        when(mockOrgInfo.getMainDeptId(any())).thenReturn("main_dept_id");
        when(mockOrgInfo.getMainOrgId(any())).thenReturn("main_org_id");

        // Mock serviceFacade的findMainOrgAndDeptByUserId方法
        when(serviceFacade.findMainOrgAndDeptByUserId(any(), any(), any())).thenReturn(mockOrgInfo);

        // Mock其他必要的方法
        doNothing().when(serviceFacade).calculateForClone(any(), any(), any(), any(), any());
        doNothing().when(serviceFacade).fillPhoneNumberInformation(any(IObjectDescribe.class), any(IObjectData.class));

        // 执行被测试方法
        StandardCloneAction.Result result = standardCloneAction.doAct(arg);

        // 验证结果
        assertNotNull(result);

        // 验证关键公共方法被调用
        verify(serviceFacade).calculateForClone(any(), any(), any(), any(), any());
        verify(serviceFacade).fillPhoneNumberInformation(any(IObjectDescribe.class), any(IObjectData.class));
        verify(serviceFacade).findMainOrgAndDeptByUserId(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法的核心业务逻辑 - handleDetail处理从对象的场景
     */
    @Test
    @DisplayName("正常场景 - doAct方法执行克隆逻辑（包含从对象）")
    void testDoActSuccessWithDetails() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        IObjectData mockObjectData = new ObjectData();
        mockObjectData.set("_id", "test_id");
        mockObjectData.set("name", "test_name");
        mockObjectData.set("record_type", "default");
        mockObjectData.set("object_describe_api_name", "test_object__c");

        // 创建模拟的从对象描述 - 使用实际的ObjectDescribe对象
        ObjectDescribe detailDescribe = new ObjectDescribe();
        detailDescribe.setApiName("detail_object__c");

        // 创建主从字段
        Map<String, Object> map = new HashMap<>();
        map.put("type", "master_detail");
        MasterDetailFieldDescribe masterDetailField = new MasterDetailFieldDescribe(map);
        masterDetailField.setApiName("master_detail_field__c");
        masterDetailField.setTargetApiName("test_object__c");
        Map<String, Object> selectOneMap = new HashMap<>();
        selectOneMap.put("type", "select_one");
        SelectOneFieldDescribe selectOneField = new SelectOneFieldDescribe(selectOneMap);
        selectOneField.setApiName("select_one_field__c");
        detailDescribe.setFieldDescribes(Lists.newArrayList(masterDetailField, selectOneField));
        ObjectDescribe mockDetailDescribe = mock(ObjectDescribe.class);
        when(mockDetailDescribe.getApiName()).thenReturn("detail_object__c");
        when(mockDetailDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList(masterDetailField, selectOneField));
        Whitebox.setInternalState(standardCloneAction, "enableCloneFieldTypes", Sets.newHashSet("select_one"));

        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);
        Whitebox.setInternalState(standardCloneAction, "detailDescribes", Lists.newArrayList(mockDetailDescribe));

        // 配置Mock行为 - 添加必要的组织信息Mock
        OrganizationInfo mockOrgInfo = mock(OrganizationInfo.class, withSettings().lenient());
        when(mockOrgInfo.getMainDeptId(any())).thenReturn("main_dept_id");
        when(mockOrgInfo.getMainOrgId(any())).thenReturn("main_org_id");

        // Mock serviceFacade的findMainOrgAndDeptByUserId方法
        when(serviceFacade.findMainOrgAndDeptByUserId(any(), any(), any())).thenReturn(mockOrgInfo);

        // Mock权限检查方法
        Map<String, Map<String, Boolean>> privilegeMap = Maps.newHashMap();
        Map<String, Boolean> createPrivilege = Maps.newHashMap();
        createPrivilege.put("Add", true);
        privilegeMap.put("detail_object__c", createPrivilege);


        when(serviceFacade.batchFunPrivilegeCheck(any(), any(), any())).thenReturn(privilegeMap);
        SearchTemplateQuery mockSearchTemplateQuery = mock(SearchTemplateQuery.class);
        when(serviceFacade.getSearchTemplateQuery(any(User.class), any(ObjectDescribeExt.class), isNull(), any(String.class), any(Boolean.class))).thenReturn(mockSearchTemplateQuery);
        when(serviceFacade.findBySearchQuery(any(MetaDataFindService.QueryContext.class), anyString(), any())).thenReturn(createTestQueryResult());

        // Mock其他必要的方法
        doNothing().when(serviceFacade).calculateForClone(any(), any(), any(), any(), any());
        doNothing().when(serviceFacade).fillPhoneNumberInformation(any(IObjectDescribe.class), any(IObjectData.class));
        when(serviceFacade.getReadonlyFields(any(User.class), anyString())).thenReturn(Sets.newHashSet());
        doNothing().when(serviceFacade).removeMaskFieldValue(any(), any(), any(), any());
        when(serviceFacade.findValidAndMatchRecordTypes(any(), any(), any(), any())).thenReturn(Lists.newArrayList("default"));
        doNothing().when(serviceFacade).fillObjectDataWithRefObject(any(), any(), any(), any(), any(Boolean.class));
        doNothing().when(infraServiceFacade).fillQuoteFieldValue(any(), any(), any(), any(), any(Boolean.class));
        doNothing().when(serviceFacade).fillDimensionFieldValue(any(User.class), any(IObjectDescribe.class), any(List.class));
        doNothing().when(serviceFacade).fillUserInfo(any(), any(), any());
        doNothing().when(serviceFacade).fillDepartmentInfo(any(), any(), any());
        doNothing().when(serviceFacade).fillCountryAreaLabel(any(), any(), any());
        doNothing().when(serviceFacade).fillRichTextImageInfo(any(), any(), any());

        // 执行被测试方法
        StandardCloneAction.Result result = standardCloneAction.doAct(arg);

        // 验证结果
        assertNotNull(result);

        // 验证关键公共方法被调用
        verify(serviceFacade).calculateForClone(any(), any(), any(), any(), any());
        verify(serviceFacade).fillPhoneNumberInformation(any(IObjectDescribe.class), any(IObjectData.class));
        verify(serviceFacade).findMainOrgAndDeptByUserId(any(), any(), any());
        verify(serviceFacade).batchFunPrivilegeCheck(any(), any(), any());
    }

    private QueryResult<IObjectData> createTestQueryResult() {
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setTotalNumber(1);
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("name", "test_name");
        ObjectData objectData = new ObjectData(dataMap);
        result.setData(Lists.newArrayList(objectData));
        return result;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法 - handleDetail权限检查过滤场景，覆盖权限检查分支
     */
    @Test
    @DisplayName("正常场景 - doAct方法执行克隆逻辑（从对象权限过滤）")
    void testDoActWithDetailPermissionFiltering() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        IObjectData mockObjectData = new ObjectData();
        mockObjectData.set("_id", "test_id");
        mockObjectData.set("name", "test_name");

        // 创建两个模拟的从对象描述，一个有权限，一个没权限
        IObjectDescribe mockDetailDescribe1 = mock(IObjectDescribe.class);
        when(mockDetailDescribe1.getApiName()).thenReturn("detail_object1__c");

        IObjectDescribe mockDetailDescribe2 = mock(IObjectDescribe.class);
        when(mockDetailDescribe2.getApiName()).thenReturn("detail_object2__c");

        Whitebox.setInternalState(standardCloneAction, "objectData", mockObjectData);
        Whitebox.setInternalState(standardCloneAction, "detailDescribes", Lists.newArrayList(mockDetailDescribe1, mockDetailDescribe2));

        // 配置Mock行为 - 添加必要的组织信息Mock
        OrganizationInfo mockOrgInfo = mock(OrganizationInfo.class, withSettings().lenient());
        when(mockOrgInfo.getMainDeptId(any())).thenReturn("main_dept_id");
        when(mockOrgInfo.getMainOrgId(any())).thenReturn("main_org_id");

        when(serviceFacade.findMainOrgAndDeptByUserId(any(), any(), any())).thenReturn(mockOrgInfo);

        // Mock权限检查方法 - 只有第一个对象有权限，第二个没有权限
        Map<String, Map<String, Boolean>> privilegeMap = Maps.newHashMap();
        Map<String, Boolean> createPrivilege1 = Maps.newHashMap();
        createPrivilege1.put("create", true);
        Map<String, Boolean> createPrivilege2 = Maps.newHashMap();
        createPrivilege2.put("create", false);
        privilegeMap.put("detail_object1__c", createPrivilege1);
        privilegeMap.put("detail_object2__c", createPrivilege2); // 没有权限
        when(serviceFacade.batchFunPrivilegeCheck(any(), any(), any())).thenReturn(privilegeMap);

        // Mock其他必要的方法
        doNothing().when(serviceFacade).calculateForClone(any(), any(), any(), any(), any());
        doNothing().when(serviceFacade).fillPhoneNumberInformation(any(IObjectDescribe.class), any(IObjectData.class));

        // 执行被测试方法
        StandardCloneAction.Result result = standardCloneAction.doAct(arg);

        // 验证结果
        assertNotNull(result);

        // 验证权限检查被调用，且只处理有权限的对象
        verify(serviceFacade).batchFunPrivilegeCheck(any(), any(), any());
        verify(serviceFacade).calculateForClone(any(), any(), any(), any(), any());
        verify(serviceFacade).fillPhoneNumberInformation(any(IObjectDescribe.class), any(IObjectData.class));
        verify(serviceFacade).findMainOrgAndDeptByUserId(any(), any(), any());
    }


    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的基本功能
     */
    @Test
    @DisplayName("正常场景 - Arg类测试")
    void testArgClass() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        String testObjectDataId = "test_object_data_id";

        // 执行被测试方法
        arg.setObjectDataId(testObjectDataId);

        // 验证结果
        assertEquals(testObjectDataId, arg.getObjectDataId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的基本功能
     */
    @Test
    @DisplayName("正常场景 - Result类测试")
    void testResultClass() {
        // 准备测试数据
        ObjectDataDocument mockObjectData = mock(ObjectDataDocument.class);
        Map<String, List<ObjectDataDocument>> mockDetails = Maps.newHashMap();

        // 执行被测试方法
        StandardCloneAction.Result result = new StandardCloneAction.Result(mockObjectData, mockDetails);

        // 验证结果
        assertSame(mockObjectData, result.getObjectData());
        assertSame(mockDetails, result.getDetails());

        // 测试无参构造函数
        StandardCloneAction.Result emptyResult = new StandardCloneAction.Result();
        assertNotNull(emptyResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试init方法当serviceFacade抛出异常时的处理
     */
    @Test
    @DisplayName("异常场景 - init方法处理serviceFacade异常")
    void testInitThrowsExceptionWhenServiceFacadeError() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        arg.setObjectDataId("test_data_id");
        Whitebox.setInternalState(standardCloneAction, "arg", arg);

        // 配置Mock行为 - 抛出异常
        when(serviceFacade.findObjectData(user, "test_data_id", "test_object__c"))
                .thenThrow(new RuntimeException("Service error"));

        // 执行并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            standardCloneAction.init();
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("Service error"));

        // 验证Mock交互
        verify(serviceFacade).findObjectData(user, "test_data_id", "test_object__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法当objectData为null时的处理
     */
    @Test
    @DisplayName("异常场景 - doAct方法处理objectData为null")
    void testDoActWhenObjectDataIsNull() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        Whitebox.setInternalState(standardCloneAction, "objectData", (IObjectData) null);
        Whitebox.setInternalState(standardCloneAction, "detailDescribes", Lists.newArrayList());

        // 执行并验证异常
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            standardCloneAction.doAct(arg);
        });

        // 验证异常发生
        assertNotNull(exception);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSuperAfterResult辅助方法的功能
     */
    @Test
    @DisplayName("正常场景 - 测试getSuperAfterResult辅助方法")
    void testGetSuperAfterResultSuccess() {
        // 准备测试数据
        StandardCloneAction.Arg arg = new StandardCloneAction.Arg();
        StandardCloneAction.Result inputResult = new StandardCloneAction.Result();

        // 创建真实对象来测试辅助方法
        StandardCloneAction realAction = new StandardCloneAction();
        Whitebox.setInternalState(realAction, "actionContext", actionContext);

        // 执行被测试方法
        StandardCloneAction.Result result = realAction.getSuperAfterResult(arg, inputResult);

        // 验证结果 - 由于调用了父类方法，结果应该不为null
        assertNotNull(result);
    }
}
