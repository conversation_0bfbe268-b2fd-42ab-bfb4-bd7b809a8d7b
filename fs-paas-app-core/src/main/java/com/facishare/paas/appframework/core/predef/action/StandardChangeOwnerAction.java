package com.facishare.paas.appframework.core.predef.action;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.DeptInfo;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStatus;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogExtendInfo;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ActionFilterResult;
import com.facishare.paas.common.util.BulkOpResult;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction.Result;

@Slf4j
public class StandardChangeOwnerAction extends BaseObjectApprovalAction<Arg, Result> {

    protected static final int PAGE_SIZE = 500;

    protected BulkOpResult bulkOpResult = BulkOpResult.builder().successObjectDataList(Lists.newArrayList()).build();
    protected List<IObjectData> oldDataList = new ArrayList<>();
    protected List<IObjectData> dataListToUpdate = Lists.newArrayList();
    private List<String> failReasons = Lists.newArrayList();

    private Result result = Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_OWNER_SUCCESS)).build();

    private OrganizationInfo organizationInfo;
    private boolean ownerIsNull;
    private List<TeamRoleInfo> teamRoleInfos = Lists.newArrayList();

    private boolean customTeamRoleGray;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ChangeOwner.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getData().stream().map(ChangeOwnerData::getObjectDataId).collect(Collectors.toList());
    }

    @Override
    protected void before(Arg arg) {
        customTeamRoleGray = TeamMember.isTeamRoleGray(actionContext.getTenantId());

        validateArg(arg);

        if (!arg.isCascadeDealDetail()) {
            super.before(arg);
            //如果处理的对象是个从对象,则抛出校验异常。
            if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
                throw new ValidateException(I18N.text(I18NKey.CAN_NOT_ONLY_CHANGE_DETAIL_OBJECT_OWNER));
            }
        } else {
            objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
        }
        if (customTeamRoleGray) {
            TeamMemberRoleService teamMemberRoleService = serviceFacade.getBean(TeamMemberRoleService.class);
            teamRoleInfos.addAll(teamMemberRoleService.queryTeamRoleInfo(actionContext.getTenantId(), objectDescribe.getApiName()));
            teamRoleInfos = teamRoleInfos.stream()
                    .filter(role -> !StringUtils.equals(TeamMember.Role.OWNER.getValue(), role.getRoleType())
                            && role.getStatus() == 1)
                    .collect(Collectors.toList());
            if (StringUtils.isNotEmpty(arg.getOldOwnerTeamMemberRole()) &&
                    teamRoleInfos.stream().noneMatch(teamRoleInfo -> StringUtils.equals(arg.getOldOwnerTeamMemberRole(), teamRoleInfo.getRoleType()))) {
                throw new ValidateException(I18N.text(I18NKey.TEAM_MEMBER_ROLE_DISABLE_OR_NOT_EXIST));
            }
            List<String> roleTypes = teamRoleInfos.stream().map(TeamRoleInfo::getRoleType).collect(Collectors.toList());
            List<String> oldOwnerTeamMemberRoleList = arg.getOldOwnerTeamMemberRoleList();
            if (CollectionUtils.notEmpty(oldOwnerTeamMemberRoleList) && oldOwnerTeamMemberRoleList.stream().anyMatch(roleType -> !roleTypes.contains(roleType))) {
                throw new ValidateException(I18N.text(I18NKey.TEAM_MEMBER_ROLE_DISABLE_OR_NOT_EXIST));
            }
        }
        ObjectDescribeExt.of(objectDescribe).getOwnerField().ifPresent(ownerField -> {
            this.ownerIsNull = arg.getData().stream().anyMatch(x -> CollectionUtils.empty(x.getOwnerId()));
            if (ownerIsNull && !AppFrameworkConfig.isChangeOwnerSupportClearObject(actionContext.getTenantId(), actionContext.getObjectApiName())) {
                throw new ValidateException(I18NExt.text(I18NKey.TEMPORARY_DOES_NOT_SUPPORT_EMPTY_OWNER, actionContext.getObjectApiName()));
            }
            if (ownerField.isRequired() && ownerIsNull) {
                throw new ValidateException(I18NExt.text(I18NKey.OWNER_REQUIRED_CAN_NOT_BE_EMPTY));
            }
        });
        initUpdateData();
        stopWatch.lap("initUpdateData");
        //如果是级联处理从对象,则不需要做任何check。必须强制保证主从的负责人是一样的。
        if (!arg.isCascadeDealDetail() && !skipBaseValidate() && !arg.isSkipValidate()) {
            validateLockAndLifeStatus();
            validateObjectApprovalFlow();
            validateDataLifeStatus();
        }

        //triggerFlow=false时不触发审批流
        if (!needTriggerApprovalFlow()) {
            arg.setSkipTriggerApprovalFlow(true);
        }
    }

    @Override
    protected IObjectData getOldObjectData() {
        if (CollectionUtils.empty(oldDataList)) {
            return null;
        }
        return oldDataList.get(0);
    }

    @Override
    protected IObjectData getPreObjectData() {
        if (CollectionUtils.empty(dataListToUpdate)) {
            return null;
        }
        return dataListToUpdate.get(0);
    }

    @Override
    protected IObjectData getPostObjectData() {
        if (CollectionUtils.empty(dataListToUpdate)) {
            return null;
        }
        return dataListToUpdate.get(0);
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHANGE_OWNER.getButtonApiName();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || arg.isCascadeDealDetail() || super.skipCheckButtonConditions();
    }

    /**
     * 跳过按钮的前置执行动作
     *
     * @return
     */
    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() || arg.isCascadeDealDetail() || super.skipPreFunction();
    }

    /**
     * @return 本次请求是否需要创建批量任务
     */
    @Override
    protected boolean isBatchAction() {
        return CollectionUtils.notEmpty(arg.getData()) && arg.getData().size() > 1;
    }

    /**
     * 跳过按钮后动作
     *
     * @return
     */
    @Override
    protected boolean skipPostFunction() {
        return super.skipPostFunction() || arg.isCascadeDealDetail();
    }

    @Override
    protected Map<String, Object> getActionParams(Arg arg) {
        return arg.toButtonArg();
    }


    @Override
    protected Map<String, List<IObjectData>> getPostObjectDetails() {
        Map<String, List<IObjectData>> detailDataMap = new HashMap<>();
        User user = actionContext.getUser();
        if (serviceFacade.isMasterObject(user.getTenantId(), objectDescribe.getApiName())) {
            //获取所有的从describe
            List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
            //获取所有的从数据
            detailDataMap = serviceFacade.findDetailObjectDataList(detailDescribes, dataList.get(0), user);
        }
        return detailDataMap;
    }

    private void validateArg(Arg arg) {
        if (CollectionUtils.empty(arg.getData())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        arg.getData().forEach(x -> {
            if (Strings.isNullOrEmpty(x.getObjectDataId())) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
            if (CollectionUtils.notEmpty(x.getOwnerId()) && x.getOwnerId().stream().anyMatch(y -> Strings.isNullOrEmpty(y) || "null".equals(y))) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
        });

        //校验数据id的最大数量
        int maxChangeOwnerDataIdNum = Math.max(400, AppFrameworkConfig.getMaxChangeOwnerDataIdNum());
        if (CollectionUtils.size(arg.getData()) > maxChangeOwnerDataIdNum) {
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.VALIDATE_CHANGE_OWNER_DATA_NUM, actionContext.getTenantId())) {
                String message = String.format("data num:%s exceed max value:%s", CollectionUtils.size(arg.getData()), maxChangeOwnerDataIdNum);
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR) + "[" + message + "]");
            } else {
                log.warn("data num:{} exceed max value:{}", CollectionUtils.size(arg.getData()), maxChangeOwnerDataIdNum);
            }
        }

        logIllegalParameters(arg.getData());
    }

    private void logIllegalParameters(List<ChangeOwnerData> data) {
        try {
            for (ChangeOwnerData ownerData : data) {
                List<String> ownerIds = CollectionUtils.nullToEmpty(ownerData.getOwnerId());
                for (String ownerId : ownerIds) {
                    int id = Integer.parseInt(ownerId);
                    if (id <= 0 && id != -10000) {
                        log.warn("Change owner Illegal parameter :{}", data);
                        return;
                    }
                }
            }
        } catch (NumberFormatException e) {
            log.warn("Change owner illegal parameter :{}", data);
        } catch (Exception e) {
            log.error("logIllegalParameters error ", e);
        }
    }

    private void validateLockAndLifeStatus() {
        if (CollectionUtils.empty(dataListToUpdate)) {
            return;
        }
        ActionFilterResult actionFilterResult = serviceFacade.validateActionByLockStatusAndLifeStatus(dataListToUpdate,
                ObjectAction.CHANGE_OWNER, actionContext.getUser(), actionContext.getObjectApiName(), false);
        if (CollectionUtils.empty(actionFilterResult.getActionList())) {
            removeData(actionFilterResult);
            String errorMessage = I18N.text(I18NKey.UNSUPPORT_OPERATION_REVERSE, actionFilterResult.getFilterDescription(),
                    ObjectAction.CHANGE_OWNER.getActionLabel());
            failReasons.add(errorMessage);
        }
    }

    private void removeData(ActionFilterResult actionFilterResult) {
        if (CollectionUtils.notEmpty(actionFilterResult.getIneffectiveDataList())) {
            dataListToUpdate.removeIf(data -> actionFilterResult.getIneffectiveDataList().contains(data));
        }

        if (CollectionUtils.notEmpty(actionFilterResult.getLockDataList())) {
            dataListToUpdate.removeIf(data -> actionFilterResult.getLockDataList().contains(data));
        }

        if (CollectionUtils.notEmpty(actionFilterResult.getUnlockDataList())) {
            dataListToUpdate.removeIf(data -> actionFilterResult.getUnlockDataList().contains(data));
        }
    }

    protected void validateObjectApprovalFlow() {
        if (CollectionUtils.empty(dataListToUpdate)) {
            return;
        }
        List<String> dataIDs = dataListToUpdate.stream().map(x -> x.getId()).collect(Collectors.toList());
        Map<String, ApprovalFlowStatus> statusMap = serviceFacade
                .batchGetApprovalStatusOfObject(actionContext.getUser(), dataIDs);

        Iterator<IObjectData> dataIterator = dataListToUpdate.iterator();
        while (dataIterator.hasNext()) {
            IObjectData objectData = dataIterator.next();
            if (ApprovalFlowStatus.isInProgress(statusMap.getOrDefault(objectData.getId(),
                    ApprovalFlowStatus.UNKNOWN))) {
                failReasons.add(I18N.text(I18NKey.ALREADY_EXIST_APPROVAL_FLOW, objectData.getName()));
                dataIterator.remove();
            }
        }
    }

    private void validateDataLifeStatus() {
        dataListToUpdate.removeIf(data -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            if (objectDataExt.isInChange()) {
                String name = StringUtils.trimToEmpty(objectDataExt.getName());
                failReasons.add(I18N.text(I18NKey.ONGOING_APPROVAL_FLOW_AND_NO_CURRENT_OPERATION_POSSIBLE, name));
                return true;
            }
            return false;
        });
    }

    protected List<String> initUpdateFields() {
        List<String> result = Lists.newArrayList(ObjectDataExt.OWNER,
                ObjectDataExt.RELEVANT_TEAM, ObjectDataExt.DATA_OWN_DEPARTMENT);
        if (arg.isUpdateDataOwnOrganization() && ObjectDescribeExt.of(objectDescribe).isOpenOrganization()) {
            result.add(ObjectDataExt.DATA_OWN_ORGANIZATION);
        }
        return result;
    }

    protected void initUpdateData() {
        List<String> dataIDs =
                arg.getData().stream().map(ChangeOwnerData::getObjectDataId).collect(Collectors.toList());
        List<IObjectData> dbDataList = serviceFacade.findObjectDataByIdsIgnoreFormula(actionContext.getTenantId(),
                dataIDs, actionContext.getObjectApiName());

        if (CollectionUtils.empty(dbDataList)) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_DATA_NOT_FOUND));
        }
        Optional<IFieldDescribe> ownerField = ObjectDescribeExt.of(objectDescribe).getOwnerField();
        boolean ownerRequired = ownerField.isPresent() && ownerField.get().isRequired();
        for (ChangeOwnerData argData : arg.getData()) {
            if (ownerRequired && CollectionUtils.empty(argData.getOwnerId())) {
                failReasons.add(I18N.text(I18NKey.OWNER_IS_NULL, argData.getObjectDataId()));
                continue;
            }

            Optional<IObjectData> objectDataOpt = dbDataList.stream()
                    .filter(dbData -> Objects.equals(dbData.getId(), argData.getObjectDataId()))
                    .findFirst();
            if (!objectDataOpt.isPresent()) {
                failReasons.add(I18N.text(I18NKey.ID_IS_NOT_FIND_CAN_NOT_CHANGE_OWNER, argData.getObjectDataId()));
                continue;
            }
            IObjectData objectData = objectDataOpt.get();
            ObjectDataExt dataExt = ObjectDataExt.of(objectData);
            if (dataExt.isInvalid()) {
                failReasons.add(I18N.text(I18NKey.IS_DELETED_CAN_NOT_CHANGE_OWNER, objectData.getName()));
                continue;
            }

            oldDataList.add(dataExt.copy());
            dataListToUpdate.add(objectData);
        }
        organizationInfo = findMainDeptAndOrg();
        // 负责人和归属部门均未发生改变则不触发审批流也不更新数据
        Map<String, ChangeOwnerData> changeOwnerDataMap =
                arg.getData().stream().collect(Collectors.toMap(ChangeOwnerData::getObjectDataId, x -> x));
        dataListToUpdate.removeIf(data -> {
            boolean isNotChange = !arg.isSkipTriggerApprovalFlow()
                    && isNotChanged(changeOwnerDataMap.get(data.getId()), ObjectDataExt.of(data));
            if (isNotChange) {
                oldDataList.remove(data);
            }
            return isNotChange;
        });

        // 处理负责人和相关团队
        dataListToUpdate.forEach(objectData -> {
            ChangeOwnerData argData = changeOwnerDataMap.get(objectData.getId());
            List<String> ownerIdList = argData.getOwnerId();
            String ownerId = CollectionUtils.notEmpty(ownerIdList) ? ownerIdList.get(0) : null;

            updateOwnerToTeamMember(objectData, ownerId,
                    arg.getOldOwnerStrategy(), arg.getOldOwnerTeamMemberPermissionType(),
                    arg.getOldOwnerTeamMemberRole(), arg.getOldOwnerTeamMemberRoleList());

            objectData.set(ObjectDataExt.OWNER, CollectionUtils.notEmpty(ownerIdList) ? ownerIdList : null);
            objectData.setLastModifiedBy(actionContext.getUser().getUserId());
            objectData.setLastModifiedTime(System.currentTimeMillis());
        });

        // 是否同时更新归属部门
        updateDataOwnDept(arg);
        updateDataOwnOrg();
    }

    private void updateDataOwnOrg() {
        if (!arg.isUpdateDataOwnOrganization()) {
            return;
        }
        // 是流程回调的的则使用回调Map中保存的DataOwnOrganizationId
        if (arg.isFlowCallBack()) {
            Map<String, List<String>> orgIdMap = Maps.newHashMap();
            arg.getData().forEach(x -> orgIdMap.put(x.getObjectDataId(), x.getDataOwnOrganizationId()));
            dataListToUpdate.forEach(data -> {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                List<String> orgIdList = orgIdMap.get(data.getId());
                if (CollectionUtils.notEmpty(orgIdList)) {
                    dataExt.setDataOwnOrganizationId(orgIdList.get(0));
                } else {
                    dataExt.setDataOwnOrganizationId(null);
                }
            });
        } else {
            // 查询组织架构获取负责人主属部门，用于填充归属部门
            dataListToUpdate.forEach(data -> {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                Optional<String> ownerIdPresent = dataExt.getOwnerId();
                if (ownerIdPresent.isPresent()) {
                    String mainOrgId = organizationInfo.getMainOrgId(ownerIdPresent.get());
                    if (StringUtils.isNotBlank(mainOrgId)) {
                        dataExt.setDataOwnOrganizationId(mainOrgId);
                    }
                }
                if (ownerIdPresent.isPresent() && StringUtils.equals(ownerIdPresent.get(), User.SUPPER_ADMIN_USER_ID)) {
                    dataExt.setDataOwnOrganizationId(User.COMPANY_ID);
                }
            });
        }
    }

    private boolean isNotChanged(@NonNull ChangeOwnerData argData, ObjectDataExt dataExt) {
        if (arg.isUpdateDataOwnOrganization()) {
            return isOwnerIdNotChanged(argData, dataExt)
                    && isDataOwnDepartmentIdNotChanged(argData, dataExt)
                    && isDataOwnOrganizationIdNotChanged(argData, dataExt);
        }
        if (updateDataOwnDepartment(arg)) {
            return isOwnerIdNotChanged(argData, dataExt)
                    && isDataOwnDepartmentIdNotChanged(argData, dataExt);
        }
        return isOwnerIdNotChanged(argData, dataExt);
    }

    private boolean isDataOwnOrganizationIdNotChanged(ChangeOwnerData argData, ObjectDataExt dataExt) {
        String ownerId = CollectionUtils.notEmpty(argData.getOwnerId()) ? argData.getOwnerId().get(0) : null;
        String mainOrgId = organizationInfo.getMainOrgId(ownerId);
        return Objects.equals(mainOrgId, dataExt.getDataOwnOrganizationId());
    }

    private boolean isDataOwnDepartmentIdNotChanged(ChangeOwnerData argData, ObjectDataExt dataExt) {
        String ownerId = CollectionUtils.notEmpty(argData.getOwnerId()) ? argData.getOwnerId().get(0) : null;
        String mainDeptId = organizationInfo.getMainDeptId(ownerId);
        return Objects.equals(mainDeptId, dataExt.getDataOwnDepartmentId());
    }

    private boolean isOwnerIdNotChanged(ChangeOwnerData argData, ObjectDataExt dataExt) {
        List<String> ownerIdList = argData.getOwnerId();
        String ownerId = CollectionUtils.notEmpty(ownerIdList) ? ownerIdList.get(0) : null;
        return Objects.equals(ownerId, dataExt.getOwnerId().orElse(null));
    }

    private void updateDataOwnDept(Arg arg) {
        if (!updateDataOwnDepartment(arg)) {
            return;
        }
        // 是流程回调的的则使用回调Map中保存的DataOwnDepartmentId
        if (arg.isFlowCallBack()) {
            Map<String, List<String>> deptIdMap = Maps.newHashMap();
            arg.getData().forEach(x -> deptIdMap.put(x.getObjectDataId(), x.getDataOwnDepartmentId()));
            dataListToUpdate.forEach(data -> {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                List<String> deptIdList = deptIdMap.get(data.getId());
                if (CollectionUtils.notEmpty(deptIdList)) {
                    dataExt.setDataOwnDepartmentId(deptIdList.get(0));
                } else {
                    dataExt.setDataOwnDepartmentId(null);
                }
            });
        } else {
            // 查询组织架构获取负责人主属部门，用于填充归属部门
            dataListToUpdate.forEach(data -> {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                Optional<String> ownerIdPresent = dataExt.getOwnerId();
                if (ownerIdPresent.isPresent()) {
                    String mainDeptId = organizationInfo.getMainDeptId(ownerIdPresent.get());
                    if (StringUtils.isNotBlank(mainDeptId)) {
                        dataExt.setDataOwnDepartmentId(mainDeptId);
                    }
                }
                if (ownerIdPresent.isPresent() && StringUtils.equals(ownerIdPresent.get(), User.SUPPER_ADMIN_USER_ID)) {
                    dataExt.setDataOwnDepartmentId(User.COMPANY_ID);
                }
            });
        }
        // 更新了归属部门，需要重新设置归属部门
        serviceFacade.setupTeamInterconnectedDepartments(actionContext.getUser(), objectDescribe, dataListToUpdate);
    }

    private OrganizationInfo findMainDeptAndOrg() {
        List<String> ownerIds = arg.getData().stream()
                .filter(data -> CollectionUtils.notEmpty(data.getOwnerId()) && !Strings.isNullOrEmpty(data.getOwnerId().get(0)))
                .map(data -> data.getOwnerId().get(0))
                .collect(Collectors.toList());
        if (arg.isUpdateDataOwnOrganization()) {
            return serviceFacade.findMainOrgAndDeptByUserId(actionContext.getTenantId(), actionContext.getUser().getUserId(), ownerIds);
        }
        List<QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfos = serviceFacade.getDeptInfoByUserIds(actionContext.getTenantId(),
                actionContext.getUser().getUserId(), ownerIds);
        if (CollectionUtils.empty(mainDeptInfos)) {
            return OrganizationInfo.empty();
        }
        List<OrganizationInfo.OrgInfo> orgInfos = mainDeptInfos.stream()
                .filter(it -> QueryDeptInfoByUserIds.MainDeptInfo.TYPE_DEPT.equals(it.getDeptType()))
                .map(mainDeptInfo -> {
                    DeptInfo mainDept = new DeptInfo();
                    mainDept.setDeptId(mainDeptInfo.getDeptId());
                    mainDept.setUserId(mainDeptInfo.getUserId());
                    return OrganizationInfo.OrgInfo.builder()
                            .mainDept(mainDept)
                            .userId(mainDeptInfo.getUserId())
                            .build();
                })
                .collect(Collectors.toList());
        return OrganizationInfo.of(orgInfos);

    }

    @Override
    protected Result doAct(Arg arg) {
        if (!result.isSuccess()) {
            return result;
        }
        if (CollectionUtils.empty(dataListToUpdate)) {
            return result;
        }
        if (!arg.isSkipTriggerApprovalFlow()) {
            Map<String, Map<String, Object>> dataMap = Maps.newHashMap();
            dataListToUpdate.forEach(x -> {
                Map<String, Object> data = Maps.newHashMap();
                Optional<String> ownerId = ObjectDataExt.of(x).getOwnerId();
                List<String> ownerIdList = ownerId.map(Lists::newArrayList).orElseGet(Lists::newArrayList);
                data.put("owner", ownerIdList);
                data.put("relateObjectApiNames", arg.getRelateObjectApiNames());
                dataMap.put(x.getId(), data);
            });
            Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
            dataListToUpdate.forEach(x -> {
                Map<String, Object> callbackData = Maps.newHashMap();
                Optional<String> ownerId = ObjectDataExt.of(x).getOwnerId();
                List<String> ownerIdList = ownerId.map(Lists::newArrayList).orElseGet(Lists::newArrayList);
                callbackData.put("ownerId", ownerIdList);
                callbackData.put("oldOwnerStrategy", arg.getOldOwnerStrategy());
                callbackData.put("oldOwnerTeamMemberPermissionType", arg.getOldOwnerTeamMemberPermissionType());
                callbackData.put("oldOwnerTeamMemberRole", arg.getOldOwnerTeamMemberRole());
                callbackData.put("oldOwnerTeamMemberRoleList", arg.getOldOwnerTeamMemberRoleList());
                callbackData.put("updateDataOwnDepartment", updateDataOwnDepartment(arg));
                callbackData.put("dataOwnDepartmentId", Strings.isNullOrEmpty(ObjectDataExt.of(x).getDataOwnDepartmentId()) ?
                        Collections.EMPTY_LIST : Lists.newArrayList(ObjectDataExt.of(x).getDataOwnDepartmentId()));
                callbackData.put("updateDataOwnOrganization", arg.isUpdateDataOwnOrganization());
                callbackData.put("dataOwnOrganizationId", Strings.isNullOrEmpty(ObjectDataExt.of(x).getDataOwnOrganizationId()) ?
                        Collections.EMPTY_LIST : Lists.newArrayList(ObjectDataExt.of(x).getDataOwnDepartmentId()));
                //将原状态放入callbackData，用于审批通过和驳回之后恢复原状态
                callbackData.put(ObjectLifeStatus.LAST_LIFE_STATUS_API_NAME, ObjectDataExt.of(x).getLifeStatusText());
                //relateObjectApiNames
                callbackData.put("relateObjectApiNames", arg.getRelateObjectApiNames());
                callbackDataMap.put(x.getId(), callbackData);
            });
            if (dataListToUpdate.size() > 1) {
                startApprovalFlowAsynchronous(dataListToUpdate, ApprovalFlowTriggerType.CHANGE_OWNER, dataMap, callbackDataMap);
                return Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_OWNER_SUCCESS)).build();
            } else {
                startApprovalFlow(dataListToUpdate, ApprovalFlowTriggerType.CHANGE_OWNER, dataMap, callbackDataMap, null);
                if (isApprovalFlowStartSuccess(dataListToUpdate.get(0).getId())) {
                    return Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_OWNER_SUCCESS)).build();
                }
            }
        }

        List<String> updateFields = initUpdateFields();
        ActionContextExt actionContextExt = ActionContextExt.of(actionContext.getUser());
        if (ownerIsNull) {
            actionContextExt.setIsAllowDeleteAllTeamMembers(true);
        }
        serviceFacade.batchUpdateByFields(actionContextExt.getContext(), dataListToUpdate, updateFields);
        stopWatch.lap("batchUpdateByFields");
        bulkOpResult.setSuccessObjectDataList(dataListToUpdate);

        return result;
    }

    protected boolean updateDataOwnDepartment(Arg arg) {
        initButton();
        String extendInfo = udefButton.getExtendInfo();
        if (StringUtils.isBlank(extendInfo)) {
            return arg.isUpdateDataOwnDepartment();
        }
        ExtendInfo info = JacksonUtils.fromJson(extendInfo, ExtendInfo.class);
        if (Objects.isNull(info)) {
            return arg.isUpdateDataOwnDepartment();
        }
        return Boolean.TRUE.equals(info.getUpdateDataOwnDepartment());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (!result.isSuccess()) {
            return result;
        }

        // 没有需要更新的数据不需要执行后续操作
        if (CollectionUtils.empty(dataListToUpdate)) {
            checkFailedResult();
            return result;
        }

        // 异步触发审批流或成功触发审批流不需要执行后续操作
        if (isApprovalFlowStartSuccessOrAsynchronous(dataListToUpdate.get(0).getId())) {
            checkFailedResult();
            return result;
        }
        List<IObjectData> oldRecordDataList = ObjectDataExt.copyList(oldDataList);
        List<IObjectData> successObjectDataList = ObjectDataExt.copyList(bulkOpResult.getSuccessObjectDataList());
        ParallelUtils.createBackgroundTask().submit(() -> {
            serviceFacade.logByActionType(actionContext.getUser(), EventType.MODIFY, ActionType.ChangeOwner, oldRecordDataList, successObjectDataList, objectDescribe);
            List<String> updateFields = Lists.newArrayList(ObjectDataExt.DATA_OWN_ORGANIZATION, ObjectDataExt.DATA_OWN_DEPARTMENT);
            LogExtendInfo logExtendInfo = LogExtendInfo.builder().peerReason(ActionType.ChangeOwner.getName()).peerReasonI18NKey(ActionType.ChangeOwner.getI18NKey()).build();
            serviceFacade.bulkRecordEditLog(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe,
                    successObjectDataList, oldRecordDataList, updateFields, logExtendInfo);
        }).run();
        stopWatch.lap("logByActionType");
        dealWithDetail(arg, objectDescribe, bulkOpResult.getSuccessObjectDataList());
        stopWatch.lap("dealWithDetail");
        if (CollectionUtils.notEmpty(bulkOpResult.getFailObjectDataList())) {
            failReasons.add(bulkOpResult.getFailReason());
        }
        checkFailedResult();
        serviceFacade.sendActionMq(actionContext.getUser(), fillOldData(bulkOpResult.getSuccessObjectDataList()), ObjectAction.CHANGE_OWNER);
        stopWatch.lap("sendActionMq");
        return super.after(arg, result);
    }

    private void checkFailedResult() {
        if (CollectionUtils.notEmpty(failReasons)) {
            if (arg.isCascadeDealDetail) {
                throw new ValidateException(StringUtils.join(failReasons, ','));
            } else {
                throw new ValidateException(I18N.text(I18NKey.FAILED_DATA, StringUtils.join(failReasons, ',')));
            }
        }
    }

    private List<IObjectData> fillOldData(List<IObjectData> dataList) {
        return ObjectDataDocument.fillOldData(dataList, oldDataList);
    }

    private void updateOwnerToTeamMember(IObjectData objectData, String owner, String strategy,
                                         String oldOwnerTeamMemberPermissionType, String oldOwnerTeamMemberRole,
                                         List<String> oldOwnerTeamMemberRoleList) {
        //如果是从对象,则不需要处理相关团队,因为从对象的相关团队是绑定在主上的。
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        //从原相关团队中获取原负责人
        Optional<TeamMember> oldOwnerTeamMemberOpt = objectDataExt.getOwnerTeamMemberOpt();
        String oldOwner = "";
        if (oldOwnerTeamMemberOpt.isPresent()) {
            oldOwner = oldOwnerTeamMemberOpt.get().getEmployee();
            //移除原负责人
            //避免将外部负责人移除相关团队
            teamMembers.removeIf(it -> TeamMember.Role.OWNER.equals(it.getRole()) && !it.isOutMember());
        }
        //从原相关团队中获取新负责人,如果能找到,也移除,避免在后面被二次添加
        teamMembers.removeIf(it -> Objects.equals(it.getEmployee(), owner));

        //添加新负责人
        if (StringUtils.isNotEmpty(owner)) {
            teamMembers.add(new TeamMember(owner, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE));
        }
        //处理原负责人
        //如果是从对象,则不需要处理相关团队,因为从对象的相关团队是绑定在主上的。
        if (strategy != null && (StringUtils.isNotEmpty(owner) && strategy.equals(UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue()))
                && StringUtils.isNotEmpty(oldOwner)) {
            if (customTeamRoleGray) {
                if (CollectionUtils.notEmpty(oldOwnerTeamMemberRoleList)) {
                    for (String roleType : oldOwnerTeamMemberRoleList) {
                        TeamMember teamMember = new TeamMember(oldOwner, roleType, TeamMember.Permission.of(oldOwnerTeamMemberPermissionType), null);
                        teamMembers.add(teamMember);
                    }
                } else {
                    TeamMember teamMember = new TeamMember(oldOwner, oldOwnerTeamMemberRole, TeamMember.Permission.of(oldOwnerTeamMemberPermissionType), null);
                    teamMembers.add(teamMember);
                }
            } else {
                List<TeamMember.Role> roleList = Lists.newArrayList();
                if (CollectionUtils.notEmpty(oldOwnerTeamMemberRoleList)) {
                    roleList = oldOwnerTeamMemberRoleList.stream().map(TeamMember.Role::of).collect(Collectors.toList());
                }
                if (CollectionUtils.empty(roleList)) {
                    TeamMember teamMember = new TeamMember(oldOwner, TeamMember.Role.of(oldOwnerTeamMemberRole), TeamMember.Permission.of(oldOwnerTeamMemberPermissionType));
                    teamMembers.add(teamMember);
                } else {
                    for (TeamMember.Role role : roleList) {
                        TeamMember teamMember = new TeamMember(oldOwner, role, TeamMember.Permission.of(oldOwnerTeamMemberPermissionType));
                        teamMembers.add(teamMember);
                    }
                }
            }
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    //主从对象同步处理
    //处理从对象时需要捕获异常，防止影响其他从对象的处理
    private void dealWithDetail(Arg arg, IObjectDescribe objectDescribe, List<IObjectData> masterDatas) {
        if (CollectionUtils.empty(masterDatas)) {
            return;
        }
        List<IObjectDescribe> relatedDescribes = serviceFacade.findDetailDescribes(actionContext.getTenantId(),
                objectDescribe.getApiName());
        if (CollectionUtils.empty(relatedDescribes)) {
            return;
        }
        for (IObjectDescribe detailDescribe : relatedDescribes) {
            if (AppFrameworkConfig.isChangeOwnerDetailAsync(actionContext.getTenantId(), detailDescribe.getApiName())) {
                ParallelUtils.createParallelTask().submit(() -> changeDetailOwner(objectDescribe, masterDatas, detailDescribe)).run();
                stopWatch.lap("changeDetailOwnerAsync");
            } else {
                changeDetailOwner(objectDescribe, masterDatas, detailDescribe);
                stopWatch.lap("changeDetailOwnerSync");
            }
        }
    }

    private void changeDetailOwner(IObjectDescribe objectDescribe, List<IObjectData> masterDataList, IObjectDescribe detailDescribe) {
        for (IObjectData masterObjectData : masterDataList) {
            try {
                SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
                queryExt.setOffset(0);
                queryExt.setLimit(PAGE_SIZE);
                String mdFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldName(objectDescribe.getApiName())
                        .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
                queryExt.addFilter(Operator.IN, mdFieldName, Lists.newArrayList(masterObjectData.getId()));
                queryExt.searchInDB();

                MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                        .user(actionContext.getUser())
                        .isSimple(true)
                        .esSearchSkipRecentUpdateCheck(true)
                        .skipRelevantTeam(true)
                        .build();
                serviceFacade.queryDataAndHandle(queryContext, queryExt.toSearchTemplateQuery(), detailDescribe, PAGE_SIZE,
                        -1, (detailDataResult) -> {
                            if (CollectionUtils.empty(detailDataResult.getData())) {
                                return;
                            }
                            detailDataChangeOwner(masterObjectData, detailDescribe, detailDataResult.getData());
                            stopWatch.lap("detailDataChangeOwner:" + detailDescribe.getApiName());
                        });
            } catch (Exception e) {
                log.error("cascade dealWithDetail error,tenantId:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                        actionContext.getTenantId(), objectDescribe.getApiName(), masterObjectData.getId(), detailDescribe.getApiName(), e);
                failReasons.add(e.getMessage());
            }
        }
    }

    private void detailDataChangeOwner(IObjectData masterObjectData, IObjectDescribe detailDescribe, List<IObjectData> detailDataList) {
        if (CollectionUtils.empty(detailDataList)) {
            return;
        }

        Arg detailArg = new Arg();
        detailArg.setOldOwnerStrategy(arg.oldOwnerStrategy);
        detailArg.setOldOwnerTeamMemberRole(arg.oldOwnerTeamMemberRole);
        detailArg.setOldOwnerTeamMemberPermissionType(arg.oldOwnerTeamMemberPermissionType);
        detailArg.setCascadeDealDetail(true);
        detailArg.setSkipTriggerApprovalFlow(true);
        detailArg.setUpdateDataOwnDepartment(updateDataOwnDepartment(arg));
        detailArg.setUpdateDataOwnOrganization(arg.isUpdateDataOwnOrganization());

        List<String> detailIdList = detailDataList.stream().map(it -> it.getId()).collect(Collectors.toList());
        List<ChangeOwnerData> datas = new ArrayList<>(detailIdList.size());
        for (String detailId : detailIdList) {
            Optional<String> ownerId = ObjectDataExt.of(masterObjectData).getOwnerId();
            ChangeOwnerData data = new ChangeOwnerData();
            data.setObjectDataId(detailId);
            data.setOwnerId(ownerId.map(Collections::singletonList).orElse(Lists.newArrayList()));
            // 主对象的归属部门变更，同步变更从对象的归属部门
            if (updateDataOwnDepartment(detailArg)) {
                data.setDataOwnDepartmentId(Lists.newArrayList(ObjectDataExt.of(masterObjectData).getDataOwnDepartmentId()));
            }
            //主对象的归属组织变更，同步变更从对象的归属组织
            if (detailArg.isUpdateDataOwnOrganization()) {
                data.setDataOwnOrganizationId(Lists.newArrayList(ObjectDataExt.of(masterObjectData).getDataOwnDepartmentId()));
            }
            datas.add(data);
        }
        detailArg.setData(datas);
        ActionContext detailActionContext = new ActionContext(actionContext.getRequestContext(),
                detailDescribe.getApiName(), actionContext.getActionCode());
        serviceFacade.triggerAction(detailActionContext, detailArg, Result.class);
    }


    @Override
    protected boolean skipNonBlockingPreFunction() {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_745)) {
            return true;
        }
        return false;
    }

    @Override
    protected Result buildValidateResult() {
        String message = validatedFunctionResult.getReturnValue() == null ? "" : validatedFunctionResult.getReturnValue().toString();

        BaseObjectSaveAction.ValidationMessage validateMessage = BaseObjectSaveAction.ValidationMessage.builder().isMatch(true).build();
        if (validatedFunctionResult.isBlock()) {
            validateMessage.setBlockMessages(Lists.newArrayList(I18N.text(I18NKey.FRONT_VALIDATE_FUNCTION_FAILED, message)));
        } else {
            validateMessage.setNonBlockMessages(Lists.newArrayList(I18N.text(I18NKey.FRONT_VALIDATE_FUNCTION_FAILED, message)));
        }

        return Result.builder().validationMessage(validateMessage).build();
    }

    @Data
    public static class ExtendInfo {
        private Boolean updateDataOwnDepartment;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @JsonProperty("Data")
        @SerializedName("Data")
        private List<ChangeOwnerData> data;
        private String oldOwnerStrategy = UdobjConstants.CHANGE_OWNER_STRATEGY.REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM.getValue();
        private String oldOwnerTeamMemberRole;
        private List<String> oldOwnerTeamMemberRoleList;
        private String oldOwnerTeamMemberPermissionType;
        @JsonProperty("isCascadeDealDetail")
        private boolean isCascadeDealDetail = false;
        private boolean skipTriggerApprovalFlow = false;
        private List<String> relateObjectApiNames;  //同时更换相关对象负责人
        // 默认变更归属部门
        @JsonProperty("isUpdateDataOwnDepartment")
        private boolean isUpdateDataOwnDepartment = true;
        // 默认不变更归属组织
        @JsonProperty("isUpdateDataOwnOrganization")
        private boolean isUpdateDataOwnOrganization = false;
        /**
         * 是否是无规格的商品更换负责人
         */
        // TODO 特殊给商品用的
        private boolean isNoSepcSpu;

        private boolean skipPreAction;
        private boolean skipButtonConditions;
        /**
         * 是否为流程回调请求
         */
        @JsonProperty("isFlowCallBack")
        private boolean isFlowCallBack = false;

        private boolean skipValidate;

        @SuppressWarnings("unchecked")
        public static Arg of(Map<String, Object> callBackData, String dataId) {
            StandardChangeOwnerAction.ChangeOwnerData data = ChangeOwnerData.builder()
                    .objectDataId(dataId)
                    .ownerId((List) callBackData.get("ownerId"))
                    .dataOwnDepartmentId((List) callBackData.get("dataOwnDepartmentId"))
                    .dataOwnOrganizationId((List) callBackData.get("dataOwnOrganizationId"))
                    .build();
            return Arg.builder()
                    .data(Lists.newArrayList(data))
                    .oldOwnerStrategy(UdobjConstants.CHANGE_OWNER_STRATEGY.REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM.getValue())
                    .oldOwnerTeamMemberRole((String) callBackData.get("oldOwnerTeamMemberRole"))
                    .oldOwnerTeamMemberPermissionType((String) callBackData.get("oldOwnerTeamMemberPermissionType"))
                    .oldOwnerTeamMemberRoleList((List<String>) callBackData.get("oldOwnerTeamMemberRoleList"))
                    .oldOwnerStrategy((String) callBackData.get("oldOwnerStrategy"))
                    .isUpdateDataOwnDepartment(Boolean.TRUE.equals(callBackData.get("updateDataOwnDepartment")))
                    .isUpdateDataOwnOrganization(Boolean.TRUE.equals(callBackData.get("updateDataOwnOrganization")))
                    .skipTriggerApprovalFlow(true)
                    .relateObjectApiNames((List<String>) callBackData.get("relateObjectApiNames"))
                    .skipPreAction(true)
                    .skipButtonConditions(true)
                    .isFlowCallBack(true)
                    .build();
        }

        public List<String> dataIds() {
            return CollectionUtils.nullToEmpty(data).stream().map(ChangeOwnerData::getObjectDataId).collect(Collectors.toList());
        }

        /**
         * 目前下游不支持更换外部负责人，不处理下游相关信息
         */
        public Map<String, Object> toButtonArg() {
            Map<String, Object> result = Maps.newHashMap();
            List<TeamMemberInfoPoJo> teamMemberInfoPoJoList = Lists.newArrayList();
            if (CollectionUtils.empty(getData())) {
                return result;
            }
            getData().forEach(data -> {
                if (CollectionUtils.empty(data.getOwnerId())) {
                    return;
                }
                TeamMemberInfoPoJo teamMemberInfoPoJo = new TeamMemberInfoPoJo();
                teamMemberInfoPoJo.setTeamMemberEmployee(data.getOwnerId());
                if (StringUtils.isNotBlank(getOldOwnerTeamMemberRole())) {
                    teamMemberInfoPoJo.setTeamMemberRole(getOldOwnerTeamMemberRole());
                }
                if (CollectionUtils.notEmpty(getOldOwnerTeamMemberRoleList())) {
                    teamMemberInfoPoJo.setTeamMemberRoleList(getOldOwnerTeamMemberRoleList());
                }
                if (StringUtils.isNotBlank(getOldOwnerTeamMemberPermissionType())) {
                    teamMemberInfoPoJo.setTeamMemberPermissionType(getOldOwnerTeamMemberPermissionType());
                }
                teamMemberInfoPoJo.setTeamMemberType(TeamMember.MemberType.EMPLOYEE.getValue());
                teamMemberInfoPoJoList.add(teamMemberInfoPoJo);
            });
            if (CollectionUtils.empty(teamMemberInfoPoJoList)) {
                return result;
            }
            result.put("teamMemberInfos", teamMemberInfoPoJoList.stream().map(TeamMemberInfoPoJo::toMap).collect(Collectors.toList()));
            return result;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangeOwnerData {
        private String objectDataId;
        private List<String> ownerId;
        private List<String> dataOwnDepartmentId;
        @Builder.Default
        private List<String> dataOwnOrganizationId = Lists.newArrayList();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private BaseObjectSaveAction.ValidationMessage validationMessage;

        public boolean isSuccess() {
            return "0".equals(errorCode);
        }
    }

}
