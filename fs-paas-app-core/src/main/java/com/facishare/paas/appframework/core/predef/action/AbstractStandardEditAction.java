package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.*;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.DataConflictsProcessor;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerAttributes;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.dto.CreateChangeOrder;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderEffectiveStatus;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleSearchResult;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph;
import com.facishare.paas.appframework.metadata.state.MergeStateContainer;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.IRule;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by liyiguang on 2019/3/28.
 */
@Slf4j
public class AbstractStandardEditAction<A extends BaseObjectSaveAction.Arg> extends BaseObjectSaveAction<A> {

    protected List<IObjectData> detailsToAdd = Lists.newArrayList();
    protected List<IObjectData> detailsToUpdate = Lists.newArrayList();
    protected List<IObjectData> detailsToDelete = Lists.newArrayList();
    protected Map<String, List<IObjectData>> detailsOnlyOrderChanges = Maps.newHashMap();

    //key表示id,value的map表示要更新的字段(key)和值(value)
    protected Map<String, Object> updatedFieldMap = Maps.newHashMap();
    protected Map<String, Object> updatedFieldMapForApproval = Maps.newHashMap();

    protected Map<String, Map<String, Object>> detailChangeMap = Maps.newHashMap();
    private boolean detailChangeForApproval = false;

    //本次操作真正得到更新的数据
    protected List<IObjectData> updatedDataList = Lists.newArrayList();

    protected IObjectData dbMasterData;
    protected Map<String, List<IObjectData>> dbDetailDataMap = Maps.newHashMap();
    private boolean findDbDetailDataFlag = false;
    //本次编辑的主对象logId
    protected String masterLogId;

    protected FieldRelationGraph graph;
    protected final Map<String, Set<String>> removeFieldMap = Maps.newHashMap();

    //给从对象补充id，后面合并数据需要用
    private List<IObjectData> noIdDataList;

    private ObjectDataSnapshot snapshot;

    private boolean isDataVersionConflict;
    private Set<String> versionConflictDetails = Sets.newHashSet();
    private IObjectData originalData;
    private Map<String, List<IObjectData>> originalDetailDataMap;

    private SaveActionServiceFacade saveActionServiceFacade;
    private Set<String> hiddenFields;

    @Override
    protected boolean enableIdempotentGrayRule() {
        return true;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Edit.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(BaseObjectSaveAction.Arg arg) {
        return Lists.newArrayList(arg.getObjectData().toObjectData().getId());
    }

    @Override
    protected String getIRule() {
        return IRule.UPDATE;
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.UPDATE;
    }

    /**
     * 获取不需要触发审批流的字段
     *
     * @return
     */
    protected Set<String> getIgnoreFieldsForApproval() {
        return Sets.newHashSet();
    }

    /**
     * 主对象是否成功触发审批流
     *
     * @return
     */
    protected boolean isEditApprovalFlowStartSuccess() {
        return isApprovalFlowStartSuccess(objectData.getId()) && ObjectDataExt.of(objectData).isInChange();
    }

    @Override
    protected final List<String> getRecordTypes() {
        return Lists.newArrayList(dbMasterData.getRecordType());
    }

    @Override
    protected final EditActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        //给从对象补充id，后面合并数据需要用
        if (ActionDomainPlugin.BEFORE.equals(method)) {
            noIdDataList = ObjectDataExt.fillDataId(detailObjectData);
        }
        return EditActionDomainPlugin.Arg.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .detailObjectData(ObjectDataDocument.ofMap(detailObjectData))
                .dbMasterData(ObjectDataDocument.of(dbMasterData))
                .isApprovalFlowStartSuccess(isApprovalFlowStartSuccess(objectData.getId()))
                .build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        EditActionDomainPlugin.Result editPluginResult = (EditActionDomainPlugin.Result) pluginResult;
        //合并插件返回结果
        ObjectDataMerger.Result mergeResult = ObjectDataMerger.builder()
                .origMasterData(objectData)
                .origDetailDataMap(detailObjectData)
                .masterDataToUpdate(editPluginResult.masterDataToUpdate())
                .detailDataToAdd(ObjectDataDocument.ofDataMap(editPluginResult.getDetailsToAdd()))
                .detailDataToUpdate(ObjectDataDocument.ofDataMap(editPluginResult.getDetailsToUpdate()))
                .detailDataToDelete(editPluginResult.getDetailsToDelete())
                .build()
                .doMerge();
        //重新diff一遍主数据
        if (mergeResult.isMasterChanged()) {
            diffMasterData();
        }
        //重新diff一遍从数据，重置arg中的从数据
        if (mergeResult.isDetailChanged()) {
            refreshDetailData();
            diffDetailData();
        }
        //合并审批流回调参数
        if (CollectionUtils.notEmpty(editPluginResult.getCustomCallbackData())) {
            customCallbackData.putAll(editPluginResult.getCustomCallbackData());
        }
        //清理原来没有id的从对象的id
        ObjectDataExt.removeDataId(noIdDataList);
    }

    @Override
    protected final Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        EditActionHandler.Arg handlerArg = new EditActionHandler.Arg();
        handlerArg.setDetailDescribeMap(detailDescribeMap);
        handlerArg.setObjectData(ObjectDataDocument.of(objectData));
        handlerArg.setDetailObjectData(ObjectDataDocument.ofMap(detailObjectData));
        handlerArg.setRelatedObjectData(RelatedDataDocument.fromMap(relatedObjectData));
        handlerArg.setAssigneesExceptional(isAssigneesExceptional);
        handlerArg.setUniqueRule(uniqueRule);
        handlerArg.setExtraCallbackData(getExtraCallbackData());
        handlerArg.setDbMasterData(ObjectDataDocument.of(dbMasterData));
        handlerArg.setDbDetailDataMap(ObjectDataDocument.ofMap(dbDetailDataMap));
        handlerArg.setDetailsToAdd(ObjectDataDocument.ofList(detailsToAdd));
        handlerArg.setDetailsToDelete(ObjectDataDocument.ofList(detailsToDelete));
        handlerArg.setDetailsToUpdate(ObjectDataDocument.ofList(detailsToUpdate));
        handlerArg.setConvertRuleDataContainer(convertRuleDataContainer);
        handlerArg.setRemoveFieldMap(removeFieldMap);
        handlerArg.setDataDiffResult(EditActionHandler.DataDiffResult.builder()
                .updatedFieldMap(updatedFieldMap)
                .updatedFieldMapForApproval(updatedFieldMapForApproval)
                .detailChangeMap(detailChangeMap)
                .detailChangeForApproval(detailChangeForApproval)
                .build());
        handlerArg.putContextData("change_order_callback_data", JSON.toJSONString(getChangeOrderCallbackData()));
        return (Handler.Arg<A>) handlerArg;
    }

    @Override
    protected final Result getInterfaceResult(Handler.Result handlerResult) {
        Result result = super.getInterfaceResult(handlerResult);
        if (Objects.nonNull(result)) {
            result.setDataChanged(isDataChanged());
        }
        return result;
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<Result> handlerResult) {
        super.processHandlerResult(handlerContext, handlerArg, handlerResult);
        if (handlerResult instanceof SaveActionHandler.Result) {
            SaveActionHandler.Result saveHandlerResult = (SaveActionHandler.Result) handlerResult;
            if (Objects.nonNull(saveHandlerResult.getObjectData())) {
                ObjectDataExt.of(this.objectData).putAll(saveHandlerResult.getObjectData());
                diffMasterData();
            }
            if (Objects.nonNull(saveHandlerResult.getDetailObjectData())) {
                this.detailObjectData.putAll(ObjectDataDocument.ofDataMap(saveHandlerResult.getDetailObjectData()));
                refreshDetailData();
                diffDetailData();
            }
            if (Objects.nonNull(saveHandlerResult.getSkipDuplicateSearchCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_DUPLICATE_SEARCH_CHECK, saveHandlerResult.getSkipDuplicateSearchCheck());
            }
            if (Objects.nonNull(saveHandlerResult.getSkipUniqueRuleCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_UNIQUE_RULE_CHECK, saveHandlerResult.getSkipUniqueRuleCheck());
            }
            if (Objects.nonNull(saveHandlerResult.getSkipValidationFunctionCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_FUNCTION_CHECK, saveHandlerResult.getSkipValidationFunctionCheck());
            }
            if (Objects.nonNull(saveHandlerResult.getSkipValidationRuleCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_RULE_CHECK, saveHandlerResult.getSkipValidationRuleCheck());
            }
        }
        if (handlerResult instanceof EditActionHandler.Result) {
            EditActionHandler.Result editHandlerResult = (EditActionHandler.Result) handlerResult;
            EditActionHandler.DataDiffResult dataDiffResult = editHandlerResult.getDataDiffResult();
            if (Objects.nonNull(dataDiffResult)) {
                this.updatedFieldMap = dataDiffResult.getUpdatedFieldMap();
                this.updatedFieldMapForApproval = dataDiffResult.getUpdatedFieldMapForApproval();
                removeHiddenFieldsByDomainPlugin(this.updatedFieldMapForApproval, this.objectDescribe.getApiName());
                this.detailChangeMap = dataDiffResult.getDetailChangeMap();
                this.detailChangeForApproval = dataDiffResult.isDetailChangeForApproval();
            }
        }
    }

    private void removeHiddenFieldsByDomainPlugin(Map<String, Object> updatedFieldMapForApproval, String objectApiName) {
        if (CollectionUtils.notEmpty(updatedFieldMapForApproval) && CollectionUtils.notEmpty(this.hiddenFields)) {
            updatedFieldMapForApproval.keySet().removeAll(hiddenFields);
        }
    }

    @Override
    protected boolean skipQueryRelateTeam() {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SKIP_QUERY_RELATE_TEAM_WHEN_UPDATE_EI, actionContext.getTenantId());
    }

    @Override
    protected boolean keepAllMultiLangValue() {
        return true;
    }

    @Override
    protected void init() {
        saveActionServiceFacade = serviceFacade.getBean(SaveActionServiceFacade.class);

        //先校验下参数
        validateBeforeInit();

        super.init();

        validateChangeOrderObject();

        initOriginalData();
        stopWatch.lap("initOriginalData");

        dbMasterData = dataList.get(0);

        //gdpr校验
        validateGdpr();
        stopWatch.lap("validateGdpr");

        // 重新设置从对象order_by字段
        setOrderForDetailData(detailObjectData);
        //校验业务类型
        validateRecordType(objectDescribe, objectData.getRecordType());

        //过滤掉值为空的多货币字段
        processMultiCurrencyFields();
        stopWatch.lap("processMultiCurrencyFields");

        //查询数据快照
        if (useSnapshot()) {
            snapshot = findSnapshot();
            stopWatch.lap("findSnapshot");
        }

        //重新查询一次,这样可以把生命状态字段和计算字段都查出来。如果不查会导致后面触发流程时候判断不对
        prepareMasterObjectData();
        stopWatch.lap("prepareMasterObjectData");

        //将从对象分为待新增、待更新、待删除3部分
        prepareDetailObjectData();
        stopWatch.lap("prepareDetailObjectData");

        //处理V0、V1、V2三个版本的数据冲突
        processDataConflicts();
        stopWatch.lap("processDataConflicts");

        //计算统计字段和计算字段
        calculateCountAndFormulaFields();
        stopWatch.lap("calculateCountAndFormulaFields");

        //传了原始数据的时候需要重算引用字段，因为端上只传了变更的字段，数据库里查到的引用字段和端上显示的可能不一样，函数里使用会有问题。
        calculateQuoteFields();
        stopWatch.lap("calculateQuoteFields");

        processRemoveFields();
        stopWatch.lap("processRemoveFields");

        initHiddenFieldsByDomainPlugin();
        stopWatch.lap("initHiddenFieldsByDomainPlugin");
    }

    private void validateChangeOrderObject() {
        if (ObjectDescribeExt.of(objectDescribe).isChangeOrderObject() && !isFromChangeOrder()) {
            throw new ValidateException(I18NExt.text(I18NKey.UNABLE_TO_UPDATE_CHANGE_ORDER_DATA));
        }
    }

    private void initHiddenFieldsByDomainPlugin() {
        try {
            hiddenFields = infraServiceFacade.getHiddenFieldsByDomainPluginDescribe(PageType.Approval.name(), pluginDescribes);
        } catch (Exception e) {
            log.warn("initHiddenFieldsByDomainPlugin failed", e);
        }
    }

    @Override
    protected void initDataList() {
        if (infraServiceFacade.containIdFieldMapping(actionContext.getUser(), objectDescribe, arg.getFieldMapping())) {
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(actionContext.getUser())
                    .skipRelevantTeam(skipQueryRelateTeam())
                    .isSimple(needSimple())
                    .includeInvalid(needInvalidData())
                    .build();
            dataList = infraServiceFacade.queryDataBySpecifiedField(queryContext, objectDescribe, Lists.newArrayList(arg.getFieldMapping()), Lists.newArrayList(arg.getObjectData().toObjectData()));
            if (CollectionUtils.empty(dataList)) {
                throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
            }
        } else {
            super.initDataList();
        }
    }

    private void initOriginalData() {
        if (CollectionUtils.notEmpty(arg.getOriginalData())) {
            originalData = arg.getOriginalData().toObjectData();
            decodeMaskFieldEncryptValue(objectDescribe, Lists.newArrayList(originalData));
            ObjectDataExt.of(originalData).extractAbstractOfRichText(objectDescribe);
        }
        if (CollectionUtils.notEmpty(arg.getOriginalDetails())) {
            originalDetailDataMap = Maps.newHashMap();
            arg.getOriginalDetails().forEach((detailApiName, detailDocuments) -> {
                IObjectDescribe detailDescribe = objectDescribes.get(detailApiName);
                List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(detailDocuments);
                decodeMaskFieldEncryptValue(detailDescribe, Lists.newArrayList(objectDataList));
                originalDetailDataMap.put(detailApiName, objectDataList);
            });
        }
    }

    @Override
    protected final boolean assigneesExceptional() {
        //cep请求不支持跳过状态校验
        if (arg.skipDataStatusValidate() && !RequestUtil.isCepRequest()) {
            return true;
        }
        if (useSnapshot()) {
            return true;
        }
        return super.assigneesExceptional();
    }

    private boolean useSnapshot() {
        return arg.useSnapshot() && ObjectDataExt.of(dbMasterData).isInChange();
    }

    private void validateBeforeInit() {
        if (arg.useSnapshot() &&
                (Objects.isNull(arg.getBizInfo())
                        || Strings.isNullOrEmpty(arg.getBizInfo().getBiz())
                        || Strings.isNullOrEmpty(arg.getBizInfo().getOtherBizId()))) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    @Override
    protected void before(A arg) {
        super.before(arg);

        if (!isIncrementUpdate()) {
            //和数据库中的数据做diff
            diffObjectDataWithDbData();
            stopWatch.lap("diffObjectDataWithDbData");
        }

        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BATCH_VALIDATE_EDIT_DATA_CONSTRAINT_BY_FIELDS, actionContext.getTenantId())) {
            EditActionHandler.Arg handlerArg = (EditActionHandler.Arg) getHandlerArg(null);
            saveActionServiceFacade.batchValidateEditDataConstraintByFields(actionContext.getUser(), handlerArg);
            stopWatch.lap("batchValidateEditDataConstraintByFields");
        }
    }

    @Override
    protected void validateLookupData(IObjectData objectData, IObjectDescribe describe) {
        //灰度是否只校验值有变化的lookup字段
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.VALIDATE_LOOKUP_FIELD_ONLY_CHANGED_GRAY_EI, actionContext.getTenantId())) {
            //获取数据有变化的字段
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            List<ObjectReferenceWrapper> fields = describeExt.getActiveReferenceFieldDescribes();
            Map<String, Object> updateFields = ObjectDataExt.of(dbMasterData).diff(objectData, describe);
            List<ObjectReferenceWrapper> changedFields = fields.stream().filter(a -> updateFields.containsKey(a.getApiName())).collect(Collectors.toList());
            super.doValidateLookupData(objectData, changedFields);
        } else {
            super.validateLookupData(objectData, describe);
        }
    }

    private ObjectDataSnapshot findSnapshot() {
        return infraServiceFacade.findAndMergeSnapshot(actionContext.getTenantId(), actionContext.getObjectApiName(),
                objectData.getId(), ApprovalFlow.getCode(), arg.getBizInfo().getOtherBizId());
    }

    private void validateGdpr() {
        if (infraServiceFacade.validateGdprCompliance(actionContext.getUser(), ObjectAction.UPDATE.getActionCode(), objectDescribe.getApiName(), objectData.getId())) {
            throw new ValidateException(I18N.text(I18NKey.GDPR_UNABLE_OPERATION, I18N.text(I18NKey.action_edit)));
        }

        if (RequestUtil.isOpenAPIRequest() && infraServiceFacade.validateGdprCompliance(actionContext.getUser(),
                RequestContext.OPENAPI_PEER_NAME, objectDescribe.getApiName(), objectData.getId())) {
            throw new ValidateException(I18N.text(I18NKey.GDPR_UNABLE_OPERATION, RequestContext.OPENAPI_PEER_NAME));
        }
    }


    private void processMultiCurrencyFields() {
        if (!ObjectDescribeExt.of(objectDescribe).containsMultiCurrencyField()) {
            return;
        }
        ObjectDataExt.of(objectData).removeEmptyMultiCurrencyFields();
        CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                dataList.forEach(data -> ObjectDataExt.of(data).removeEmptyMultiCurrencyFields()));
    }

    @Override
    protected void validate() {
        if (skipBaseValidate()) {
            return;
        }

        super.validate();

        //审批例外人不校验锁定状态和生命状态
        if (!isAssigneesExceptional) {
            //根据objectData的生命状态和锁定状态来判断数据是否有权限进行该操作
            serviceFacade.checkActionByLockStatusAndLifeStatus(objectData, ObjectAction.UPDATE, actionContext.getUser(), objectDescribe
                    .getApiName(), false);
            stopWatch.lap("checkActionByLockStatusAndLifeStatus");

            //校验数据的生命状态，变更中的数据不允许编辑
            validateDataLifeStatus();
        }
    }

    private void validateDataLifeStatus() {
        if (ObjectDataExt.of(objectData).isInChange()) {
            String name = StringUtils.trimToEmpty(objectData.getName());
            throw new ValidateException(I18N.text(I18NKey.ONGOING_APPROVAL_FLOW_AND_NO_CURRENT_OPERATION_POSSIBLE, name));
        }
    }

    /**
     * openApi更新时，校验唯一性规则、只是更新时的辅助、并不是像更新导入一样用来匹配数据
     * <p>
     * 1、查询唯一性规则，过滤掉查询结果中用户传来的id
     * 2、没有查到重复数据，使用用户传来的id直接更新
     * 3、查到重复数据、提示重复
     */
    @Override
    protected void checkUniquenessRule() {
        if (!actionContext.needCheckUniquenessRule()) {
            return;
        }
        if (Objects.isNull(uniqueRule) || !uniqueRule.isUseWhenCallOpenApi()) {
            return;
        }

        if (!UniqueRuleExt.isUseable(uniqueRule)) {
            log.warn("Uniqueness rule is in effect, describeApiName=>{}, user=>{}", objectDescribe.getApiName(),
                    JSON.toJSONString(actionContext.getUser()));
            throw new MetaDataBusinessException(I18N.text(I18NKey.UNIQUENESS_RULE_IS_IN_EFFECT));
        }
        // 唯一性规则接口查重、过滤结果中data的id
        Optional<UniqueRuleSearchResult.DuplicateData> duplicateData = getDuplicateDataByUniqueRuleSearch();
        // 没有查到重复数据、且更新时数据id不为空，则根据当前数据id更新
        if (duplicateData.isPresent()) {
            log.warn("find duplicate data tenantId=>{}, describeApiName=>{}, duplicateData=>{}",
                    actionContext.getTenantId(), objectDescribe.getApiName(), JSON.toJSONString(duplicateData));
            // 查到多条重复数据，提示重复
            throw new UniqueRuleValidationException(I18N.text(I18NKey.VALID_UNIQUENESS_MESSAGE_DUPLICATED,
                    UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribe)));
        }
    }

    @Override
    protected void validateDetail(String detailApiName, List<IObjectData> detailObjectData) {
        super.validateDetail(detailApiName, detailObjectData);

        List<IObjectData> dataToUpdate = detailsToUpdate.stream().filter(x -> detailApiName.equals(x.getDescribeApiName())).collect(Collectors.toList());
        List<IObjectData> dataToDelete = detailsToDelete.stream().filter(x -> detailApiName.equals(x.getDescribeApiName())).collect(Collectors.toList());

        if (CollectionUtils.empty(dataToUpdate) && CollectionUtils.empty(dataToDelete)) {
            return;
        }

        //审批例外人不校验锁定状态和生命状态
        if (isAssigneesExceptional) {
            return;
        }

        //根据objectData的生命状态和锁定状态来判断数据是否有权限进行该操作
        if (CollectionUtils.notEmpty(dataToDelete)) {
            serviceFacade.checkActionByLockStatusAndLifeStatus(dataToDelete, ObjectAction.INVALID,
                    actionContext.getUser(), detailApiName, false);
        }

        if (CollectionUtils.notEmpty(dataToUpdate)) {
            serviceFacade.checkActionByLockStatusAndLifeStatus(dataToUpdate, ObjectAction.UPDATE,
                    actionContext.getUser(), detailApiName, false);
        }
    }

    @Override
    protected BaseObjectSaveAction.Result doAct(A arg) {
        //增量更新的话在doAct做diff，防止子类在super.before()之后修改的字段更新不到库里
        if (isIncrementUpdate()) {
            //和数据库中的数据做diff
            diffObjectDataWithDbData();
            stopWatch.lap("diffObjectDataWithDbData");
        }

        //编辑审批中的数据，保存快照之后直接返回
        if (useSnapshot()) {
            saveSnapshot();
            stopWatch.lap("saveSnapshot");

            //审批过程中编辑数据，记录一条hidden=true的修改记录
            RequestUtil.setModifyLogHidden();
            //批量处理记录审计日志
            recordEditLog();
            stopWatch.lap("recordEditLog");

            //直接跳出action，忽略后边的逻辑
            throw new AcceptableValidateException(buildResult(objectData, detailObjectData));
        }

        resetLastLifeStatus();

        //尝试触发主对象的编辑审批流
        tryTriggerEditApprovalFlow();
        stopWatch.lap("tryTriggerEditApprovalFlow");

        handleQuoteValueBeforeCreate();
        stopWatch.lap("handleQuoteValueBeforeCreate");

        //更新主对象和从对象的数据
        if (!isEditApprovalFlowStartSuccess()) {
            doUpdateData();
            stopWatch.lap("doUpdateData");

            //设置写库标记
            writeDB = true;

            //尝试触发主对象的新建审批流
            tryTriggerAddApprovalFlow();
            stopWatch.lap("tryTriggerAddApprovalFlow");
        } else {
            //成功触发了编辑审批，记录一条hidden=true的修改记录
            RequestUtil.setModifyLogHidden();
            //批量处理记录审计日志
            recordEditLog();
            stopWatch.lap("recordEditLog");
        }
        return buildResult(objectData, detailObjectData);
    }

    protected void dealNPathSign(IObjectDescribe objDesc, List<IObjectData> dataList) {
        IActionContext ctx = ActionContextExt.of(actionContext.getUser()).getContext();
        infraServiceFacade.getFileStoreService().generateNPathSignedUrlForEdit(ctx,
                AppFrameworkConfig.signedUrlFieldTypes(objDesc.getTenantId()), objDesc, dataList);
    }

    private void dealDetailsNPathSign(Map<String, IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        if (MapUtils.isEmpty(detailDataMap)) {
            return;
        }
        detailDataMap.forEach((objectApiName, dataList) -> {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
                return;
            }
            IObjectDescribe detailDesc = detailDescribes.get(objectApiName);
            dealNPathSign(detailDesc, dataList);
        });
    }

    protected Result buildResult(IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                .isDuplicate(Boolean.FALSE)
                .triggerApproval(isApprovalFlowStartSuccess(objectData.getId()))
                .writeDB(writeDB)
                .dataChanged(isDataChanged())
                .build();
    }

    private boolean isDataChanged() {
        try {
            if (CollectionUtils.empty(updatedFieldMap) && CollectionUtils.empty(detailChangeMap)) {
                return false;
            }
            Map<String, Object> masterToUpdateMap = ObjectDataExt.of(copyAndRemoveSpecialFields(objectDescribe.getApiName(),
                    ObjectDataExt.of(updatedFieldMap).getObjectData())).toMap();
            ObjectDataExt.of(masterToUpdateMap).removeFieldForLog(objectDescribe);
            Set<String> detailApiNamesToUpdate = Sets.newHashSet();
            detailChangeMap.forEach((detailApiName, changeMap) -> {
                //先获取从对象的diff结果
                Map<String, Map<String, Object>> updateDataMap = (Map<String, Map<String, Object>>) changeMap.get(ObjectAction.UPDATE.getActionCode());
                if (CollectionUtils.empty(updateDataMap)) {
                    return;
                }
                for (Map.Entry<String, Map<String, Object>> entry : updateDataMap.entrySet()) {
                    IObjectData cpData = copyAndRemoveSpecialFields(detailApiName, ObjectDataExt.of(entry.getValue()).getObjectData());
                    IObjectDescribe detailDescribe = objectDescribes.get(detailApiName);
                    if (Objects.nonNull(detailDescribe)) {
                        ObjectDataExt.of(cpData).removeFieldForLog(detailDescribe);
                    }
                    if (CollectionUtils.notEmpty(ObjectDataExt.of(cpData).toMap())) {
                        detailApiNamesToUpdate.add(detailApiName);
                        break;
                    }
                }
            });
            return CollectionUtils.notEmpty(masterToUpdateMap) || CollectionUtils.notEmpty(detailApiNamesToUpdate)
                    || CollectionUtils.notEmpty(detailsToAdd) || CollectionUtils.notEmpty(detailsToDelete);
        } catch (Throwable e) {
            log.warn("get isDataChanged failed", e);
            return true;
        }
    }

    @Override
    protected BaseObjectSaveAction.Result after(A arg, BaseObjectSaveAction.Result result) {
        //成功触发审批流不需要执行后续操作
        if (isEditApprovalFlowStartSuccess()) {
            return result;
        }
        updateDuplicateSearchData();
        stopWatch.lap("updateDuplicateSearchData");

        super.after(arg, result);
        stopWatch.lap("super.after");

        //批量处理记录审计日志
        recordEditLog();
        stopWatch.lap("recordEditLog");

        //触发新建工作流
        startCreateWorkFlow();
        stopWatch.lap("startCreateWorkFlow");

        // sendAction
        // Mq:http://wiki.firstshare.cn/pages/viewpage.action?pageId=37088911
        sendActionMq();
        stopWatch.lap("sendActionMq");

        return result;
    }

    @Override
    protected void customProcessResult(Result result) {
        // 处理 signature
        dealNPathSign(objectDescribe, Lists.newArrayList(objectData));
        dealDetailsNPathSign(detailDescribeMap, detailObjectData);

        processDataForDisplay();
        stopWatch.lap("processDataForDisplay");

        result.setObjectData(ObjectDataDocument.of(objectData));
        super.customProcessResult(result);
    }

    private void updateDuplicateSearchData() {
        if (!isIncrementUpdate()) {
            return;
        }
        try {
            IObjectData afterData = ObjectDataExt.of(ObjectDataExt.of(objectData).copy()).merge(dbMasterData).getObjectData();
            List<IDuplicatedSearch> duplicatedSearchList = serviceFacade.getDuplicateSearchListByType(actionContext.getTenantId(), objectDescribe.getApiName(), IDuplicatedSearch.Type.NEW);
            serviceFacade.handleDuplicateSearchRule(duplicatedSearchList, Lists.newArrayList(afterData), objectDescribe, actionContext.getUser());
        } catch (Exception e) {
            log.error("updateDuplicateSearchData error ", e);
        }
    }

    private void saveSnapshot() {
        Map<String, Object> masterSnapshot = getCallbackData();
        ObjectDataSnapshot objectDataSnapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(masterSnapshot)
                .detailSnapshot(detailChangeMap)
                .biz(ApprovalFlow.getCode())
                .bizId(arg.getBizInfo().getOtherBizId())
                .subBizId(arg.getBizInfo().getBizId())
                .build();
        infraServiceFacade.createSnapshot(actionContext.getUser(), actionContext.getObjectApiName(), objectData.getId(), objectDataSnapshot);
    }

    private void processDataForDisplay() {
        if (!RequestUtil.isCepRequest()) {
            return;
        }
        serviceFacade.fillCurrencyFieldInfo(objectDescribe, Lists.newArrayList(objectData), actionContext.getUser());
        //下游快速编辑富文本时，提交后详情页不刷新，显示的富文本图片有问题，在这里处理一下返回值
        serviceFacade.fillRichTextImageInfo(objectDescribe, Lists.newArrayList(objectData), actionContext.getUser());
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        //审批过程中编辑数据不需要再次触发审批
        if (useSnapshot()) {
            return false;
        }
        //arg里的参数优先级比queryParameter更高，cep请求不支持此参数
        if (arg.skipApprovalFlow() && !RequestUtil.isCepRequest()) {
            return false;
        }
        return super.needTriggerApprovalFlow();
    }

    private void tryTriggerEditApprovalFlow() {
        if (!needTriggerApprovalFlow()) {
            return;
        }

        Map<String, Object> normalUpdateMap = ObjectDataExt.of(Maps.newHashMap(updatedFieldMapForApproval))
                .removeCalculateField(objectDescribe).toMap();
        if (CollectionUtils.empty(normalUpdateMap) && !detailChangeForApproval) {
            return;
        }

        //只编辑了从对象的非灰度企业不触发审批
        if (CollectionUtils.empty(normalUpdateMap)
                && !AppFrameworkConfig.isInMasterDetailApprovalGrayList(actionContext.getTenantId())) {
            return;
        }

        //如果是单独编辑从对象，尝试触发主对象的审批，否则normal状态触发编辑审批
        Map<String, Object> callbackData = getCallbackData();
        if (needTriggerMasterApproval()) {
            tryTriggerMasterApproval(ApprovalFlowTriggerType.UPDATE, ObjectAction.UPDATE, objectData, callbackData,
                    objectDescribes, arg.getFreeApprovalDef());
        } else if (ObjectDataExt.of(objectData).isNormal()
                && !ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMasterAndInGrayList()) {
            startApprovalFlow(ApprovalFlowTriggerType.UPDATE, objectData, updatedFieldMapForApproval, callbackData,
                    detailChangeMap, objectDescribes, arg.getFreeApprovalDef());
        }
        recordLifeStatusModifyLog();
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus();
    }

    private Map<String, Object> getCallbackData() {
        Map<String, Object> callbackData = Maps.newHashMap(updatedFieldMap);
        callbackData.putAll(customCallbackData);
        return callbackData;
    }

    private void tryTriggerAddApprovalFlow() {
        if (!needTriggerApprovalFlow()) {
            return;
        }

        //如果生命状态变了，说明已经触发了编辑审批
        if (isLifeStatusChanged()) {
            return;
        }

        //如果是单独编辑从对象，尝试触发主对象的审批，否则ineffective状态触发新建审批
        if (needTriggerMasterApproval()) {
            tryTriggerMasterApproval(ApprovalFlowTriggerType.CREATE, ObjectAction.UPDATE, objectData, updatedFieldMap,
                    objectDescribes, arg.getFreeApprovalDef());
        } else if (ObjectDataExt.of(objectData).isIneffective() && !ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMasterAndInGrayList()) {
            //有从对象的话将创建时间存起来，审批通过回调的时候需要使用
            Long detailCreateTime = hasDetails() ? objectData.getCreateTime() : null;
            Map<String, Map<String, Object>> callbackDataMap = buildCallbackDataForAddAction(objectData, detailCreateTime);
            startApprovalFlow(Lists.newArrayList(objectData), ApprovalFlowTriggerType.CREATE, Maps.newHashMap(),
                    callbackDataMap, arg.getFreeApprovalDef());
        }
        recordLifeStatusModifyLog();
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus();
    }

    private boolean hasDetails() {
        if (CollectionUtils.notEmpty(arg.getDetails())) {
            return true;
        }
        return serviceFacade.isMasterObject(actionContext.getTenantId(), objectDescribe.getApiName());
    }

    private List<IObjectData> fillOldData(List<IObjectData> updatedDataList) {
        if (!Objects.equals(Utils.NEW_OPPORTUNITY_API_NAME, objectDescribe.getApiName())) {
            return updatedDataList;
        }

        return ObjectDataDocument.fillOldData(updatedDataList, Lists.newArrayList(ObjectDataExt.of(dbMasterData).copy()));
    }

    private boolean needVersionConflictErrorMessage() {
        if (arg.skipVersionCheck()) {
            return false;
        }
        if (CollectionUtils.empty(arg.getOriginalData()) && CollectionUtils.empty(arg.getOriginalDetails())) {
            return true;
        }
        return notSupportDataConflictsMerge();
    }

    protected void prepareMasterObjectData() {
        //校验主数据的版本号
        isDataVersionConflict = ObjectDataExt.checkIfDataVersionConflict(objectData, dbMasterData);
        if (isDataVersionConflict && needVersionConflictErrorMessage()) {
            throw new ValidateException(I18N.text(I18NKey.DATA_EXPIRED));
        }

        //将数据加工一下，目前主要是将图片、附件从临时文件转成正式文件
        serviceFacade.processData(objectDescribe, Lists.newArrayList(objectData));

        convertPathForRichText(objectData, objectDescribe);

        // 去掉入参中的负责人和相关团队、避免出现数据不一致的情况
        removeDataOwnerAndTeamMember();

        //处于编辑审批中的数据需要合并快照
        if (Objects.nonNull(snapshot)) {
            snapshot.mergeIntoMasterData(dbMasterData);
        }
        modifyObjectDataByDbData(objectData, dbMasterData);
    }

    protected void prepareDetailObjectData() {
        if (CollectionUtils.empty(detailObjectData)) {
            return;
        }

        //先找到其已经关联了的所有的从对象的数据。
        findDbDetailDataMap();
        if (Objects.nonNull(snapshot)) {
            snapshot.mergeIntoDetailData(dbDetailDataMap);
        }

        //将从对象的数据分成更新、删除、新增三种集合
        classifyDetailData();
        for (Map.Entry<String, List<IObjectData>> entry : detailObjectData.entrySet()) {
            String detailApiName = entry.getKey();
            //1. 准备数据:获取从对象的新dataList以及idList。
            List<IObjectData> newDetailDataList = entry.getValue();

            //将数据加工一下，目前主要是将图片、附件从临时文件转成正式文件
            serviceFacade.processData(objectDescribes.get(detailApiName), newDetailDataList);

            //2. 准备数据:获取从对象的原dataList以及idList。
            List<IObjectData> oldDetailDataList = dbDetailDataMap.getOrDefault(detailApiName, Lists.newArrayList());
            Map<String, IObjectData> oldDataMap = oldDetailDataList.stream().collect(Collectors.toMap(IObjectData::getId, x -> x));

            //3. 轮询所有的OldDetailObjectData,根据id和将要更新的NewDetailObjectData进行比对。
            detailsToUpdate.stream().filter(x -> Objects.equals(x.getDescribeApiName(), detailApiName)).forEach(x -> {
                IObjectData dbData = oldDataMap.get(x.getId());
                //校验从数据的版本号
                boolean versionConflict = ObjectDataExt.checkIfDataVersionConflict(x, dbData);
                if (versionConflict) {
                    versionConflictDetails.add(detailApiName);
                    if (needVersionConflictErrorMessage()) {
                        throw new ValidateException(I18N.text(I18NKey.DATA_EXPIRED));
                    }
                }
                modifyObjectDataByDbData(x, dbData);
                ObjectDataExt.of(x).remove(ObjectDataExt.RELEVANT_TEAM);
            });

            //4. 补全新建数据的信息
            List<IObjectData> dataToAdd = detailsToAdd.stream()
                    .filter(x -> Objects.equals(x.getDescribeApiName(), detailApiName))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(dataToAdd)) {
                modifyDetailObjectDataToAddWhenEdit(objectData, detailApiName, dataToAdd);
                addMasterDetailFieldIntoDetailDataList(objectData.getId(), objectDescribes.get(detailApiName), dataToAdd);
            }
        }

        if (isFromChangeOrder() && ObjectDescribeExt.of(objectDescribe).isChangeOrderObject()) {
            List<IObjectData> noTemporaryDataList = Lists.newArrayList();
            try {
                // 主对象的生效状态修改为未生效
                ObjectDataExt.of(objectData).setChangeOrderEffectiveStatus(ChangeOrderEffectiveStatus.INEFFECTIVE);
                noTemporaryDataList.addAll(ObjectDataExt.fillTemporaryId(detailObjectData));
                noTemporaryDataList.addAll(ObjectDataExt.fillTemporaryId(dbDetailDataMap));

                // 修改从对象的变更状态
                detailObjectData.forEach((describeApiName, objectDataList) -> {
                    MergeStateContainer mergeStateContainer = getMergeStateContainer(objectDataList);
                    List<IObjectData> dbDetailDataList = dbDetailDataMap.getOrDefault(describeApiName, Collections.emptyList());
                    MergeStateContainer dbStateContainer = getMergeStateContainer(dbDetailDataList);
                    dbStateContainer.changeState(mergeStateContainer, (currentMergeStateInfo, sourceMergeStatusInfo) -> {
                        ObjectDataExt dataExt = ObjectDataExt.of(sourceMergeStatusInfo.getData());
                        if (currentMergeStateInfo.isAdd()) {
                            dataExt.setChangedType(CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD);
                        } else if (currentMergeStateInfo.isDelete()) {
                            dataExt.setChangedType(CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_DELETED);
                        } else {
                            dataExt.setChangedType(CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE);
                        }
                    });
                });
            } finally {
                ObjectDataExt.removeTemporaryId(noTemporaryDataList);
            }
        }
    }

    private MergeStateContainer getMergeStateContainer(List<IObjectData> objectDataList) {
        List<IObjectData> addList = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> deleteList = Lists.newArrayList();
        for (IObjectData data : objectDataList) {
            String changeType = ObjectDataExt.of(data).getChangedType();
            if (CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD.equals(changeType)) {
                addList.add(data);
            } else if (CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE.equals(changeType)) {
                updateList.add(data);
            } else if (CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_DELETED.equals(changeType)) {
                deleteList.add(data);
            }
        }
        return MergeStateContainer.of(addList, updateList, deleteList);
    }

    @Override
    protected final Map<String, List<IObjectData>> findDbDetailDataMap() {
        if (findDbDetailDataFlag) {
            return dbDetailDataMap;
        }
        List<IObjectDescribe> detailDescribes = Lists.newArrayList(detailDescribeMap.values()).stream()
                .filter(x -> detailObjectData.containsKey(x.getApiName())).collect(Collectors.toList());
        dbDetailDataMap = serviceFacade.findDetailObjectDataList(detailDescribes, objectData, actionContext.getUser());
        findDbDetailDataFlag = true;
        return dbDetailDataMap;
    }

    private void classifyDetailData() {
        if (CollectionUtils.empty(detailObjectData)) {
            return;
        }

        detailsToAdd = Lists.newArrayList();
        detailsToUpdate = Lists.newArrayList();
        detailsToDelete = Lists.newArrayList();

        for (Map.Entry<String, List<IObjectData>> entry : detailObjectData.entrySet()) {
            String detailApiName = entry.getKey();
            //1. 准备数据:获取从对象的新dataList以及idList。
            List<IObjectData> newDetailDataList = entry.getValue();
            Set<String> newIdList = newDetailDataList.stream()
                    .map(IObjectData::getId)
                    .filter(id -> !Strings.isNullOrEmpty(id))
                    .collect(Collectors.toSet());

            //2. 准备数据:获取从对象的原dataList以及idList。
            List<IObjectData> oldDetailDataList = dbDetailDataMap.getOrDefault(detailApiName, Lists.newArrayList());
            Map<String, IObjectData> oldDataMap = oldDetailDataList.stream().collect(Collectors.toMap(IObjectData::getId, x -> x));

            //更新的从数据
            List<IObjectData> dataToUpdate = newDetailDataList.stream()
                    .filter(x -> !Strings.isNullOrEmpty(x.getId()) && oldDataMap.containsKey(x.getId()))
                    .collect(Collectors.toList());
            detailsToUpdate.addAll(dataToUpdate);

            //删除的从数据
            List<IObjectData> dataToDelete = oldDetailDataList.stream().filter(x -> !newIdList.contains(x.getId())).collect(Collectors.toList());
            detailsToDelete.addAll(dataToDelete);

            //新建的从数据
            List<IObjectData> dataToAdd = newDetailDataList.stream()
                    .filter(x -> Strings.isNullOrEmpty(x.getId()) || !oldDataMap.containsKey(x.getId()))
                    .peek(x -> ObjectDataExt.of(x).setIsNewData())
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(dataToAdd)) {
                //新建的从对象的统计字段需要根据字段上的属性进行格式化
                IObjectDescribe detailDescribe = objectDescribes.get(detailApiName);
                ObjectDescribeExt.of(detailDescribe).getCountFields().forEach(count -> {
                    Object value = CountExt.of(count).formatResult(null);
                    dataToAdd.forEach(data -> data.set(count.getApiName(), value));
                });
                detailsToAdd.addAll(dataToAdd);
            }
        }
    }

    private void processDataConflicts() {
        // 从变更单创建数据时，无需合并
        if (isFromChangeOrder()) {
            return;
        }
        //没有传V0数据，无需合并
        if (CollectionUtils.empty(arg.getOriginalData()) && CollectionUtils.empty(arg.getOriginalDetails())) {
            return;
        }

        //启用了对象白名单但是没有配置白名单，无需合并
        if (notSupportDataConflictsMerge()) {
            return;
        }

        //根据V0、V1、V2三个版本处理主数据和从数据的冲突
        DataConflictsProcessor.Result processResult = DataConflictsProcessor.builder()
                .saveActionServiceFacade(saveActionServiceFacade)
                .user(actionContext.getUser())
                .seriesId(arg.getSeriesId())
                .isDataVersionConflict(isDataVersionConflict)
                .versionConflictDetails(versionConflictDetails)
                .masterDescribe(objectDescribe)
                .describeMap(objectDescribes)
                .originalData(originalData)
                .originalDetailDataMap(originalDetailDataMap)
                .dbMasterData(dbMasterData)
                .dbDetailDataMap(dbDetailDataMap)
                .objectData(objectData)
                .detailDataMap(detailObjectData)
                .build()
                .process();

        DataConflicts dataConflicts = processResult.getDataConflicts();
        Map<String, Map<String, DataConflicts>> detailConflicts = processResult.getDetailConflicts();
        //第一次提交需要提示冲突信息
        if (!arg.skipVersionCheck() && (Objects.nonNull(dataConflicts) || CollectionUtils.notEmpty(detailConflicts))) {
            //补充__r等信息供前端展示
            if (Objects.nonNull(dataConflicts)) {
                List<IObjectData> objectDataList = Lists.newArrayList(dataConflicts.getLastData().toObjectData(),
                        dataConflicts.getCurrentData().toObjectData());
                fillExtendFieldInfo(objectDescribe, objectDataList);
                // 冲突校验的结果需要加掩码
                fillMaskFieldValue(actionContext.getUser(), objectDescribe, objectDataList);
            }
            if (CollectionUtils.notEmpty(detailConflicts)) {
                detailConflicts.forEach((detailApiName, conflictsMap) -> {
                    List<IObjectData> dataList = conflictsMap.values().stream().flatMap(x -> Lists.newArrayList(x.getLastData().toObjectData(),
                            x.getCurrentData().toObjectData()).stream()).collect(Collectors.toList());
                    IObjectDescribe detailDescribe = objectDescribes.get(detailApiName);
                    fillExtendFieldInfo(detailDescribe, dataList);
                    // 冲突校验的结果需要加掩码
                    fillMaskFieldValue(actionContext.getUser(), detailDescribe, dataList);
                });
            }
            Result result = Result.builder()
                    .versionCheckBlocked(true)
                    .dataConflicts(dataConflicts)
                    .detailConflicts(detailConflicts)
                    .build();
            throw new AcceptableValidateException(result);
        }
        //重置arg中的从数据
        if (processResult.isDetailChanged()) {
            refreshDetailData();
        }
    }

    private boolean notSupportDataConflictsMerge() {
        String tenantId = actionContext.getTenantId();
        String objectApiName = actionContext.getObjectApiName();
        String formatObjectApiName = UdobjGrayConfig.isCustomObject(objectApiName) ? UdobjGrayConfigKey.UDOBJ : objectApiName;
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DATA_CONFLICT_MERGE_OBJECTS_GRAY_EI, actionContext.getTenantId())
                && !UdobjGrayConfig.isAllowByKeys(new String[]{UdobjGrayConfigKey.DATA_CONFLICT_MERGE_OBJECTS_GRAY, tenantId}, objectApiName)
                && !UdobjGrayConfig.isAllowByKeys(new String[]{UdobjGrayConfigKey.DATA_CONFLICT_MERGE_OBJECTS_GRAY, tenantId}, formatObjectApiName);
    }

    private void refreshDetailData() {
        setOrderForDetailData(detailObjectData);
        classifyDetailData();
    }

    protected void diffObjectDataWithDbData() {
        diffMasterData();
        diffDetailData();
    }

    private void removeDataOwnerAndTeamMember() {
        ObjectDataExt.of(objectData).removeDataOwner();
        ObjectDataExt.of(objectData).remove(ObjectDataExt.RELEVANT_TEAM);
    }

    protected void diffMasterData() {
        updatedFieldMap = Maps.newHashMap();
        updatedFieldMapForApproval = Maps.newHashMap();
        Map<String, Object> updateFields = ObjectDataExt.of(dbMasterData).diff(objectData, objectDescribe);
        if (CollectionUtils.notEmpty(updateFields)) {
            ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(updateFields));
            dataExt.removeInvalidFieldForApproval(objectDescribe);
            dataExt.remove(getIgnoreFieldsForApproval());
            updatedFieldMapForApproval.putAll(dataExt.toMap());
            removeHiddenFieldsByDomainPlugin(updatedFieldMapForApproval, objectDescribe.getApiName());

            //callback_data去掉业务特殊字段
            ObjectDataExt.of(updateFields).remove(getIgnoreFieldsForApproval());
            updatedFieldMap.putAll(updateFields);
        }
    }

    protected void diffDetailData() {
        //给新增的从对象补充id，数据快照需要使用
        ObjectDataExt.fillDataId(detailsToAdd);
        Map<String, List<IObjectData>> detailToAddMap = groupDetailsByDescribeApiName(detailsToAdd);
        Map<String, List<IObjectData>> detailToDeleteMap = groupDetailsByDescribeApiName(detailsToDelete);
        Map<String, List<IObjectData>> detailToUpdateMap = groupDetailsByDescribeApiName(detailsToUpdate);
        detailChangeMap = Maps.newHashMap();

        detailToAddMap.forEach((k, v) -> {
            detailChangeMap.putIfAbsent(k, Maps.newHashMap());
            detailChangeMap.get(k).put(ObjectAction.CREATE.getActionCode(), ObjectDataDocument.ofList(ObjectDataExt.copyList(v)));
        });

        detailToDeleteMap.forEach((k, v) -> {
            detailChangeMap.putIfAbsent(k, Maps.newHashMap());
            detailChangeMap.get(k).put(ObjectAction.DELETE.getActionCode(), v.stream().map(x -> x.getId()).collect(Collectors.toList()));
        });

        detailChangeForApproval = CollectionUtils.notEmpty(detailChangeMap);

        detailToUpdateMap.forEach((k, v) -> {
            IObjectDescribe describe = objectDescribes.get(k);
            Map<String, IObjectData> dbDataMap = dbDetailDataMap.get(k).stream().filter(x -> ObjectDataExt.of(x).hasId())
                    .collect(Collectors.toMap(x -> x.getId(), x -> x));
            Map<String, ObjectDataDocument> updateMap = Maps.newHashMap();
            v.forEach(data -> {
                IObjectData dbData = dbDataMap.get(data.getId());
                Map<String, Object> updateFields = ObjectDataExt.of(dbData).diff(data, describe);
                if (CollectionUtils.notEmpty(updateFields)) {
                    updateMap.put(data.getId(), ObjectDataDocument.of(updateFields));
                }
            });
            if (CollectionUtils.notEmpty(updateMap)) {
                detailChangeMap.putIfAbsent(k, Maps.newHashMap());
                detailChangeMap.get(k).put(ObjectAction.UPDATE.getActionCode(), updateMap);

                if (!detailChangeForApproval) {
                    detailChangeForApproval = updateMap.values().stream().anyMatch(x -> {
                        Map<String, Object> copyMap = Maps.newHashMap(x);
                        ObjectDataExt.of(copyMap).removeInvalidFieldForApproval(describe);
                        ObjectDataExt.of(copyMap).removeCalculateField(describe);
                        return CollectionUtils.notEmpty(copyMap);
                    });
                }
            }
        });
    }

    protected void modifyObjectDataByDbData(IObjectData newData, IObjectData dbData) {
        IObjectDescribe objectDescribe = objectDescribes.get(dbData.getDescribeApiName());
        Map<String, String> selectOneDataTypeMap = objectDescribe.getFieldDescribes().stream()
                .filter(a -> Objects.equals(a.getType(), IFieldType.SELECT_ONE))
                .collect(Collectors.toMap(x -> x.getApiName() + "__o", IFieldDescribe::getApiName));
        Map<String, String> selectManyDataTypeMap = objectDescribe.getFieldDescribes().stream()
                .filter(a -> Objects.equals(a.getType(), IFieldType.SELECT_MANY))
                .collect(Collectors.toMap(x -> x.getApiName() + "__o", IFieldDescribe::getApiName));

        ObjectDataExt.of(dbData).toMap().keySet().forEach(x -> {
            if (ObjectDataExt.of(newData).toMap().containsKey(x)) {
                return;
            }
            Object newDataSelectOne = newData.get(selectOneDataTypeMap.get(x));
            if (Objects.nonNull(selectOneDataTypeMap.get(x)) && Objects.nonNull(newDataSelectOne)
                    && !SelectOne.OPTION_OTHER_VALUE.equals(newDataSelectOne)) {
                return;
            }
            if (Objects.nonNull(selectManyDataTypeMap.get(x)) && Objects.nonNull(newData.get(selectManyDataTypeMap.get(x)))) {
                List<String> selectManyValue = (List<String>) newData.get(selectManyDataTypeMap.get(x));
                if (!selectManyValue.contains(SelectOne.OPTION_OTHER_VALUE)) {
                    return;
                }
            }
            newData.set(x, dbData.get(x));
        });
        //同步一下生命状态和锁定状态
        ObjectDataExt.of(newData).setLifeStatus(ObjectDataExt.of(dbData).getLifeStatus());
        ObjectDataExt.of(newData).setLockStatus(ObjectDataExt.of(dbData).getLockStatus());
        // 移除图片中的 signedUrl
        ObjectDataExt.of(newData).removeSignedUrl(objectDescribe);
    }

    protected void doUpdateData() {
        // 创建变更单
        doCreateChangeOrder();

        IObjectData objectDataCp = copyAndRemoveSpecialFields(objectDescribe.getApiName(), objectData);
        stopWatch.lap("doUpdateData-copyAndRemoveSpecialFields");
        Map<String, List<IObjectData>> detailToAddMap = getSortedDetailDataMap(detailsToAdd);
        stopWatch.lap("doUpdateData-getSortedDetailDataMap-toAdd");

        Map<String, List<IObjectData>> detailToUpdateMap;
        if (isIncrementUpdate()) {
            //灰度了增量更新的只更新diff的字段
            detailToUpdateMap = getDetailUpdateMapForIncrementUpdate();
        } else if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SKIP_NO_CHANGE_DETAILS_IN_EDIT_ACTION_EI, actionContext.getTenantId())) {
            //灰度企业只更新有变化的从对象
            detailToUpdateMap = getUpdateMapFromDetailChangeMap();
        } else {
            detailToUpdateMap = getSortedDetailDataMap(detailsToUpdate.stream()
                    .map(x -> copyAndRemoveSpecialFields(x.getDescribeApiName(), x))
                    .collect(Collectors.toList()));
        }
        stopWatch.lap("doUpdateData-getSortedDetailDataMap-toUpdate");
        Map<String, List<IObjectData>> detailToDeleteMap = groupDetailsByDescribeApiName(detailsToDelete);
        stopWatch.lap("doUpdateData-groupDetailsByDescribeApiName");
        updateMasterAndDetail(objectDataCp, detailToAddMap, detailToUpdateMap, detailToDeleteMap);
        stopWatch.lap("doUpdateData-updateMasterAndDetail");

        //如果新增了从对象，则往redis里加一把锁，防止用户继续保存出现错误数据
        if (CollectionUtils.notEmpty(detailToAddMap)) {
            saveActionServiceFacade.tryLockWithUpdateKey(arg, actionContext.getObjectApiName(), actionContext.getUser());
        }

        //更新数据中的version
        objectData.setVersion(objectDataCp.getVersion());
//        ObjectDataExt.of(objectData).handleMultiLangField(objectDescribe);
        updatedDataList.add(objectData);
        detailsToUpdate.stream().filter(x -> detailToUpdateMap.containsKey(x.getDescribeApiName())).forEach(x -> {
            detailToUpdateMap.get(x.getDescribeApiName()).stream().filter(y -> y.getId().equals(x.getId()))
                    .findFirst().ifPresent(y -> {
                        x.setVersion(y.getVersion());
                        updatedDataList.add(x);
                    });
        });
    }

    private void doCreateChangeOrder() {
        if (!isChangeOrderAction()) {
            return;
        }
        if (!ChangeOrderConfig.changeOrderDescribeGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return;
        }
        if (!ObjectDescribeExt.of(objectDescribe).enabledChangeOrder()) {
            return;
        }
        CreateChangeOrder.Arg changeArg = buildChangeArg();

        CreateChangeOrder.Result changeResult = processChangeOrderRequest(changeArg);
        if (!changeResult.isSuccess()) {
            throw new ValidateException(changeResult.getMessage());
        }
        throw new AcceptableValidateNoRollbackException(buildChangeResult(changeResult.getChangeOrderApiName(), changeResult.getChangeOrderDataId()));
    }

    private CreateChangeOrder.Result processChangeOrderRequest(CreateChangeOrder.Arg changeArg) {
        if ("change_order".equals(arg.getActionType())) {
            return infraServiceFacade.doCreateChangeOrder(actionContext.getUser(), changeArg);
        }
        return infraServiceFacade.doModifyChangeOrder(actionContext.getUser(), changeArg);
    }

    private boolean isChangeOrderAction() {
        return ChangeOrderConfig.isChangeOrderAction(arg.getActionType());
    }

    private CreateChangeOrder.Arg buildChangeArg() {
        IObjectData objectDataCp = copyAndRemoveSpecialFields(objectDescribe.getApiName(), objectData);
        Map<String, List<IObjectData>> detailToAddMap = getSortedDetailDataMap(detailsToAdd);
        Map<String, List<IObjectData>> detailToUpdateMap = getSortedDetailDataMap(detailsToUpdate.stream()
                .map(x -> copyAndRemoveSpecialFields(x.getDescribeApiName(), x))
                .collect(Collectors.toList()));
        Map<String, List<IObjectData>> detailToDeleteMap = groupDetailsByDescribeApiName(detailsToDelete);

        CreateChangeOrder.Arg changeArg = new CreateChangeOrder.Arg();
        changeArg.setDescribe(objectDescribe);
        changeArg.setMasterData(objectDataCp);
        changeArg.setDetailToAddMap(detailToAddMap);
        changeArg.setDetailToUpdateMap(detailToUpdateMap);
        changeArg.setDetailToDeleteMap(detailToDeleteMap);
        changeArg.setOriginalData(dbMasterData);
        changeArg.setOriginalDetails(dbDetailDataMap);
        changeArg.setCallBackData(getChangeOrderCallbackData());
        return changeArg;
    }

    protected Map<String, Object> getChangeOrderCallbackData() {
        Map<String, Object> result = Maps.newHashMap();
        result.put(ExtraDataKeys.TRIGGER_WORK_FLOW, needTriggerWorkFlow());
        return result;
    }

    private Result buildChangeResult(String changeOrderApiName, String changeOrderDataId) {
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                .isDuplicate(Boolean.FALSE)
                .changeOrderApiName(changeOrderApiName)
                .changeOrderDataId(changeOrderDataId)
                .build();
    }


    private Map<String, List<IObjectData>> getDetailUpdateMapForIncrementUpdate() {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        detailChangeMap.forEach((apiName, changeMap) -> {
            //先获取从对象的diff结果
            Map<String, Map<String, Object>> updateDataMap = (Map<String, Map<String, Object>>) changeMap.get(ObjectAction.UPDATE.getActionCode());
            if (CollectionUtils.empty(updateDataMap)) {
                return;
            }
            //将diff结果拷贝一份用于更新
            List<IObjectData> toUpdateList = Lists.newArrayList();
            updateDataMap.forEach((id, fieldMap) -> {
                IObjectData data = ObjectDataExt.of(fieldMap).getObjectData();
                IObjectData cpData = copyAndRemoveSpecialFields(apiName, data);
                if (CollectionUtils.notEmpty(ObjectDataExt.of(cpData).toMap())) {
                    cpData.setTenantId(actionContext.getTenantId());
                    cpData.setDescribeApiName(apiName);
                    cpData.setId(id);
                    toUpdateList.add(cpData);
                }
            });
            if (CollectionUtils.notEmpty(toUpdateList)) {
                result.put(apiName, toUpdateList);
            }
        });
        return result;
    }

    private Map<String, List<IObjectData>> getUpdateMapFromDetailChangeMap() {
        Map<String, List<IObjectData>> detailToUpdateMap = Maps.newHashMap();
        detailChangeMap.forEach((apiName, changeMap) -> {
            Map<String, Object> updateDataMap = (Map<String, Object>) changeMap.get(ObjectAction.UPDATE.getActionCode());
            if (CollectionUtils.empty(updateDataMap)) {
                return;
            }
            List<IObjectData> toUpdateList = detailsToUpdate.stream()
                    .filter(x -> updateDataMap.containsKey(x.getId()))
                    .map(x -> copyAndRemoveSpecialFields(apiName, x))
                    .collect(Collectors.toList());
            detailToUpdateMap.put(apiName, toUpdateList);
        });
        return detailToUpdateMap;
    }

    protected void updateMasterAndDetail(IObjectData objectDataCp,
                                         Map<String, List<IObjectData>> detailToAddMap,
                                         Map<String, List<IObjectData>> detailToUpdateMap,
                                         Map<String, List<IObjectData>> detailToDeleteMap) {
        Map<String, Object> masterUpdateMap = ObjectDataExt.of(copyAndRemoveSpecialFields(objectDescribe.getApiName(),
                ObjectDataExt.of(updatedFieldMap).getObjectData())).toMap();
        UpdateMasterAndDetailData.Arg updateArg = UpdateMasterAndDetailData.Arg.builder()
                .incrementUpdate(isIncrementUpdate())
                .incrementUpdateDetail(isIncrementUpdate())
                .toUpdateMap(masterUpdateMap)
                .masterObjectData(objectDataCp)
                .detailsToAdd(detailToAddMap)
                .detailsToUpdate(detailToUpdateMap)
                .detailsToDelete(detailToDeleteMap)
                .convertRuleDataContainer(convertRuleDataContainer)
                .actionType("editAction")
                .realTimeCalculateDetailAuth(arg.realTimeCalculateDetailAuth())
                .build();
        serviceFacade.updateMasterAndDetailData(actionContext.getUser(), updateArg);
        modifyOriginalDataByChangeOrder(updateArg);
    }

    private void modifyOriginalDataByChangeOrder(UpdateMasterAndDetailData.Arg updateArg) {
        if (!ObjectDescribeExt.of(objectDescribe).isChangeOrderObject() || !isFromChangeOrder()) {
            return;
        }
        if (Objects.isNull(originalData)) {
            return;
        }
        if (CollectionUtils.notEmpty(detailsToUpdate) && CollectionUtils.notEmpty(updateArg.getDetailsToUpdate())) {
            Map<String, List<IObjectData>> updateDataMap = getSortedDetailDataMap(detailsToUpdate);
            updateArg.getDetailsToUpdate().forEach((describeApiName, dataList) -> {
                Map<String, String> id2OriginalDetailDataMap = Maps.newHashMap();
                for (IObjectData data : CollectionUtils.nullToEmpty(updateDataMap.get(describeApiName))) {
                    ObjectDataExt dataExt = ObjectDataExt.of(data);
                    String id = dataExt.getId();
                    String originalDetailDataId = dataExt.getOriginalDetailDataId();
                    if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(originalDetailDataId)) {
                        id2OriginalDetailDataMap.put(id, originalDetailDataId);
                    }
                }
                for (IObjectData data : dataList) {
                    String originalDetailDataId = id2OriginalDetailDataMap.get(data.getId());
                    if (StringUtils.isNotBlank(originalDetailDataId)) {
                        ObjectDataExt.of(data).setOriginalDetailDataId(originalDetailDataId);
                    }
                }
            });
        }
        infraServiceFacade.modifyChangeOrderOriginalData(actionContext.getUser(), updateArg, originalData, originalDetailDataMap);
    }

    protected boolean isIncrementUpdate() {
        return AppFrameworkConfig.isIncrementUpdateInEditAction(actionContext.getTenantId(), actionContext.getObjectApiName());
    }

    private Map<String, List<IObjectData>> getSortedDetailDataMap(List<IObjectData> detailDataList) {
        return groupDetailsByDescribeApiName(detailDataList);
    }

    private Map<String, List<IObjectData>> groupDetailsByDescribeApiName(List<IObjectData> detailDataList) {
        return ObjectDataExt.groupByDescribeApiName(detailDataList);
    }

    private IObjectData copyAndRemoveSpecialFields(String objectApiName, IObjectData objectData) {
        Set<String> fieldApiNamesToRemove = removeFieldMap.getOrDefault(objectApiName, Sets.newHashSet());
        return ObjectDataExt.of(ObjectDataExt.of(objectData).copy()).remove(fieldApiNamesToRemove).getObjectData();
    }

    /**
     * 处理需要在更新操作前从对象数据中移除的特殊字段。
     * 这些字段包括：
     * 1. 系统字段（如锁定状态、版本号等）
     * 2. GDPR相关字段（针对OpenAPI请求）
     * 3. 业务自定义需要移除的字段
     * 4. 统计字段
     * 5. 关联字段
     */
    protected final void processRemoveFields() {
        // 1. 初始化基础需要移除的系统字段
        Set<String> baseFieldsToRemove = Sets.newHashSet(
                IObjectData.VERSION,
                ObjectLockStatus.LOCK_STATUS_API_NAME,
                ObjectLifeStatus.LIFE_STATUS_API_NAME,
                ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME,
                IObjectData.OWNER,
                IObjectData.DATA_OWN_DEPARTMENT,
                ObjectDataExt.RELEVANT_TEAM,
                IObjectData.OUT_OWNER,
                IObjectData.OUT_TENANT_ID,
                IObjectData.CREATE_ENTERPRISE
        );

        // 2. 处理 OpenAPI 请求的 GDPR 字段
        if (RequestUtil.isOpenAPIRequest()) {
            filterGdprFields(baseFieldsToRemove);
        }

        // 3. 为每个对象创建独立的移除字段集合
        objectDescribes.keySet().forEach(apiName -> {
            // 创建新的集合，避免共享引用
            Set<String> fieldsToRemove = new HashSet<>(baseFieldsToRemove);

            // 添加业务自定义字段
            Set<String> specialFields = getFieldsToRemoveBeforeUpdate(apiName);
            if (CollectionUtils.notEmpty(specialFields)) {
                fieldsToRemove.addAll(specialFields);
            }
            removeFieldMap.put(apiName, fieldsToRemove);
        });

        // 4. 处理统计字段
        objectDescribes.keySet().forEach(apiName -> {
            Set<String> currentFields = removeFieldMap.get(apiName);

            // 处理统计字段
            Set<String> countFields = new HashSet<>();
            if (StringUtils.equals(apiName, objectDescribe.getApiName()) && CollectionUtils.notEmpty(arg.getDetails())) {
                countFields.addAll(ObjectDescribeExt.of(objectDescribe).getCountFields().stream()
                        .filter(x -> !arg.getDetails().containsKey(x.getSubObjectDescribeApiName()))
                        .map(x -> x.getApiName())
                        .collect(Collectors.toSet()));
            } else {
                countFields.addAll(ObjectDescribeExt.of(objectDescribes.get(apiName)).getCountFields().stream()
                        .map(x -> x.getApiName())
                        .collect(Collectors.toSet()));
            }
            currentFields.addAll(countFields);

            // 5. 处理关联字段
            if (graph != null) {
                countFields.forEach(x -> graph.getNode(apiName, x).ifPresent(countNode ->
                        graph.reachableNodes(countNode).stream()
                                .filter(node -> objectDescribes.containsKey(node.getObjectApiName()))
                                .forEach(node -> removeFieldMap.get(node.getObjectApiName()).add(node.getFieldApiName()))));
            }
        });
    }

    private void filterGdprFields(Set<String> fieldApiNamesToRemove) {
        List<String> needFilterGdprFields = infraServiceFacade.needFilterGdprFields(actionContext.getUser(), objectDescribe.getApiName(), RequestContext.OPENAPI_PEER_NAME);
        if (CollectionUtils.notEmpty(needFilterGdprFields)) {
            fieldApiNamesToRemove.addAll(needFilterGdprFields);
        }
    }

    /**
     * @param describeApiName This string may be use for further computation in overriding classes
     * @return
     */
    protected Set<String> getFieldsToRemoveBeforeUpdate(String describeApiName) {
        return Sets.newHashSet();
    }

    private void calculateCountAndFormulaFields() {
        List<IObjectDescribe> detailDescribes = objectDescribes.values().stream()
                .filter(x -> detailObjectData.containsKey(x.getApiName()))
                .collect(Collectors.toList());
        //计算主对象和从对象的计算字段和主对象统计从对象的统计字段
        CalculateFields calculateFields = infraServiceFacade.computeCalculateFieldsForAddAction(objectDescribe, detailDescribes, true);
        serviceFacade.batchCalculateBySortFields(actionContext.getUser(), objectData, detailObjectData, calculateFields);
        graph = calculateFields.getGraph();
    }

    private void calculateQuoteFields() {
        StopWatch sw = StopWatch.create("calculateQuoteFields");
        infraServiceFacade.fillQuoteFieldValue(actionContext.getUser(), Lists.newArrayList(objectData), objectDescribe, false);
        sw.lap("calculateMasterQuote");
        detailObjectData.forEach((detailApiName, detailList) -> {
            IObjectDescribe detailDescribe = objectDescribes.get(detailApiName);
            List<Quote> quoteList = ObjectDescribeExt.of(detailDescribe).getQuoteFieldDescribes();
            infraServiceFacade.fillQuoteFieldValue(actionContext.getUser(), detailList, detailDescribe, null,
                    false, quoteList, objectData);
            sw.lap("calculateDetailQuote-" + detailApiName);
        });
        sw.logSlow(100);
    }

    protected void batchUpdateObjectData(List<IObjectData> dataToUpdate) {
        if (CollectionUtils.empty(dataToUpdate)) {
            return;
        }
        //由于系统自动异步更新比较多，这里将version等系统字段去掉，防止大概率的版本冲突，而且人工并行更新同一条数据的概率相对来说小很多，所以此处影响较小。
        //构建新的ObjectData去更新，防止后续有人使用其中的version等字段但是取不到值。
        groupDetailsByDescribeApiName(dataToUpdate).forEach((k, v) -> {
            List<IObjectData> updateList = v.stream().map(x -> copyAndRemoveSpecialFields(k, x)).collect(Collectors.toList());

            //主从一起编辑的时候传标记给元数据，不对数据做diff，直接更新
            if (CollectionUtils.notEmpty(arg.getDetails())) {
                serviceFacade.batchUpdateByMasterDetail(updateList, actionContext.getUser());
            } else {
                serviceFacade.batchUpdate(updateList, actionContext.getUser());
            }
        });
        updatedDataList.addAll(dataToUpdate);
    }

    private void detailAddAuditLog(String objectApiName, List<IObjectData> dataList) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribes.get(objectApiName));
        serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Add, describeMap, dataList, masterLogId, ConvertRuleLogicService.toConvertSourceContainer(convertRuleDataContainer));
    }

    private void detailDeleteAuditLog(String objectApiName, List<IObjectData> dataList) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, objectDescribes.get(objectApiName));
        serviceFacade.log(actionContext.getUser(), EventType.DELETE, ActionType.Delete, describeMap, dataList, masterLogId);
    }

    private void detailModifyLog(String apiName, List<IObjectData> dataList) {
        serviceFacade.detailModifyLog(actionContext.getUser(), objectDescribes.get(apiName), dataList, getDetailUpdateFieldMap(apiName),
                dbDetailDataMap.get(apiName), masterLogId);
    }

    private Map<String, Map<String, Object>> getDetailUpdateFieldMap(String apiName) {
        if (CollectionUtils.empty(detailChangeMap.get(apiName))) {
            return Maps.newHashMap();
        }
        return (Map<String, Map<String, Object>>) detailChangeMap.get(apiName).get(ObjectAction.UPDATE.getActionCode());
    }

    protected List<IObjectData> getAllDataToUpdate() {
        List<IObjectData> dataToUpdate = Lists.newArrayList();
        dataToUpdate.addAll(detailsToUpdate);
        dataToUpdate.add(objectData);
        return dataToUpdate;
    }

    protected void recordEditLog() {
        //使用拷贝的数据记日志，防止ConcurrentModificationException
        IObjectData cpObjectData = ObjectDataExt.of(objectData).copy();
        IObjectData cpDbMasterData = ObjectDataExt.of(dbMasterData).copy();
        List<IObjectData> cpDetailsToUpdate = ObjectDataExt.copyList(detailsToUpdate);
        List<IObjectData> cpDetailsToDelete = ObjectDataExt.copyList(detailsToDelete);
        List<IObjectData> cpDetailsToAdd = ObjectDataExt.copyList(detailsToAdd);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            masterModifyLog(cpObjectData, cpDbMasterData);
            groupDetailsByDescribeApiName(cpDetailsToUpdate).forEach(this::detailModifyLog);
            groupDetailsByDescribeApiName(cpDetailsToDelete).forEach(this::detailDeleteAuditLog);
            groupDetailsByDescribeApiName(cpDetailsToAdd).forEach(this::detailAddAuditLog);
        });
        parallelTask.run();
    }

    private void masterModifyLog(IObjectData cpObjectData, IObjectData cpDbMasterData) {
        Map detailChange = detailChangeMap;
        LogService.MasterLogInfo masterLog = serviceFacade.fillMasterModifyLog(actionContext.getUser(), objectDescribes,
                cpObjectData, getUpdatedFieldMapForLog(), cpDbMasterData, detailChange, getLogExtendsInfo(),
                ConvertRuleLogicService.toConvertSourceContainer(convertRuleDataContainer));
        masterLogId = masterLog.getMasterLogId();
        customizeLog(masterLog.getLogList());
        serviceFacade.sendLog(masterLog.getLogList());
    }

    protected void customizeLog(List<LogInfo> logInfoList) {
        // 提供给业务方的拓展点
    }

    protected void recordLog() {
        // 提供给业务方的拓展点
    }

    private void sendActionMq() {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        List<IObjectData> updateDataList = fillOldData(ObjectDataExt.copyList(updatedDataList));
        List<IObjectData> deleteDataList = ObjectDataExt.copyList(detailsToDelete);
        List<IObjectData> addDataList = ObjectDataExt.copyList(detailsToAdd);
        parallelTask.submit(() -> {
            sendActionMq(updateDataList, ObjectAction.UPDATE);
            sendActionMq(deleteDataList, ObjectAction.DELETE);
            sendActionMq(addDataList, ObjectAction.CREATE);
        });
        parallelTask.run();
    }

    private Map<String, Map<String, Map<String, Object>>> getUpdatedFieldMapForLog() {
        Map<String, Map<String, Map<String, Object>>> allUpdatedFieldMap = Maps.newHashMap();
        // 获取主对象的更新字段
        Map<String, Map<String, Object>> masterUpdateMap = Maps.newHashMap();
        masterUpdateMap.put(objectData.getId(), updatedFieldMap);
        allUpdatedFieldMap.put(objectData.getDescribeApiName(), masterUpdateMap);
        return allUpdatedFieldMap;
    }

    protected void startCreateWorkFlow() {
        if (!needTriggerWorkFlow()) {
            return;
        }

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            List<IObjectData> dataListForCreateWorkFlow = getDataListForCreateWorkFlow();
            doStartCreateWorkFlow(dataListForCreateWorkFlow);
        });
        parallelTask.run();
    }

    protected List<IObjectData> getDataListForCreateWorkFlow() {
        List<IObjectData> dataListForCreateWorkFlow = Lists.newArrayList();
        //主从白名单
        //主对象是normal状态
        //编辑前主对象已经是in_change状态
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(actionContext.getTenantId())
                || ObjectDataExt.of(objectData).isNormal()
                || ObjectDataExt.of(dbMasterData).isInChange()) {
            dataListForCreateWorkFlow.addAll(detailsToAdd);
        }
        //状态从ineffective变成normal，本对象和非主从白名单的从需要触发工作流
        if (ObjectDataExt.of(dbMasterData).isIneffective() && ObjectDataExt.of(objectData).isNormal()) {
            if (!AppFrameworkConfig.isInMasterDetailApprovalWhiteList(actionContext.getTenantId())) {
                dataListForCreateWorkFlow.addAll(detailsToUpdate);
            }
            dataListForCreateWorkFlow.add(objectData);
        }
        //从对象，主是未生效状态且没有触发审批，主和从都需要触发工作流
        List<IObjectData> relateDataForCreateWorkFlow = super.getRelateDataForCreateWorkFlow();
        if (dataListForCreateWorkFlow.stream().anyMatch(x -> x.getDescribeApiName().equals(objectData.getDescribeApiName())
                && x.getId().equals(objectData.getId()))) {
            relateDataForCreateWorkFlow.removeIf(x -> x.getDescribeApiName().equals(objectData.getDescribeApiName())
                    && x.getId().equals(objectData.getId()));
        }
        dataListForCreateWorkFlow.addAll(relateDataForCreateWorkFlow);
        return dataListForCreateWorkFlow;
    }

    //在主从一同编辑时,对于新建的从对象的处理
    private void modifyDetailObjectDataToAddWhenEdit(IObjectData masterObjectData,
                                                     String detailObjectDescribeApiName,
                                                     List<IObjectData> objectDataDetailToAdd) {
        if (CollectionUtils.empty(objectDataDetailToAdd)) {
            return;
        }

        ObjectDataExt masterDataExt = ObjectDataExt.of(masterObjectData);
        Optional<String> masterOwnerId = masterDataExt.getOwnerId();
        String outOwnerId = masterDataExt.getOutOwnerId().orElse(null);
        String outTenantId = masterDataExt.getOutTenantId();

        //循环处理所有的从对象,插入系统信息和负责人
        IObjectDescribe detailDescribe = this.objectDescribes.get(detailObjectDescribeApiName);
        objectDataDetailToAdd.forEach(detail -> {
            ObjectDataExt detailDataExt = ObjectDataExt.of(detail);
            //设置系统字段
            modifySystemAndPackageFieldsOfObjectDataBeforeCreate(detail, detailDescribe);
            //设置负责人
            masterOwnerId.ifPresent(detailDataExt::setOwnerId);
            //设置数据所属部门
            detail.setDataOwnDepartment(masterObjectData.getDataOwnDepartment());
            detail.setDataOwnOrganization(masterObjectData.getDataOwnOrganization());
            //灰度企业从对象生命状态默认和主对象保持一致，否则默认为normal
            if (AppFrameworkConfig.isInMasterDetailApprovalGrayList(actionContext.getTenantId())) {
                detailDataExt.setLifeStatus(masterDataExt.getLifeStatus());
            } else {
                detailDataExt.setLifeStatus(ObjectLifeStatus.NORMAL);
            }
            // 补充外部负责人信息
            detailDataExt.setOutTenantAndOutOwner(outTenantId, outOwnerId);
            // 编辑时新增的从对象补充主对象的合作伙伴
            syncDetailDataPartner(masterDataExt.getPartnerId(), detailDescribe, detail);
        });
    }

    @Override
    protected String getButtonApiName() {
        if (AppFrameworkConfig.isAddEditUIActionGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return ObjectAction.UPDATE_SAVE.getButtonApiName();
        }
        return ButtonExt.DEFAULT_EDIT_BUTTON_API_NAME;
    }

    @Override
    protected final void replaceDetailDataMergeResult(MergeStateContainer sourceMergeStateContainer) {
        detailsToAdd = dataToAdd(sourceMergeStateContainer);
        detailsToUpdate = dataToUpdate(sourceMergeStateContainer);
        detailsToDelete = sourceMergeStateContainer.detailsToDelete();
        if (log.isInfoEnabled()) {
            log.info("detailDataMergeResult detailsToAdd:{}, detailsToUpdate:{}, detailsToDelete:{}",
                    JacksonUtils.toJson(ObjectDataDocument.ofList(detailsToAdd)),
                    JacksonUtils.toJson(ObjectDataDocument.ofList(detailsToUpdate)),
                    JacksonUtils.toJson(ObjectDataDocument.ofList(detailsToDelete)));
        }
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        detailsToAdd.forEach(data -> details.computeIfAbsent(data.getDescribeApiName(), k -> Lists.newArrayList()).add(data));
        detailsToUpdate.forEach(data -> details.computeIfAbsent(data.getDescribeApiName(), k -> Lists.newArrayList()).add(data));
        // 按已有的从对象 order_by 排一下序
        details.forEach((apiName, objectDataList) -> ObjectDataExt.sortByOrderBy(objectDataList));
        // 用合并后的数据替换 detailObjectData
        detailObjectData = details;
    }

    private List<IObjectData> dataToUpdate(MergeStateContainer sourceMergeStateContainer) {
        List<IObjectData> dataToUpdate = sourceMergeStateContainer.detailsToUpdate();
        if (CollectionUtils.empty(dataToUpdate)) {
            return Lists.newArrayList();
        }
        for (IObjectData data : dataToUpdate) {
            ObjectDataExt.of(data).remove(ObjectDataExt.RELEVANT_TEAM);
        }
        return dataToUpdate;
    }

    private List<IObjectData> dataToAdd(MergeStateContainer sourceMergeStateContainer) {
        List<IObjectData> dataToAdd = sourceMergeStateContainer.detailsToAdd();
        if (CollectionUtils.empty(dataToAdd)) {
            return Lists.newArrayList();
        }
        Map<String, List<IObjectData>> detailDataMap = dataToAdd.stream()
                .collect(Collectors.groupingBy(IObjectData::getDescribeApiName, Collectors.mapping(Function.identity(), Collectors.toList())));
        List<IObjectData> result = Lists.newArrayList();
        detailDataMap.forEach((detailApiName, dataList) -> {
            modifyDetailObjectDataToAddWhenEdit(objectData, detailApiName, dataList);
            addMasterDetailFieldIntoDetailDataList(objectData.getId(), objectDescribes.get(detailApiName), dataList);
            result.addAll(dataList);
        });
        return result;
    }

    @Override
    protected final MergeStateContainer getSourceMergeStateContainer() {
        List<IObjectData> detailsToDeleteCopy = ObjectDataExt.copyList(this.detailsToDelete);
        ObjectDataExt.fillTemporaryId(detailsToDeleteCopy);
        return MergeStateContainer.of(detailsToAdd, detailsToUpdate, detailsToDeleteCopy);
    }

}
