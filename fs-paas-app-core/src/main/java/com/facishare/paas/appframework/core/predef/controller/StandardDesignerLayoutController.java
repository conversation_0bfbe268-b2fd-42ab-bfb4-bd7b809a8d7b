package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.component.ISuspendedComponentInfo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ICustomComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isGrayUserControlBpmAndApprovalListObj;
import static com.facishare.paas.appframework.metadata.layout.ComponentHeaderSetter.SOURCE_TYPE_DESIGNER;

public class StandardDesignerLayoutController extends PreDefineController<StandardDesignerLayoutController.Arg, StandardDesignerLayoutController.Result> {

    private SpecialButtonManager specialButtonManager;
    private LayoutDesignerButtonManager layoutDesignerButtonManager;

    protected IObjectDescribe describe;

    private boolean supportMultiLanguage;

    /**
     * 加工布局，子类可重写该方法
     *
     * @param layout
     */
    protected void processLayout(ILayout layout) {
        // 处理layout脏数据
        // 涉及 新建/编辑/详情页
        if (Objects.isNull(layout)) {
            return;
        }
        LayoutExt.of(layout).fixUIEvent();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        LayoutContext.get().setNoReplaceLayoutNameI18n(arg.isNoNeedReplaceI18n());
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        specialButtonManager = serviceFacade.getBean(SpecialButtonManager.class);
        layoutDesignerButtonManager = serviceFacade.getBean(LayoutDesignerButtonManager.class);
        LayoutContext.get().setLayoutVersion(LayoutVersion.V3);
    }

    @Override
    protected Result doService(Arg arg) {
        describe = findDescribe();

        supportMultiLanguage = serviceFacade.isSupportMultiLanguage(controllerContext.getTenantId());

        ILayout layout = null;
        if (arg.isIncludeLayout()) {
            layout = serviceFacade.getLayoutLogicService().findLayoutByApiName(controllerContext.getUser(), arg.getLayoutApiName(), describe.getApiName());
        }

        if (layout != null) {
            //后台设计器获取实时翻译
            LayoutExt.of(layout).layoutDescriptionNullToEmpty();
            List<String> keys = LayoutExt.of(layout).getLayoutTransKeys();
            Map<String, Localization> localization = infraServiceFacade.getLocalization(keys, controllerContext.getTenantId(), false, true);
            if (LayoutExt.of(layout).isFlowLayout()) {
                processFlowLayout(layout, localization);
            } else if (LayoutExt.of(layout).isDetailLayout()) {
                processDetailPageLayout(layout, localization);
            } else if (LayoutExt.of(layout).isEditLayout()) {
                processEditPageLayout(layout, localization);
            }
            //加工布局
            processLayout(layout);
        }
        ObjectDescribeDocument describeExtra = null;
        if (arg.isIncludeDescribeExtra()) {
            DescribeExtra extra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), describe, Collections.emptyList(),
                    DescribeExpansionRender.RenderType.Designer, true);
            describeExtra = ObjectDescribeDocument.of(extra);
        }
        return Result.builder()
                .objectDescribe(arg.isIncludeDescribe() ? ObjectDescribeDocument.of(describe) : null)
                .layout(LayoutDocument.of(layout))
                .describeExtra(describeExtra)
                .fieldsExtra(arg.includeFieldsExtra ? infraServiceFacade.findFieldsExtra(controllerContext.getUser(), describe.getApiName()) : null)
                .build();
    }


    private IObjectDescribe findDescribe() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getDescribeApiName());
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        describeExt.fillOtherOptionInSelectOneFields();
        describeExt.descriptionNullToEmpty();
        describeExt.fillDefaultShowDetailButton();
        describeExt.fillEnabledCloneDefaultValueToField();
        return objectDescribe;
    }

    private void processFlowLayout(ILayout layout, Map<String, Localization> localizationMap) {
        List<String> oldDetailComponentNames = LayoutExt.of(layout).getMasterDetailComponentNames();
        List<IObjectDescribe> detailDescribes = null;
        if (CollectionUtils.notEmpty(oldDetailComponentNames)) {
            //查询从对象
            detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(), describe.getApiName());
        }

        EditLayoutRender.builder()
                .functionLogicService(serviceFacade.getFunctionLogicService())
                .layoutLogicService(serviceFacade.getLayoutLogicService())
                .licenseService(serviceFacade)
                .describeLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .pageType(PageType.Designer)
                .layout(layout)
                .describe(describe)
                .detailDescribes(detailDescribes)
                .existMultiLanguage(supportMultiLanguage)
                .localizationMap(localizationMap)
                .appId(getAppId())
                .build()
                .render();
    }

    private void processDetailPageLayout(ILayout layout, Map<String, Localization> localizationMap) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        LayoutExt layoutExt = LayoutExt.of(layout);

        //支持按钮排序
        handleLayoutButtons(describeExt, layoutExt);

        List<IComponent> components = layoutExt.getComponentsSilently().stream()
                .filter(x -> !ComponentExt.of(x).isCustomComponent()
                        && !ComponentExt.of(x).isMasterDetailComponent())
                .filter(x -> !ComponentExt.of(x).isRelatedList()
                        || (isGrayUserControlBpmAndApprovalListObj(describe.getApiName()) && Lists.newArrayList("Approval_related_list", "BPM_related_list").contains(ComponentExt.of(x).getName()))
                        || ComponentExt.of(x).isPaymentComponent())
                .collect(Collectors.toList());

        //查询关联对象和从对象
        List<IObjectDescribe> relatedDescribes = serviceFacade.findRelatedDescribes(controllerContext.getTenantId(), describe.getApiName());
        List<RelatedObjectDescribeStructure> detailObjects = describeExt.getDetailObjectDescribeStructuresOrderByFieldCreateTime(relatedDescribes);
        List<RelatedObjectDescribeStructure> lookupObjects = describeExt.getRelatedObjectDescribeStructuresOrderByFieldCreateTime(relatedDescribes);
        //根据配置中心的配置去掉不展示的关联对象
        lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(describe.getApiName(), x.getRelatedObjectDescribe().getApiName()));
        serviceFacade.getLayoutLogicService().filterRelatedObjectStructure(controllerContext.getUser(), layout, lookupObjects);

        //从对象
        List<IComponent> detailComponentList = MasterDetailGroupComponentBuilder.builder()
                .layoutExt(layoutExt)
                .detailObjectsDescribeList(detailObjects).build()
                .getComponentListForNewDesigner();
        detailComponentList = layoutExt.filterComponents(detailComponentList);
        components.addAll(detailComponentList);

        //关联对象
        List<IComponent> relateComponentList = RelatedObjectGroupComponentBuilder.builder()
                .layout(layoutExt)
                .user(controllerContext.getUser())
                .objectDescribe(describeExt)
                .relatedObjectDescribeList(lookupObjects)
                .build()
                .getComponentListForDesigner();
        relateComponentList = layoutExt.filterComponents(relateComponentList);
        //使用新版附件替换旧版附件
        LayoutComponents.replaceOldAttachComponent(layoutExt, relateComponentList);
        components.addAll(relateComponentList);

        //自定义组件
        List<ICustomComponent> newCustomComponents = serviceFacade.getLayoutLogicService().findCustomComponents(controllerContext.getTenantId(), layoutExt);
        List<IComponent> customComponents = layoutExt.filterCustomComponents(newCustomComponents);
        components.addAll(customComponents);

        List<Map> leftTabsApiNames = null;
        //兼容只有详细信息的古老布局
        if (!layoutExt.isNewLayout()) {
            //跟进动态
            IComponent saleLogComponent = RelatedObjectGroupComponentBuilder.builder().build()
                    .buildRecordComponentWithoutFieldsAndButtons(arg.getDescribeApiName());
            //指定对象的v1布局不展示跟进动态组件
            if (!layoutExt.containsComponent(saleLogComponent.getName())
                    && !AppFrameworkConfig.isHideSaleLogInV1Layout(arg.getDescribeApiName())) {
                components.add(saleLogComponent);
            }

            //相关团队
            IComponent teamComponent = TeamComponentBuilder.builder().build().buildUserListComponentWithoutFieldsAndButtons();
            if (!layoutExt.containsComponent(teamComponent.getName())) {
                components.add(teamComponent);
            }

            //修改记录
            IComponent modifyLogComponent = LayoutExt.buildModifyRecordComponent();
            if (!layoutExt.containsComponent(modifyLogComponent.getName())) {
                relateComponentList.add(modifyLogComponent);
                components.add(modifyLogComponent);
            }

            //将相关对象适配成layout_structure中的结构
            leftTabsApiNames = LayoutStructure.getOrderComponentsStructure(layoutExt, detailComponentList,
                    relateComponentList);
        }

        //根据配置过滤组件
        components.removeIf(x -> DefObjConstants.isComponentInvisible(arg.getDescribeApiName(), x.getName()));

        layoutExt.removeHiddenComponents(components);
        //将销售记录修改为跟进动态
        components.stream().filter(x -> ComponentExt.of(x).isSaleLog()).forEach(x -> x.setHeader(I18N.text(I18NKey.FOLLOW_UP_DYNAMIC)));
        resetHeadInfoComponent(components);

        //根据i18nKey重置component的header
        processComponentHeader(components, localizationMap);

        LayoutStructure.buildLayoutStructure(layoutExt, describeExt, components, leftTabsApiNames,
                Lists.newArrayList(), false, false);
        if (CollectionUtils.notEmpty(layoutExt.getUiEventIds())) {
            LayoutLogicService.LayoutContext context = LayoutLogicService.LayoutContext.of(controllerContext.getUser(), getAppId());
            byte enableEditStatus = serviceFacade.getLayoutLogicService().getEditLayoutStatus(context, layout.getRefObjectApiName());
            if (EditLayout.EditLayoutStatus.ENABLE.getCode() == enableEditStatus) {
                // 开启新建编辑页布局后详情页布局ui事件相关配置无需返回。
                // 否则会造成复制布局时会重新新建无用的ui事件及引用关系，造成用户无法删除此ui事件函数引用
                layoutExt.setUiEventIds(Lists.newArrayList());
                layoutExt.setEvents(Lists.newArrayList());
            } else {
                List<Map<String, Object>> events = handleUIEventFunction(layout);
                layoutExt.setEvents(events);
            }
        }
        // web补充三流组件
        WebDetailLayout.of(layoutExt).processWebFlowComponents(controllerContext.getTenantId(), describeExt);
        serviceFacade.getLayoutLogicService().handleSummaryKeyComponents(controllerContext.getTenantId(), describeExt, layoutExt);
        //处理移动端布局
        processMobileLayout(layoutExt, detailObjects, lookupObjects, localizationMap, describeExt);
        //处理侧边栏布局
        processSidebarLayout(layoutExt, detailObjects, lookupObjects, localizationMap, describeExt);
    }

    private void processSidebarLayout(LayoutExt layoutExt, List<RelatedObjectDescribeStructure> detailObjects,
                                      List<RelatedObjectDescribeStructure> lookupObjects, Map<String, Localization> localizationMap,
                                      ObjectDescribeExt describeExt) {
        if (!layoutExt.isEnableSidebarLayout()) {
            return;
        }
        LayoutExt sidebarLayout = LayoutExt.of(layoutExt.getSidebarLayout());
        dealExtraLayout(layoutExt, detailObjects, lookupObjects, localizationMap, describeExt, sidebarLayout);
        layoutExt.setSidebarLayout(sidebarLayout.toMap());

    }

    private void dealExtraLayout(LayoutExt layoutExt, List<RelatedObjectDescribeStructure> detailObjects, List<RelatedObjectDescribeStructure> lookupObjects,
                                 Map<String, Localization> localizationMap, ObjectDescribeExt describeExt, LayoutExt extraLayout) {
        List<IComponent> components = extraLayout.getComponentsSilently().stream()
                .filter(x -> !ComponentExt.of(x).isMasterDetailComponent() && !ComponentExt.of(x).isRelatedList())
                .collect(Collectors.toList());
        //从对象
        List<IComponent> detailComponentList = MasterDetailGroupComponentBuilder.builder()
                .layoutExt(extraLayout).detailObjectsDescribeList(detailObjects).build().getComponentListForNewDesigner();
        detailComponentList = extraLayout.filterComponents(detailComponentList);
        //Tab显示主对象字段处理
        layoutExt.buildTabShowMasterField(detailComponentList, describeExt);
        components.addAll(detailComponentList);
        //关联对象
        List<IComponent> relateComponentList = RelatedObjectGroupComponentBuilder.builder()
                .layout(extraLayout).objectDescribe(ObjectDescribeExt.of(describe))
                .user(controllerContext.getUser())
                .relatedObjectDescribeList(lookupObjects).build().getComponentListForDesigner();
        relateComponentList = extraLayout.filterComponents(relateComponentList);
        //使用新版附件替换旧版附件
        LayoutComponents.replaceOldAttachComponent(describe.getApiName(), extraLayout, relateComponentList);
        components.addAll(relateComponentList);
        LayoutExt.fillNameComponent(extraLayout, layoutExt, components);
        extraLayout.removeHiddenComponents(components);
        //根据i18nKey重置component的header
        processComponentHeader(components, extraLayout.getSuspendedComponent(), localizationMap);

        //将新增的从对象和相关对象放入layout_structure或页签导航中
        LayoutStructure.modifyLayoutStructure(extraLayout, components, true);
        //根据配置过滤组件
        components.removeIf(x -> DefObjConstants.isComponentInvisible(arg.getDescribeApiName(), x.getName()));
        //将不在components中的组件从layout_structure里清理掉
        LayoutStructure.clearLayoutStructureByComponents(extraLayout, components);
        extraLayout.setComponents(components);

        //处理三流组件
        AbstractLayoutProcessor.processFlowComponents(controllerContext.getTenantId(), extraLayout, describeExt);

        //加工按钮
        handleLayoutButtons(ObjectDescribeExt.of(describe), extraLayout);
    }

    private void resetHeadInfoComponent(List<IComponent> components) {
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), describe.getApiName());
        LayoutExt.resetHeadInfoComponent(components, unauthorizedFields, describe, controllerContext.getUser());
    }

    private String getAppId() {
        return arg.getAppId();
    }

    private void processComponentHeader(List<IComponent> components, Map<String, Localization> localizationMap) {
        processComponentHeader(components, Lists.newArrayList(), localizationMap);
    }

    private void processComponentHeader(List<IComponent> components, List<ISuspendedComponentInfo> suspendedComponents, Map<String, Localization> localizationMap) {
        ComponentHeaderSetter.builder()
                .tenantId(controllerContext.getTenantId())
                .layoutType(LayoutTypes.DETAIL)
                .layoutVersion(LayoutVersion.V3)
                .components(components)
                .suspendedComponents(suspendedComponents)
                .objectApiName(describe.getApiName())
                .layoutApiName(arg.getLayoutApiName())
                .existMultiLanguage(supportMultiLanguage)
                .sourceType(SOURCE_TYPE_DESIGNER)
                .localizationMap(localizationMap)
                .componentPreKeyMap(serviceFacade.getLayoutLogicService().findComponentPreKeys(components))
                .build()
                .reset();
    }

    private void processMobileLayout(LayoutExt layoutExt,
                                     List<RelatedObjectDescribeStructure> detailObjects,
                                     List<RelatedObjectDescribeStructure> lookupObjects,
                                     Map<String, Localization> localizationMap,
                                     ObjectDescribeExt describeExt) {
        if (!layoutExt.isEnableMobileLayout()) {
            return;
        }
        LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
        dealExtraLayout(layoutExt, detailObjects, lookupObjects, localizationMap, describeExt, mobileLayout);
        layoutExt.setMobileLayout(mobileLayout.toMap());
    }

    private void processEditPageLayout(ILayout layout, Map<String, Localization> localizationMap) {
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(),
                describe.getApiName());
        Map<ButtonUsePageType, List<IButton>> customButtonMap = serviceFacade.findButtonsByUsePages(controllerContext.getUser(),
                describe, Lists.newArrayList(ButtonUsePageType.Create, ButtonUsePageType.Edit));

        //处理web端布局
        EditLayoutRender.builder()
                .functionLogicService(serviceFacade.getFunctionLogicService())
                .licenseService(serviceFacade)
                .layoutLogicService(serviceFacade.getLayoutLogicService())
                .describeLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .pageType(PageType.Designer)
                .layout(layout)
                .describe(describe)
                .detailDescribes(detailDescribes)
                .customButtonMap(customButtonMap)
                .existMultiLanguage(supportMultiLanguage)
                .localizationMap(localizationMap)
                .appId(getAppId())
                .build()
                .render();

        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!layoutExt.isEnableMobileLayout()) {
            return;
        }

        //处理移动端布局
        LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
        mobileLayout.setName(layoutExt.getName());
        EditLayoutRender.builder()
                .licenseService(serviceFacade)
                .layoutLogicService(serviceFacade.getLayoutLogicService())
                .describeLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .pageType(PageType.Designer)
                .layoutAgentType(LayoutAgentType.MOBILE)
                .layout(mobileLayout)
                .describe(describe)
                .detailDescribes(detailDescribes)
                .customButtonMap(customButtonMap)
                .existMultiLanguage(supportMultiLanguage)
                .localizationMap(localizationMap)
                .appId(getAppId())
                .build()
                .render();
        LayoutStructure.syncTitleNameWithMobile(layoutExt, mobileLayout);
        layoutExt.setMobileLayout(mobileLayout.toMap());
    }

    private void handleLayoutButtons(ObjectDescribeExt describeExt, LayoutExt layoutExt) {
        if (!AppFrameworkConfig.designerLayoutButton(describeExt.getTenantId(), describeExt.getApiName())) {
            _handleLayoutButtons(describeExt, layoutExt);
            return;
        }
        if (CollectionUtils.empty(layoutExt.getButtonOrder())) {
            //如果按钮为空，说明没有排序，响应默认排序
            //自定义按钮
            List<IButton> customButtons = findDetailButtonsAndFilter(describeExt, controllerContext.getUser());
            ButtonOrder.fillLayoutWithButtonsByDefaultOrder(layoutExt, customButtons, describeExt);
            // 处理预置对象,需要开通服务才展示的按钮
            List<IButton> buttons = processDetailPageButtons(layoutExt.getButtonOrder(), controllerContext.getUser());
            //过滤隐藏的按钮
            if (CollectionUtils.notEmpty(layoutExt.getHiddenButtons())) {
                buttons.removeIf(x -> layoutExt.getHiddenButtons().contains(x.getName()));
            }
            layoutExt.setButtonOrder(buttons);
        } else {
            //保存于布局中的按钮，按照ObjectAction的名称显示
            ButtonOrder.synchronizeName(layoutExt, describeExt);
            //layout中存在按钮，说明已排序，将新增按钮加入layout
            fillLayoutWithNewButtons(layoutExt, describeExt);
        }
        //过滤禁用的按钮
        layoutExt.removeUnActiveButton();
        filterButtonsByDescribe(describeExt, layoutExt);
    }

    private void filterButtonsByDescribe(ObjectDescribeExt describeExt, LayoutExt layoutExt) {
        List<IButton> buttonOrder = layoutExt.getButtonOrder();
        //过滤button
        List<IButton> buttons = ButtonExt.filterLayoutButtons(describeExt, buttonOrder);
        layoutExt.setButtonOrder(buttons);
    }

    private void _handleLayoutButtons(ObjectDescribeExt describeExt, LayoutExt layoutExt) {
        if (CollectionUtils.empty(layoutExt.getButtonOrder())) {
            //如果按钮为空，说明没有排序，响应默认排序
            //自定义按钮
            List<IButton> customButtons = findDetailButtonsAndFilter(describeExt, controllerContext.getUser());
            ButtonOrder.fillLayoutWithButtonsByDefaultOrder(layoutExt, customButtons, describeExt);
        } else {
            //保存于布局中的按钮，按照ObjectAction的名称显示
            ButtonOrder.synchronizeName(layoutExt, describeExt);
            //layout中存在按钮，说明已排序，将新增按钮加入layout
            _fillLayoutWithNewButtons(layoutExt, describeExt);
        }
        //过滤禁用的按钮
        layoutExt.removeUnActiveButton();
        // 处理预置对象,需要开通服务才展示的按钮
        List<IButton> buttons = processDetailPageButtons(layoutExt.getButtonOrder(), controllerContext.getUser());

        //过滤隐藏的按钮
        if (CollectionUtils.notEmpty(layoutExt.getHiddenButtons())) {
            buttons.removeIf(x -> layoutExt.getHiddenButtons().contains(x.getName()));
        }

        //排序
        List<IButton> buttonOrder = layoutExt.getButtonOrder();
        buttons = ButtonOrder.orderingByTemplate(buttons, buttonOrder);

        layoutExt.setButtonOrder(buttons);
        filterButtonsByDescribe(describeExt, layoutExt);
    }

    /*
     * 新增自定义按钮存入布局，默认排最后
     */
    private void fillLayoutWithNewButtons(LayoutExt layoutExt, ObjectDescribeExt describeExt) {
        //查找自定义按钮
        List<IButton> customButtons = findDetailButtonsAndFilter(describeExt, controllerContext.getUser());
        //更新布局中自定义按钮激活状态和名称
        ButtonOrder.updateCustomButtonWithLayout(customButtons, layoutExt);
        //无重复并集
        ButtonOrder.backhanderNewCustomButtons(layoutExt, customButtons);
        //追加新增预定义按钮
        ButtonOrder.backhanderNewPredefinedButtons(layoutExt, describeExt.getApiName(), () -> {
            LayoutButtons layoutButtons = LayoutButtons.getInstance(describeExt);
            List<IButton> buttons = layoutButtons.getActionButtons();
            List<IButton> tailButtons = layoutButtons.getTailButtons();
            buttons.addAll(tailButtons);
            // 处理预置对象,需要开通服务才展示的按钮
            return processDetailPageButtons(buttons, controllerContext.getUser());
        });
        // 过滤被被隐藏的按钮
        filterHiddenButtons(layoutExt);
    }

    private void _fillLayoutWithNewButtons(LayoutExt layoutExt, ObjectDescribeExt describeExt) {
        //查找自定义按钮
        List<IButton> customButtons = findDetailButtonsAndFilter(describeExt, controllerContext.getUser());
        //更新布局中自定义按钮激活状态和名称
        ButtonOrder.updateCustomButtonWithLayout(customButtons, layoutExt);
        //无重复并集
        ButtonOrder.backhanderNewCustomButtons(layoutExt, customButtons);
        //追加新增预定义按钮
        ButtonOrder.backhanderNewPredefinedButtons(layoutExt, describeExt);
        // 过滤被被隐藏的按钮
        filterHiddenButtons(layoutExt);
    }

    private void filterHiddenButtons(LayoutExt layoutExt) {
        List<String> hiddenButtons = layoutExt.getHiddenButtons();
        if (CollectionUtils.empty(hiddenButtons)) {
            return;
        }
        List<IButton> buttonOrder = layoutExt.getButtonOrder();
        buttonOrder.removeIf(it -> hiddenButtons.contains(it.getName()));
        layoutExt.setButtonOrder(buttonOrder);
    }

    private List<IButton> findDetailButtonsAndFilter(ObjectDescribeExt describeExt, User user) {
        List<IUdefButton> buttonList = serviceFacade.findButtonList(user, describeExt.getApiName());
        List<IUdefButton> buttons = serviceFacade.filterButtonsForUsePageType(user, null, describeExt, ButtonUsePageType.Detail.getId(), buttonList, Collections.emptySet());

        Set<String> buttonNames = AppFrameworkConfig.webDetailButtonGray(user.getTenantId(), describeExt.getApiName());
        if (CollectionUtils.empty(buttonNames)) {
            return ButtonOrder.filteringUdefButton(buttons);
        }
        return buttons.stream().map(ButtonExt::of)
                .filter(ButtonExt::isActive)
                .filter(it -> !it.isSystemButton() || buttonNames.contains(it.getApiName()))
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());
    }

    /**
     * 获取详情页布局的特殊按钮
     *
     * @param describeExt
     * @return
     */
    protected List<IButton> getDetailPageSpecialButtons(ObjectDescribeExt describeExt) {
        if (!describeExt.isCustomObject()) {
            SpecialButtonProvider provider = specialButtonManager.getProvider(describeExt.getApiName());
            return provider.getSpecialButtons();
        }
        return Lists.newArrayList();
    }

    /**
     * 过滤详情页按钮
     *
     * @param buttons
     * @param user
     * @return
     */
    protected List<IButton> filterDetailPageButton(List<IButton> buttons, User user) {
        if (CollectionUtils.empty(buttons)) {
            return buttons;
        }
        if (AppFrameworkConfig.isGrayLayoutDesignerButtonManagerObject(describe.getApiName())) {
            return layoutDesignerButtonManager.getLocalProvider(describe.getApiName()).getButtons(buttons, user);
        }
        return buttons;
    }

    /**
     * 加工详情页布局的按钮，子类可重写
     *
     * @param buttons
     * @param user
     * @return
     */
    private List<IButton> processDetailPageButtons(List<IButton> buttons, User user) {
        if (CollectionUtils.empty(buttons)) {
            return buttons;
        }
        //老对象特殊按钮处理,不启用的按钮不在布局中显示
        List<IButton> specialButtons = getDetailPageSpecialButtons(ObjectDescribeExt.of(describe));
        if (CollectionUtils.notEmpty(specialButtons)) {
            ButtonOrder.combineSpecialButtons(buttons, specialButtons);
        }
        buttons = serviceFacade.filterPartnerButtons(user, describe.getApiName(), buttons);
        // 处理预置对象,需要开通服务才展示的按钮
        buttons = filterDetailPageButton(buttons, user);
        return buttons;
    }

    private List<Map<String, Object>> handleUIEventFunction(ILayout layout) {
        // 补充UI事件中的函数信息
        List<Map<String, Object>> events = layout.getEvents();
        if (CollectionUtils.notEmpty(events)) {
            List<UIEventExt> eventXs = UIEventExt.ofList(events);
            List<String> funcApiNames = eventXs.stream().map(UIEventExt::getFuncApiName).collect(Collectors.toList());
            List<IUdefFunction> functions = serviceFacade.getFunctionLogicService().findFunctionByApiNames(controllerContext.getUser(),
                    funcApiNames, describe.getApiName());
            for (UIEventExt e : eventXs) {
                functions.stream().filter(
                        f -> f.getApiName().equals(e.getFuncApiName())).findFirst().ifPresent(func -> {
                    e.set(UIEventExt.FUNC_NAME, func.getFunctionName());
                    e.set(UIEventExt.FUNC_DESCRIBE, func.getRemark());
                });

            }
        }
        return events;
    }

    @Override
    protected void finallyDo() {
        LayoutContext.remove();
        super.finallyDo();
    }

    @Data
    public static class Arg {
        String describeApiName;
        String layoutApiName;
        boolean includeLayout = true;
        boolean includeDescribe = true;
        private boolean includeDescribeExtra;
        boolean includeFieldsExtra;
        /**
         * 应用Id
         */
        private String appId;
        private boolean noNeedReplaceI18n = false;
        private boolean removeI18n = false;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        LayoutDocument layout;
        ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExtra;
        private List<Map<String, Object>> fieldsExtra;
    }

}