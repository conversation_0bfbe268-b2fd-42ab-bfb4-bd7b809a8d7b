package com.facishare.paas.appframework.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FrameComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.facishare.paas.timezone.config.TimeZoneConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.userdefobj.DefObjConstants.ALLOBJ;

/**
 * Created by zhouwr on 2018/5/3
 */
@Slf4j
public abstract class AppFrameworkConfig {

    public static final int DEFAULT_MAX_QUERY_LIMIT = 2000;
    public static volatile int MAX_QUERY_LIMIT = 2000;
    public static volatile int MAX_QUERY_AREA_LIMIT = 500;

    @Getter
    private static int maxChangeOwnerDataIdNum = 2000;

    @Getter
    private static volatile int maxQueryLimit = 2000;
    @Getter
    private static final int grayMaxQueryLimit = 3_000;

    @Getter
    private static volatile int maxQueryAreaLimit = 500;
    @Getter
    private static volatile int maxDetailDataQueryTimes = 5;

    @Getter
    private static volatile int maxQueryLimitWithCalculateCount = 100;

    @Getter
    private static volatile int maxQueryLimitWithIds = 500;

    @Getter
    private static volatile int maxQueryLimitWithIdsAndCalculateCount = 100;

    public static final int WIPE_CUSTOM_SETTINGS_BATCH_SIZE = 1_000;

    public static final String ALL = "ALL";

    public static final String UDOBJ = "udobj";

    public static final String CUSTOMER = "customObj";

    public static final String DEFAULT = "default";
    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
    private static final Splitter CONFIG_SPLITTER2 = Splitter.on(";").omitEmptyStrings().trimResults();
    private static final Splitter CONFIG_SPLITTER3 = Splitter.on("|").omitEmptyStrings().trimResults();

    @Getter
    private static Set<String> exportVipTenantIds = Sets.newHashSet();

    @Getter
    private static Set<String> vipTenantIds = Sets.newHashSet();

    @Getter
    private static Set<String> clonePicTenantIds = Sets.newHashSet();

    @Getter
    private static Set<String> numberFieldTypes = Sets.newHashSet();

    @Getter
    private static Set<String> dateFieldTypes = Sets.newHashSet();

    @Getter
    private static Set<String> listQueryByCurrentLangBlackObject = Sets.newHashSet();

    @Getter
    private static Set<String> fileFieldTypes = Sets.newHashSet();

    @Getter
    private static Set<String> ignoreFieldTypesForApproval = Sets.newHashSet();

    @Getter
    private static Set<String> ignoreFieldTypesForDiff = Sets.newHashSet();

    @Getter
    private static Map<String, List<String>> ignoreFieldApiNamesForApproval = Maps.newHashMap();

    @Getter
    private static List<String> objectOrder = Lists.newArrayList();

    @Getter
    private static Map<String, String> dataPrivilegeMap = Collections.emptyMap();

    @Getter
    private static Set<String> relatedNotNeedDataAuth = Sets.newHashSet();

    private static Set<String> uniqueRuleRedisGrayTenant = Sets.newHashSet();
    // 唯一性规则redis过期时间、单位秒
    @Getter
    private static volatile int uniqueRuleRedisExpireTime;
    // 启用兼容 null字符串
    @Getter
    private static volatile boolean uniqueRuleIncludeNull;
    // 唯一性规则字段全为空时，不调用查重服务
    @Getter
    private static volatile boolean uniqueRuleValueAllEmptyNotQueryGray;

    private static Set<String> packageObjectListWhichSupportDescribeCache;
    //启用预设describe缓存的企业id
    private static Set<String> packageObjectListWhichSupportDescribeCacheTenants;
    private static Set<String> tenantIdListWhichSupportDataVersionCheck;
    private static Set<String> masterDetailApprovalWhiteList;
    private static Set<String> masterDetailApprovalGrayList;

    @Getter
    private static Map<String, List<String>> outTeamMemberRole = Maps.newHashMap();
    @Getter
    private static Map<String, List<String>> teamMemberRole = Maps.newHashMap();
    private static Map<String, List<String>> teamMemberRoleBlackField = Maps.newHashMap();
    private static Map<String, List<String>> ignoreAuditLogFields = Maps.newHashMap();

    private static Map<String, Integer> enableMultiLangFieldCount = Maps.newHashMap();

    private static Set<String> skipValidateLookupGrayTenant = Sets.newHashSet();

    private static Set<String> mobileSpecialComponents = Sets.newHashSet();

    @Getter
    private static Set<String> dataLangSupportPreFieldApiName = Sets.newHashSet();

    @Getter
    private static List<String> defaultButtonOrder = Lists.newArrayList();

    private static Set<String> multiFieldName = Sets.newHashSet();

    private static Set<String> SPECIAL_MULTI_LANG_DESCRIBE_API_NAME = Sets.newHashSet();

    private static Set<String> relatedTeamDataPermissionModifyWhiteTenants = Sets.newHashSet();
    private static Set<String> updateIgnoreOtherGrayApiName = Sets.newHashSet();
    private static Set<String> industryEnterInfoGrayTenantId = Sets.newHashSet();
    private static Set<String> relatedListFunctionTenant = Sets.newHashSet();

    private static Map<String, Map<String, List<String>>> layoutSpecialRelatedObjectSetting = Maps.newHashMap();
    private static Map<String, Map<String, Object>> layoutSpecialFrameSetting = Maps.newHashMap();

    private static Map<String, List<String>> updateImportByIdGrayTenant = Maps.newHashMap();

    private static Map<String, List<String>> supportMultiLangObjField = Maps.newLinkedHashMap();

    private static Map<String, List<String>> specialMaskFieldMap = Maps.newHashMap();

    private static Set<String> showCustomButtonInLockGrayTenant = Sets.newHashSet();

    private static Map<String, Integer> relatedListFunctionCount = Maps.newHashMap();

    private static Set<String> listSearchGrayObject = Sets.newHashSet();
    private static Map<String, List<String>> listSearchSupportOr = Maps.newHashMap();
    private static Set<String> changeOwnerClearGrayObject = Sets.newHashSet();
    private static Map<String, GrayRule> changeOwnerClearGray = Maps.newHashMap();

    //默认将跟进动态放在布局最前面的对象
    private static Set<String> saleLogHighOrderObjects = Sets.newHashSet();

    private static Set<String> importApprovalFlowTenantGray = Sets.newHashSet();
    private static Map<String, GrayRule> importTriggerApprovalFlowGray = Maps.newHashMap();
    private static Map<String, GrayRule> unionInsertImportTriggerApprovalFlowGray = Maps.newHashMap();
    // 目标值支持新建导入的灰度企业
    private static Set<String> goalValueObjectImportGray = Sets.newHashSet();
    //草稿黑名单企业
    private static Set<String> dataDraftTenantBlackList = Sets.newHashSet();
    private static Set<String> dataDraftObjectBlackList = Sets.newHashSet();

    private static Map<String, List<Map<String, String>>> relatedListNaviUrl = Maps.newHashMap();

    private static Map<String, List<Map<String, String>>> uiActionNaviUrl = Maps.newHashMap();

    // 移动端不支持批量操作的对象名单
    private static Set<String> mobileListBlackObjectList = Sets.newHashSet();

    // 新建编辑按钮的灰度
    private static Set<String> addEditUiActionGrayTenant = Sets.newHashSet();
    private static Set<String> addEditUiActionGrayObject = Sets.newHashSet();
    private static Set<String> addEditUiActionGrayObject735 = Sets.newHashSet();
    private static Set<String> editUiButtonDisplayConditions = Sets.newHashSet();
    private static Set<String> showingOutTeamMemberGrayTenant = Sets.newHashSet();

    private static Set<String> supportSaveAndCreateBlackTenant = Sets.newHashSet();
    private static Set<String> duplicateDataGrayTenant = Sets.newHashSet();

    //在v1布局中不展示跟进动态组件的对象
    private static Set<String> hideSaleLogInV1LayoutObjects = Sets.newHashSet();

    // 查重规则在redis中的过期时间
    @Getter
    private static volatile int duplicatedSearchRedisExpireTimes = 10;

    private static Set<String> teamMemberDisableSelectAllTenant = Sets.newHashSet();
    private static Set<String> supportAccountOperationMapTenants = Sets.newHashSet();
    private static Set<String> createAndEditLayoutSupportTransComponentType = Sets.newHashSet();

    // 货币列表
    @Getter
    private static List<Map<String, String>> currencyList = Lists.newArrayList();

    @Getter
    private static List<String> currencyBlackObjectList = Lists.newArrayList();

    // 语种字段对齐默认配置
    @Getter
    private static List<Map<String, String>> fieldAlignDefaultConfigs = Lists.newArrayList();

    //多组织黑名单
    @Getter
    private static List<String> organizationBlackObjectList = Lists.newArrayList();

    // 查重锁 等待时间
    @Getter
    private static volatile long duplicateSearchLockWaitTimeSeconds = 5;
    // 查重锁 持有时间

    @Getter
    private static volatile long duplicateSearchLockWaitLeaseTimeSeconds = 10;
    // 查重全局锁 等待时间
    @Getter
    private static volatile long duplicateSearchGlobalLockWaitTimeSeconds = 8;
    // 查重全局锁 持有时间
    @Getter
    private static volatile long duplicateSearchGlobalLockWaitLeaseTimeSeconds = 5;

    // 禁用查重锁的灰度企业
    private static Set<String> disableDuplicateSearchLockEi = Sets.newHashSet();

    private static Set<String> useMultiRegionFiledType = Sets.newHashSet();
    // 启用完全匹配灰度
    private static Map<String, List<String>> preciseDuplicateSearchGray = Maps.newHashMap();
    private static Set<String> preciseDuplicateSearchGrayEi = Sets.newHashSet();

    private static Set<String> listLayoutGrayEi = Sets.newHashSet();
    private static Set<String> listLayoutBlackEi = Sets.newHashSet();
    private static Set<String> listLayoutGrayObject = Sets.newHashSet();
    private static Set<String> listLayoutGrayObject750 = Sets.newHashSet();
    private static Set<String> listLayoutGrayEi750 = Sets.newHashSet();

    /**
     * 910 之后，列表页布局全网，只保留不支持的预置对象
     */
    private static GrayRule LIST_LAYOUT_GRAY_RULE;

    private static Set<String> duplicateLimitGrayEi = Sets.newHashSet();
    private static Set<String> duplicateOuterGrayEi = Sets.newHashSet();

    private static Set<String> printTemplateDateTimeUseUserTimeZoneEi = Sets.newHashSet();
    private static volatile int duplicateLimitGray = 200;

    @Getter
    private static volatile int bulkDeleteLimit;

    @Getter
    private static volatile int bulkDeleteMasterLimit;
    // 导出图片支持的最大数据域条数
    private static volatile int exportFileRowsThrottleVip;
    private static volatile int exportFileRowsThrottle;

    @Getter
    private static volatile int fieldPromptLimit = 20;

    private static Map<String, Integer> bodySizeLimit = Maps.newHashMap();

    private static Map<String, String> preObjNameToApiName = Maps.newHashMap();

    private static Set<String> syncFieldGrayEi = Sets.newHashSet();

    private static Set<String> bodySizeLimitGrayEi = Sets.newHashSet();

    private static Set<String> applyDataPrivilegeCheckEi = Sets.newHashSet();
    private static Set<String> applyDataPrivilegeCheckBlackDescribeApiName = Sets.newHashSet();
    private static Set<String> exportUseDbEi = Sets.newHashSet();

    @Getter
    private static volatile boolean pictureAnnexDownloadFuncCodeSwitch = false;

    @Getter
    private static Set<String> uiEventCurrencyGrayEi = Sets.newHashSet();

    private static Set<String> importObjectListGrayEi = Sets.newHashSet();
    //  灰度导入的模块
    private static Map<String, List<String>> importObjectModuleGray = Maps.newHashMap();
    private static Map<String, Set<String>> importObjectModule = Maps.newHashMap();
    private static List<String> importObjectModuleOrder = Lists.newArrayList();

    private static Map<String, List<String>> detailRenderTable = Maps.newHashMap();
    private static Set<String> switchCacheGrayEi = Sets.newHashSet();

    //灰度引用人员字段的计算字段支持筛选的企业
    private static Set<String> grayFilterByEmployeeFormulaTenants = Sets.newHashSet();

    private static Set<String> duplicatedSearchPostMessageObj = Sets.newHashSet();

    private static Map<String, Integer> detailObjectSizeLimit = Maps.newHashMap();

    private static Map<String, List<String>> insertImportUnsupportFieldName = Maps.newHashMap();
    private static Map<String, List<String>> insertImportUnsupportFieldType = Maps.newHashMap();
    private static Map<String, List<String>> updateImportUnsupportFieldName = Maps.newHashMap();
    private static Map<String, List<String>> updateImportUnsupportFieldType = Maps.newHashMap();

    private static List<String> grayAllFieldTypeList = Lists.newArrayList();
    private static List<String> grayObjectReferenceManyFieldListTenantid = Lists.newArrayList();
    private static List<String> grayBigFileAttachmentFieldListEi = Lists.newArrayList();
    private static List<String> bigObjectFieldTypeList = Lists.newArrayList();

    private static Set<String> functionPermissionAppIdGrayEi = Sets.newHashSet();
    private static volatile boolean needConvertQueryResultDateFieldValue;

    private static Map<String, List<String>> functionNamespaceValidator = Maps.newHashMap();

    @Getter
    private static volatile boolean approvalTaskFlowLayoutFieldUseGray = true;

    private static volatile boolean ES_CHECK_DUPLICATE = true;

    private static Set<String> whatListWhatFieldGrayEi = Sets.newHashSet();

    private static Set<String> draftIgnoreAppId = Sets.newHashSet();

    private static Set<String> createEditSystemButtonGrayEi = Sets.newHashSet();

    private static Set<String> listLayoutSupportTransComponent = Sets.newHashSet();

    @Getter
    private static Set<String> listLayoutSupportComponent = Sets.newHashSet();

    private static Map<String, GrayRule> listLayoutSupportTransComponentGray = Maps.newHashMap();

    private static Map<String, List<String>> createEditSystemButtonSupport = Maps.newHashMap();

    private static Set<String> outDraftAppIdGrayEi = Sets.newHashSet();

    //支持超过500个字段的企业id
    private static Set<String> supportMoreFieldsEI = Sets.newHashSet();
    //支持导入的时候同步计算的企业id
    private static Set<String> calculateInImportEI = Sets.newHashSet();
    //更新计算字段不更新版本号的企业id
    private static Set<String> calculateSkipVersionChangeEI = Sets.newHashSet();
    //更新导入指定唯一字段支持的字段

    //检查数据权限时，参数datalist中data瘦身后在rpc走provider
    private static Set<String> dataPrivilegeProviderSimpleGrayEi = Sets.newHashSet();
    // 移动端新建、编辑页支持 UI 按钮的企业
    private static Set<String> mobileSupportUiPaasButtonGrayEi = Sets.newHashSet();

    private static Set<String> removeDuplicatedValueGrayEi = Sets.newHashSet();

    private static volatile boolean validateLookupSearchDataInDb = true;

    // 待办列表灰度不查询相关团队
    private static Set<String> whatListIsAllowDeleteAllMembersGrayEi = Sets.newHashSet();
    private static Set<String> whatListIsAllowDeleteAllMembersGrayApiName = Sets.newHashSet();
    // 批量打印excel导出灰度
    private static Set<String> batchPrintExportGrayEi = Sets.newHashSet();
    private static Set<String> batchPrintExportObjectList = Sets.newHashSet();

    //EditAction中使用增量更新的企业id(自定义对象使用)
    private static Set<String> incrementUpdateInEditActionTenant = Sets.newHashSet();
    //EditAction中使用增量更新的对象apiName
    private static Map<String, List<String>> incrementUpdateInEditActionObjects = Maps.newHashMap();

    // 灰度将客户、合作伙伴数据转换为外部负责人
    private static Set<String> grayRelationOuterDataPrivilegeTenant = Sets.newHashSet();

    // 导出查询场景显示列
    private static Set<String> exportAccordingToTemplateFieldListGrayEi = Sets.newHashSet();

    private static Set<String> wxMiniProgramEi = Sets.newHashSet();
    // 主属性支持显示字段灰度企业id
    private static Set<String> primaryAttributeSupportDisplayNameGrayEi = Sets.newHashSet();
    private static Set<String> supportDisplayNameFieldObject = Sets.newHashSet();
    // 流程已办场景，不走数据权限
    private static Set<String> flowTaskPassTemplateDataRightEi = Sets.newHashSet();

    // 灰度按钮设计器按钮特殊处理的对象
    private static Set<String> layoutDesignerButtonManagerGray = Sets.newHashSet();

    private static Map<String, List<String>> addEditUiActionGrayObjectEi = Maps.newHashMap();

    private static Set<String> grayRecoverButtonEi = Sets.newHashSet();
    private static Set<String> grayRecoverButtonObject = Sets.newHashSet();

    private static Set<String> userControlBpmListAndApprovalListComponentsObj = Sets.newHashSet();

    // es 分页查询优化的标记的灰度开关
    private static volatile boolean esPaginationOptimization = false;

    private static Set<String> mappingRuleWhiteObj = Sets.newHashSet();

    @Getter
    private static int maxErrorMessageLength = 500;

    @Getter
    private static volatile int numberStepMaxValue = Integer.MAX_VALUE;

    private static Map<String, List<String>> dataTypeValidateIgnore = Maps.newHashMap();

    //灰度移动端卡片模式的企业(通用预设对象使用)
    private static Set<String> commonGrayMobileCardRenderTypeTenant = Sets.newHashSet();
    //全网移动端卡片模式的预设对象
    private static Set<String> grayMobileCardRenderTypePredefineObjects = Sets.newHashSet();
    //独立灰度移动端卡片模式的预设对象和企业id(预设对象使用，key-预设对象apiName，value-灰度企业id数组)
    private static Map<String, List<String>> grayMobileCardRenderTypeObjects = Maps.newHashMap();

    private static Map<String, Integer> whatListRelateDataMaxCount = Maps.newHashMap();
    // 对象显示字段支持whatList字段黑名单对象
    private static Set<String> displayNameSupportWhatListFieldBlackListObjects = Sets.newHashSet();

    //触发新建审批时直接透传数据的对象(key-对象apiName，value-灰度企业id数组)
    private static Map<String, List<String>> startAddApprovalWithDataObjects = Maps.newHashMap();

    //工作流黑名单(key-企业id，value-对象apiName列表)
    private static Map<String, List<String>> workflowBlacklist = Maps.newHashMap();

    @Getter
    private static volatile int optionDelayTimeLevel;

    private static Map<String, List<GrayRuleHelper<Set<String>>>> listSingleButtonGray = Maps.newHashMap();
    private static Map<String, List<GrayRuleHelper<Set<String>>>> listNormalButtonGray = Maps.newHashMap();
    private static Map<String, List<GrayRuleHelper<Set<String>>>> webDetailButtonGray = Maps.newHashMap();
    private static Map<String, List<GrayRuleHelper<Set<String>>>> buttonPostFuncActionAsyncGray = Maps.newHashMap();

    private static Map<String, Map<String, String>> recordTypeFuncGray = Maps.newHashMap();
    // 列表页补充拓展数据的等待时间
    private static volatile int listAsyncFillFieldInfoWaitTime = 20;
    // 补充引用字段的等待时间
    private static volatile int fillQuoteFieldInfoWaitTime = 20;
    // 联合导入关联标识的过起时间偏移量
    @Getter
    private static volatile int importExpireTimeOffsetDate = 0;

    private static Map<String, GrayRule> importSupportSpecifiedFieldGray = Maps.newHashMap();
    private static Map<String, GrayRule> designerLayoutButton = Maps.newHashMap();

    private static Map<String, List<String>> logTypeObject = Maps.newHashMap();

    private static Map<String, GrayRule> uiEventSupportModifyMasterDetailGray = Maps.newHashMap();

    private static Map<String, GrayRule> grayManyAbstractLayoutFilterConfig = Maps.newHashMap();

    private static Map<String, GrayRule> relatedListFormSupportObject = Maps.newHashMap();
    private static Map<String, List<String>> relatedListFormSupportObjectField = Maps.newHashMap();

    /**
     * 不支持按钮的内部对象
     */
    private static Set<String> unSupportButtonInternalObject = Sets.newHashSet();

    @Getter
    private static Set<String> undisplayedPresetObjects = Sets.newHashSet();

    private static Map<String, GrayRule> treeViewSupportObject = Maps.newHashMap();

    private static volatile boolean treeViewObjectNotSupportImport;

    @Getter
    private static volatile int treeViewObjectAllowMaxHierarchy;

    @Getter
    private static volatile int treeViewObjectAllowMaxSaveSize;
    @Getter
    private static volatile int treeViewObjectAllowMaxChildSize;

    //流程布局最大个数(key:企业id)
    private static Map<String, Integer> maxFlowLayoutCountMap = Maps.newHashMap();
    private static Map<String, List<String>> needDiffSystemFieldName = ImmutableMap.of(UDOBJ, ImmutableList.of(DBRecord.OUT_TENANT_ID, DBRecord.OUT_OWNER, IObjectData.NAME));
    private static Map<String, List<String>> unSupportFieldForModifyLog = Maps.newHashMap();

    private static volatile long maxCountObjectDataGray;

    private static volatile long maxHistoryDataCountGray;

    //通信运营商
    @Getter
    private static List<String> communicationOperator = Lists.newArrayList();

    @Getter
    private static List<String> municipalityDirectlyUnderTheCentralGovernmentCode = Lists.newArrayList();

    //主从一起新建编辑页面从对象单行按钮支持平铺按钮的灰度
    private static Map<String, GrayRule> masterDetailEditPageDetailSingleButtonGray = Maps.newHashMap();
    //主从一起新建编辑页面从对象单行按钮支持插入按钮的灰度
    private static Map<String, GrayRule> masterDetailEditPageDetailSingleButtonInsertGray = Maps.newHashMap();
    // 前验证函数支持修改从对象数据的灰度
    private static Map<String, GrayRule> validationFunctionMergeDetailDataGray = Maps.newHashMap();
    private static Map<String, GrayRule> filterButtonsProviderGray = Maps.newHashMap();
    private static Map<String, GrayRule> exportExcelPageGray = Maps.newHashMap();
    // 掩码加密字段灰度
    private static Map<String, GrayRule> maskFieldEncryptGray = Maps.newHashMap();
    private static Map<String, GrayRule> maskFieldEncryptObjectFieldsGray = Maps.newHashMap();
    private static Map<String, GrayRule> maskFieldEncryptObjectPagesGray = Maps.newHashMap();
    private static Map<String, GrayRule> whatListDescribeCacheGray = Maps.newHashMap();
    // 前验证函数支持修改从对象数据,不支持合并的字段
    private static Set<String> validationFunctionMergeDetailIgnoreFieldsGray = Sets.newHashSet("relevant_team", "owner", "out_owner", "out_tenant_id", "partner_id", "data_own_department", "data_own_organization");

    //灰度资源配额启用校验企业
    private static Set<String> enableCheckEnterpriseResourcesQuote = Sets.newHashSet();

    private static Set<String> dateTimeSupportNotUseMultitimeZoneTenant = Sets.newHashSet();

    @Getter
    private static volatile int syncCalculateDataDataBatchSize = 100;

    @Getter
    private static volatile int syncCalculateDataMaxQueryCount = 10_000;

    private static Map<String, Integer> optionSetMaxCountGray = Maps.newHashMap();

    @Getter
    private static List<String> presetObjectsNoLicense = Lists.newArrayList();

    private static Map<String, Map<String, List<String>>> multiLangSupport = Maps.newHashMap();
    private static Map<String, GrayRule> objectMultiLangGray = Maps.newHashMap();

    private static Set<String> closeRelatedTeamSwitchObject = Sets.newHashSet();
    private static Set<String> closeGlobalSearchSwitchObject = Sets.newHashSet();
    private static Set<String> closeFollowUpDynamicSwitchObject = Sets.newHashSet();
    private static Set<String> closeModifyRecordSwitchObject = Sets.newHashSet();

    private static Map<String, List<String>> childBulkCreateGray = Maps.newHashMap();

    private static Set<String> layoutUnincludeFlowComponent = Sets.newHashSet();

    // 列表页通用UI按钮支持传参的灰度
    private static Map<String, List<GrayRuleHelper<Set<String>>>> listNormalUiActionSupportQueryParam = Maps.newHashMap();

    private static Map<String, Map<String, Set<String>>> duplicateSearchSupportIgnoreUpperAndLower = Maps.newHashMap();

    private static Set<String> transWorkBenchSupportMobileComponents = Sets.newHashSet();

    //web布局转移动端布局保留在layout_structure中的组件
    @Getter
    private static List<String> webToMobileLayoutReserveInStructureComponents = Lists.newArrayList();
    // web布局转移动端布局保留在layout_structure中的组件类型
    @Getter
    private static List<String> webToMobileLayoutReserveInStructureComponentTypes = Lists.newArrayList();
    //web布局转移动端布局要过滤掉的组件
    private static List<String> webToMobileLayoutFilterComponents = Lists.newArrayList();
    //web布局转移动端布局要过滤掉的组件类型
    private static List<String> webToMobileLayoutFilterComponentTypes = Lists.newArrayList();

    @Getter
    private static volatile String gdprUrl;
    private static Map<String, Integer> masterRelatedObjectComponentLimit = Maps.newHashMap();

    private static Map<String, Integer> htmlRichTextLimit = Maps.newHashMap();
    private static Map<String, Integer> richTextLimit = Maps.newHashMap();
    private static Map<String, Integer> bigTextLimit = Maps.newHashMap();
    private static Map<String, Integer> objectRichTextFieldMaxCount = Maps.newHashMap();
    private static Set<String> richTextObjectBlackList = Sets.newHashSet();
    private static Map<String, Integer> objectHtmlRichTextFieldMaxCount = Maps.newHashMap();
    private static Map<String, Integer> objectBigTextFieldMaxCount = Maps.newHashMap();
    private static Map<String, Integer> addTeamMemberLimit = Maps.newHashMap();

    private static Map<String, Integer> objectLookupFieldIncreaseNum = Maps.newHashMap();

    private static Set<String> isSupportSystemTag = Sets.newHashSet();
    private static Set<String> multiDuplicateRuleAndSupportFilterGrayEi = Sets.newHashSet();

    private static Set<String> multiDuplicateRuleAndSupportFilterBlackGrayObject = Sets.newHashSet();

    private static Set<String> executeButtonBeforeValidateBlackObj = Sets.newHashSet();

    private static Set<String> manageGroupGrayEi = Sets.newHashSet();

    /**
     * 不支持翻译工作台组件名称翻译的组件
     */
    private static Set<String> transMultiLanguageFilterComponent = Sets.newHashSet();
    private static Map<String, GrayRule> transMultiLanguageFilterComponentV2 = Maps.newHashMap();

    @Getter
    private static volatile int multiDuplicateCountLimit = 5;

    private static Set<String> socialObjGray = Sets.newHashSet();

    private static Map<String, GrayRule> lookupTreeViewObjectNotDisplayWholePathGrayEi = Maps.newHashMap();

    private static Map<String, GrayRule> simpleDescribeGrayEi = Maps.newHashMap();
    private static Set<String> simpleDescribeFieldAttrWhiteList = Sets.newHashSet();
    //Add接口使用参数幂等校验的灰度规则
    private static Map<String, GrayRule> paramsIdempotentGrayRules = Maps.newHashMap();

    private static Map<String, GrayRule> buttonAsyncCallBack = Maps.newHashMap();
    //查询数据时实时计算统计字段的灰度规则
    private static Map<String, GrayRule> calculateCountWhenFindDataGrayRules = Maps.newHashMap();

    @Getter
    private static volatile int outerOrganizationMaxPageSize = 4;

    private static GrayRule fieldEnableCloneEiGrayRule = new GrayRule("deny");

    private static Set<String> fieldEnableCloneFieldTypeGray = Sets.newHashSet();

    private static Map<String, GrayRule> fieldEnableCloneGray = Maps.newHashMap();

    private static Map<String, Integer> detailObjectByPreobjSizeLimit = Maps.newHashMap();

    private static Map<String, String> initToolsSysdbRouteTenantIdMap = Collections.emptyMap();

    private static Set<String> notCheckCountDetailObjects = Sets.newHashSet();

    private static Set<String> oldLayoutConvertV3AddComponents = Sets.newHashSet();

    private static Set<String> convertRuleSupportObjects = Sets.newHashSet();

    private static Set<String> convertRuleBlackObjects = Sets.newHashSet();

    private static Map<String, GrayRule> editDraftObjectGray = Maps.newHashMap();

    private static Map<String, List<String>> batchUpdateFieldTypeSupportAttribute = Maps.newHashMap();

    //映射接口不校验从对象权限的的映射规则列表
    private static Set<String> notCheckDetailObjectPrivilegeMappingRules = Sets.newHashSet();

    private static Map<String, GrayRule> unSupportFieldTypesIfOutUserGrayRules = Maps.newHashMap();
    // 该时间点之后新建的ui事件函数，默认执行从对象计算的 2024-05-01 00:00:00.000
    private static Long UI_EVENT_DO_CALCULATE_DETAILS_LAST_TIME_GRAY = 1714492800000L;

    // 字段列表 显示 字段引用按钮
    public static String WHERE_FIELD_IS_USED_GRAY_KEY = "where_field";

    // 根据 targetObj 过滤一部分不需要记录的ref
    private static String SKIP_RECORD_REF_BY_TARGET_OBJ_GRAY_KEY = "skip_record_ref_by_target_obj";

    private static Set<String> SKIP_RECORD_REF_BY_TARGET_OBJ_GRAY = Sets.newHashSet();

    private static List<RouterInfo> brushRouterInfoList = Lists.newArrayList();
    @Getter
    private static List<String> whatListObjectSortList = Lists.newArrayList();
    //ai配置信息
    private static Map<String, Object> AI_CONFIG_INFO = Maps.newHashMap();
    //在线文档配置
    private static Map<String, Object> ONLINE_DOC_CONFIG_INFO = Maps.newHashMap();
    //包插件配置信息
    private static Map<String, Object> PACKAGE_PLUGIN_CONFIG_INFO = Maps.newHashMap();

    @Getter
    private static Integer Batch_Copy_Role_To_User_Limit = 50;

    public static final String NO_SUPPORT_CURRENT_LOGIN_USERID = "noSupportCurrentLoginUserId";

    public static final String GRAY_HEAD_INFO_TEMPLATE = "gray_head_info_template";

    private static final String MULTI_REGION_FILED_TYPE = IFieldType.DATE + "," + IFieldType.DATE_TIME + "," + IFieldType.TIME + "," + IFieldType.CURRENCY + "," + IFieldType.NUMBER;

    @Getter
    private static Set<String> noSupportCurrentLoginUserObjects = Sets.newHashSet();

    @Getter
    private static Map<String, Integer> splitOrderMaxNumLimit = Maps.newHashMap();

    private static Set<String> ignoreFilterValidateFields = Sets.newHashSet();

    private static Map<String, List<String>> validateIsIndexFields = Maps.newHashMap();

    @Getter
    private static Set<String> searchDataBlackObject = Sets.newHashSet();

    private static Map<String, GrayRuleHelper<Set<String>>> fieldGrayConfig = Maps.newHashMap();

    @Getter
    private static Map<String, List<String>> changeDetailOwnerAsync = Maps.newHashMap();

    /**
     * 侧边栏布局模版，key为对象apiName
     */
    private static Map<String, Map<String, Object>> sidebarLayoutTemplate = Maps.newHashMap();

    private static volatile Boolean DUPLICATED_SEARCH_REMOVE_SELF = true;

    @Setter
    @Getter
    private static Set<String> buttonClickModifyRecordGrayEis = Sets.newHashSet();

    //查数据时允许的筛选条件最大数量
    @Getter
    private static int maxFilterNumWhenSearchData;
    //查数据时单个筛选条件中允许的筛选值的最大数量
    @Getter
    private static int maxFieldValueNumWhenSearchData;
    @Getter
    private static List<String> notSupportObjectsInMobileDraft = Lists.newArrayList();
    private static Map<String, Integer> asyncTaskMaxDayQueryLimit = Maps.newHashMap();

    @Getter
    private static Set<String> funcCheckWhiteApiNameList = Sets.newHashSet();

    // 忽略单选字段选项依赖关系的清理
    private static Map<String, List<String>> ignoreCleanOptionRelation = Maps.newHashMap();

    @Getter
    private static volatile long maxBodySize = 50 * 1024 * 1024;

    private static String LOAD_CONFIG_FAIL_NOTIFY_SESSION_ID;

    //不支持编辑币种的对象列表
    private static Set<String> notSupportEditCurrencyObjects = Sets.newHashSet();

    //下游用户不支持的组件类型灰度配置（支持租户+组件类型）
    private static Map<String, GrayRule> outerUserUnsupportedComponentTypesGrayRules = Maps.newHashMap();

    static {
        ConfigFactory.getConfig("fs-paas-appframework-config", ChangeListenerHolder.create(config -> {
            log.warn("reload config fs-paas-appframework-config,content:{}", config.getString());

            transWorkBenchSupportMobileComponents = getSetFromConfig(config, "transWorkBenchSupportMobileComponents");

            Batch_Copy_Role_To_User_Limit = config.getInt("Batch_Copy_Role_To_User_Limit", Batch_Copy_Role_To_User_Limit);
            setMaxQueryLimit(config.getInt("maxQueryLimit", 2000));
            setMaxChangeOwnerDataIdNum(config.getInt("maxChangeOwnerDataIdNum", 2000));

            setMaxQueryAreaLimit(config.getInt("maxQueryAreaLimit", 500));
            maxDetailDataQueryTimes = config.getInt("maxDetailDataQueryTimes", 5);
            setMaxQueryLimitWithCalculateCount(config.getInt("maxQueryLimitWithCalculateCount", 100));
            setMaxQueryLimitWithIds(config.getInt("maxQueryLimitWithIds", 500));
            setMaxQueryLimitWithIdsAndCalculateCount(config.getInt("maxQueryLimitWithIdsAndCalculateCount", 100));

            setExportVipTenantIds(getSetFromConfig(config, "export.vip.tenantId"));
            setVipTenantIds(getSetFromConfig(config, "vip.tenantId"));
            setClonePicTenantIds(getSetFromConfig(config, "clonePic.tenantId"));
            setNumberFieldTypes(getSetFromConfig(config, "number.fieldType"));
            setDateFieldTypes(getSetFromConfig(config, "date.fieldType"));
            setFileFieldTypes(getSetFromConfig(config, "file.fieldType"));
            setIgnoreFieldTypesForApproval(getSetFromConfig(config, "approval.ignore.fieldType"));
            setIgnoreFieldTypesForDiff(getSetFromConfig(config, "diff.ignore.fieldType"));
            setIgnoreFieldApiNamesForApproval(parseMapFromConfig(config, "ignoreFieldApiNamesForApproval"));

            listQueryByCurrentLangBlackObject = getSetFromConfig(config, "listQueryByCurrentLangBlackObject");

            setObjectOrder(getListFromConfig(config, "object.order"));

            setRelatedNotNeedDataAuth(getSetFromConfig(config, "related.not.need.dataAuth"));

            recordTypeFuncGray = parseMapFromConfig(config, "record_type_func_gray");

            setDataPrivilegeMap(parseMapFromConfig(config, "object.data_privilege"));

            uniqueRuleRedisGrayTenant = getSetFromConfig(config, "uniqueRule.redis.gray.tenant");

            // 默认三秒
            setUniqueRuleRedisExpireTime(config.getInt("uniqueRule.redis.expire.time", 3));
            setUniqueRuleIncludeNull(config.getBool("uniqueRule.include.null", false));

            setUniqueRuleValueAllEmptyNotQueryGray(config.getBool("uniqueRule.value.all.empty.not.query", false));

            setFieldPromptLimit(config.getInt("fieldPromptLimit", 20));

            DUPLICATED_SEARCH_REMOVE_SELF = config.getBool("duplicated_search_remove_self", true);

            packageObjectListWhichSupportDescribeCache = getSetFromConfig(config, "describe.cache.packageObject");
            packageObjectListWhichSupportDescribeCacheTenants = getSetFromConfig(config, "describe.cache.packageObject.tenants");
            tenantIdListWhichSupportDataVersionCheck = getSetFromConfig(config, "checkDataVersion.tenantId");
            masterDetailApprovalWhiteList = getSetFromConfig(config, "masterDetailApproval.whiteList");
            masterDetailApprovalGrayList = getSetFromConfig(config, "masterDetailApproval.grayList");
            enableMultiLangFieldCount = getIntMapFromConfig(config, "enableMultiLangFieldCount");
            dataLangSupportPreFieldApiName = getSetFromConfig(config, "dataLangSupportPreFieldApiName");

            setOutTeamMemberRole(parseMapFromConfig(config, "outTeamMember.role.config"));
            setTeamMemberRole(parseMapFromConfig(config, "teamMember.role.config"));
            teamMemberRoleBlackField = parseMapFromConfig(config, "teamMember.role.black.field.config");
            ignoreAuditLogFields = parseMapFromConfig(config, "auditLog.black.field.config");

            // 跳过 lookup 过滤条件校验的灰度企业
            skipValidateLookupGrayTenant = getSetFromConfig(config, "skip.validate.lookup.gray.tenant");

            preObjNameToApiName = parseMapFromConfig(config, "pre_obj_name_to_api_name");

            setDefaultButtonOrder(getListFromConfig(config, "default.button.order"));

            multiFieldName = getSetFromConfig(config, "multi.field.name");

            useMultiRegionFiledType = getSetFromConfig(config, "useMultiRegionFiledType", MULTI_REGION_FILED_TYPE);

            relatedTeamDataPermissionModifyWhiteTenants = getSetFromConfig(config, "relate.team.data.permission.modify.tenant");

            updateIgnoreOtherGrayApiName = getSetFromConfig(config, "update.ignore.other.gray.api.name");

            layoutSpecialRelatedObjectSetting = parseMapFromConfig(config, "layout.special.related.object.setting");
            layoutSpecialFrameSetting = parseMapFromConfig(config, "layout.special.frame.setting");

            updateImportByIdGrayTenant = parseMapFromConfig(config, "update.import.by.id.gray.tenant");

            userControlBpmListAndApprovalListComponentsObj = getSetFromConfig(config, "user.controller.bpm_related_list.and.approval_related_list.components.obj");

            supportMultiLangObjField = parseMapFromConfig(config, "support_multi_lang_obj_field");
            //需要下发原值的掩码字段
            specialMaskFieldMap = parseMapFromConfig(config, "mask.specialFields");
            // 查询工商信息的新接口
            industryEnterInfoGrayTenantId = getSetFromConfig(config, "industry_enter_info_gray_tenant_id");

            relatedListFunctionTenant = getSetFromConfig(config, "related.list.function.tenantId");
            // 锁定页面展示自定义按钮的灰度企业
            showCustomButtonInLockGrayTenant = getSetFromConfig(config, "show_custom_button_in_lock_gray_tenant");

            relatedListFunctionCount = parseMapFromConfig(config, "related.list.function.count");

            SPECIAL_MULTI_LANG_DESCRIBE_API_NAME = getSetFromConfig(config, "specialMultiLangDescribeApiName");

            listSearchGrayObject = getSetFromConfig(config, "list_search_gray_object");
            listSearchSupportOr = getFieldListMapFromConfig(config, "list_search_support_or");
            changeOwnerClearGrayObject = getSetFromConfig(config, "change_owner_clear_gray_object");
            changeOwnerClearGray = getFsGrayRule(config, "change_owner_clear_gray");

            exportExcelPageGray = getFsGrayRule(config, "export_excel_page_gray");
            saleLogHighOrderObjects = getSetFromConfig(config, "layout.sale_log_high_order.objects");

            batchUpdateFieldTypeSupportAttribute = parseMapFromConfig(config, "batch_update_field_type_support_attribute");

            importApprovalFlowTenantGray = getSetFromConfig(config, "import_approval_flow_tenant_gray");
            importTriggerApprovalFlowGray = getFsGrayRule(config, "import_trigger_approval_flow_gray");
            unionInsertImportTriggerApprovalFlowGray = getFsGrayRule(config, "union_insert_import_trigger_approval_flow_gray");
            goalValueObjectImportGray = getSetFromConfig(config, "goal_value_object_import_gray");

            dataDraftTenantBlackList = getSetFromConfig(config, "data_draft_tenant_black_list");
            dataDraftObjectBlackList = getSetFromConfig(config, "data_draft_object_black_list");

            relatedListNaviUrl = parseMapFromConfig(config, "related_list_navi_url");

            uiActionNaviUrl = parseMapFromConfig(config, "ui_action_navi_url");
            duplicatedSearchPostMessageObj = getSetFromConfig(config, "duplicated_search_post_message_obj");

            listLayoutSupportTransComponent = getSetFromConfig(config, "list_layout_support_trans_component");
            listLayoutSupportComponent = getSetFromConfig(config, "list_layout_support_component");

            // 移动端不支持批量操作的对象名单
            mobileListBlackObjectList = getSetFromConfig(config, "mobile_list_black_object_list");

            addEditUiActionGrayTenant = getSetFromConfig(config, "add_edit_ui_action_gray_tenant");
            addEditUiActionGrayObject = getSetFromConfig(config, "add_edit_ui_action_gray_object");
            addEditUiActionGrayObject735 = getSetFromConfig(config, "add_edit_ui_action_gray_object_735");
            addEditUiActionGrayObjectEi = getFieldListMapFromConfig(config, "add_edit_ui_action_gray_object_ei");
            editUiButtonDisplayConditions = getSetFromConfig(config, "edit_ui_button_display_conditions");
            showingOutTeamMemberGrayTenant = getSetFromConfig(config, "showing_out_team_member_gray_tenant");
            supportSaveAndCreateBlackTenant = getSetFromConfig(config, "support_save_and_create_black_tenant");

            hideSaleLogInV1LayoutObjects = getSetFromConfig(config, "layout.hideSaleLogInV1Layout.objects");
            duplicateDataGrayTenant = getSetFromConfig(config, "duplicate_data_gray_tenant");
            teamMemberDisableSelectAllTenant = getSetFromConfig(config, "team_member_disable_select_all_tenant");

            setDuplicatedSearchRedisExpireTimes(config.getInt("duplicated_search_redis_expire_times", 10));
            supportAccountOperationMapTenants = getSetFromConfig(config, "supportAccountOperationMap_ei");

            setCurrencyList(getListMapFromConfig(config, "currency_list"));
            setCurrencyBlackObjectList(getListFromConfig(config, "currency_black_object_list"));

            fieldAlignDefaultConfigs = getListMapFromConfig(config, "language_field_align_default_configs");
            setDuplicateSearchLockWaitTimeSeconds(config.getLong("duplicate_search_lock_wait_time_seconds", 5));
            setDuplicateSearchLockWaitLeaseTimeSeconds(config.getLong("duplicate_search_lock_wait_lease_time_seconds", 10));

            setDuplicateSearchGlobalLockWaitTimeSeconds(config.getLong("duplicate_search_global_lock_wait_time_seconds", 8));
            setDuplicateSearchGlobalLockWaitLeaseTimeSeconds(config.getLong("duplicate_search_global_lock_wait_lease_time_seconds", 5));

            listLayoutGrayEi = getSetFromConfig(config, "list_layout_gray_ei");
            listLayoutBlackEi = getSetFromConfig(config, "list_layout_black_ei");
            listLayoutGrayObject = getSetFromConfig(config, "list_layout_gray_object");
            listLayoutGrayObject750 = getSetFromConfig(config, "list_layout_gray_750_object");
            listLayoutGrayEi750 = getSetFromConfig(config, "list_layout_gray_750_ei");
            String listLayoutGrayRule = config.get("list_layout_gray_rule");
            if (Strings.isNullOrEmpty(listLayoutGrayRule)) {
                LIST_LAYOUT_GRAY_RULE = null;
            } else {
                LIST_LAYOUT_GRAY_RULE = new GrayRule(listLayoutGrayRule);
            }

            setBulkDeleteLimit(config.getInt("bulk_delete_limit", 200));
            setBulkDeleteMasterLimit(config.getInt("bulk_delete_master_limit", 2000));

            disableDuplicateSearchLockEi = getSetFromConfig(config, "disable_duplicate_search_lock_ei");
            preciseDuplicateSearchGray = getFieldListMapFromConfig(config, "precise_duplicate_search_gray");
            preciseDuplicateSearchGrayEi = getSetFromConfig(config, "precise_duplicate_search_gray_ei");

            bodySizeLimit = parseMapFromConfig(config, "body_size_limit");

            syncFieldGrayEi = getSetFromConfig(config, "sync_field_gray_ei");
            duplicateLimitGrayEi = getSetFromConfig(config, "duplicate_limit_gray_ei");
            duplicateLimitGray = config.getInt("duplicate_limit_gray", 200);
            duplicateOuterGrayEi = getSetFromConfig(config, "duplicate_outer_gray_ei");

            exportFileRowsThrottleVip = config.getInt("export_file_rows_throttle_vip", 10000);
            exportFileRowsThrottle = config.getInt("export_file_rows_throttle", 5000);
            mobileSpecialComponents = getSetFromConfig(config, "mobile_special_components");

            bodySizeLimitGrayEi = getSetFromConfig(config, "body_size_limit_gray_ei");

            applyDataPrivilegeCheckEi = getSetFromConfig(config, "apply_data_privilege_check_ei");
            applyDataPrivilegeCheckBlackDescribeApiName = getSetFromConfig(config, "apply_data_privilege_check_black_describe_api_name");

            setPictureAnnexDownloadFuncCodeSwitch(config.getBool("picture_annex_download_func_code_switch", false));
            exportUseDbEi = getSetFromConfig(config, "export_use_db_ei");

            setUiEventCurrencyGrayEi(getSetFromConfig(config, "ui_event_currency_gray_ei"));

            importObjectListGrayEi = getSetFromConfig(config, "import_object_list_gray_ei");
            importObjectModuleGray = getFieldListMapFromConfig(config, "import_object_module_gray");
            importObjectModuleOrder = getListFromConfig(config, "import_object_module_order");

            createAndEditLayoutSupportTransComponentType = getSetFromConfig(config, "createAndEditLayoutSupportTransComponentType");

            detailRenderTable = parseMapFromConfig(config, "detail_render_table");

            switchCacheGrayEi = getSetFromConfig(config, "switch_cache_gray_ei");

            grayFilterByEmployeeFormulaTenants = getSetFromConfig(config, "grayFilterByEmployeeFormulaTenants");

            detailObjectSizeLimit = parseMapFromConfig(config, "detail_object_size_limit");

            // 新建导入不支持的字段
            insertImportUnsupportFieldName = getFieldListMapFromConfig(config, "insert_import_unsupport_field_name");
            // 新建导入不支持的字段类型
            insertImportUnsupportFieldType = getFieldListMapFromConfig(config, "insert_import_unsupport_field_type");
            // 更新导入不支持的字段
            updateImportUnsupportFieldName = getFieldListMapFromConfig(config, "update_import_unsupport_field_name");
            // 更新导入不支持的字段类型
            updateImportUnsupportFieldType = getFieldListMapFromConfig(config, "update_import_unsupport_field_type");

            //新增加的灰度字段类型
            grayAllFieldTypeList = getListFromConfig(config, "gray_all_field_type_list");
            //大对象支持的字段类型
            bigObjectFieldTypeList = getListFromConfig(config, "big_object_field_type_list");
            //灰度查找关联多选的
            grayObjectReferenceManyFieldListTenantid = getListFromConfig(config, "gray_object_reference_many_field_list_tenantId");
            // 大附件字段灰度
            grayBigFileAttachmentFieldListEi = getListFromConfig(config, "gray_big_file_attachment_field_list_ei");

            functionPermissionAppIdGrayEi = getSetFromConfig(config, "function_permission_app_id_gray_ei");

            functionNamespaceValidator = parseMapFromConfig(config, "function_namespace_map");

            needConvertQueryResultDateFieldValue = config.getBool("needConvertQueryResultDateFieldValue", false);
            // 流程待办布局，启用灰度
            setApprovalTaskFlowLayoutFieldUseGray(config.getBool("approval_task_flow_layout_field_use_gray", true));
            // 流程待办 what 字段灰度企业
            whatListWhatFieldGrayEi = getSetFromConfig(config, "what_list_what_field_gray_ei");

            draftIgnoreAppId = getSetFromConfig(config, "draft_ignore_app_id");
            // 新建编辑页按钮
            createEditSystemButtonGrayEi = getSetFromConfig(config, "create_edit_system_button_gray_ei");
            createEditSystemButtonSupport = getFieldListMapFromConfig(config, "create_edit_system_button_support");

            outDraftAppIdGrayEi = getSetFromConfig(config, "out_draft_app_id_gray_ei");
            supportMoreFieldsEI = getSetFromConfig(config, "supportMoreFieldsEI");
            calculateInImportEI = getSetFromConfig(config, "calculateInImportEI");
            calculateSkipVersionChangeEI = getSetFromConfig(config, "calculateSkipVersionChangeEI");

            validateLookupSearchDataInDb = config.getBool("validate_lookup_search_data_in_db", true);

            dataPrivilegeProviderSimpleGrayEi = getSetFromConfig(config, "data_privilege_provider_simple_gray_ei");

            mobileSupportUiPaasButtonGrayEi = getSetFromConfig(config, "mobile_support_ui_paas_button_gray_ei");

            whatListIsAllowDeleteAllMembersGrayEi = getSetFromConfig(config, "what_list_is_allow_delete_all_members_gray_ei");
            whatListIsAllowDeleteAllMembersGrayApiName = getSetFromConfig(config, "what_list_is_allow_delete_all_members_gray_api_name");

            removeDuplicatedValueGrayEi = getSetFromConfig(config, "remove_duplicated_value_gray_ei");
            grayRelationOuterDataPrivilegeTenant = getSetFromConfig(config, "gray_relation_outer_data_privilege_tenant");

            batchPrintExportGrayEi = getSetFromConfig(config, "batch_print_export_gray_ei");
            batchPrintExportObjectList = getSetFromConfig(config, "batch_print_export_object_list");

            exportAccordingToTemplateFieldListGrayEi = getSetFromConfig(config, "export_according_to_template_field_list_gray_ei");

            incrementUpdateInEditActionTenant = getSetFromConfig(config, "incrementUpdateInEditActionTenant");
            incrementUpdateInEditActionObjects = parseMapFromConfig(config, "incrementUpdateInEditActionObjects");

            wxMiniProgramEi = getSetFromConfig(config, "wx_mini_program_gray_ei");

            primaryAttributeSupportDisplayNameGrayEi = getSetFromConfig(config, "primary_attribute_support_display_name_gray_ei");

            supportDisplayNameFieldObject = getSetFromConfig(config, "supportDisplayNameFieldObject");

            layoutDesignerButtonManagerGray = getSetFromConfig(config, "layout_designer_button_manager_gray_object");

            grayRecoverButtonEi = getSetFromConfig(config, "gray_recover_button_ei");
            grayRecoverButtonObject = getSetFromConfig(config, "gray_recover_button_object");

            maxErrorMessageLength = config.getInt("maxErrorMessageLength", 500);

            flowTaskPassTemplateDataRightEi = getSetFromConfig(config, "flow_task_pass_template_data_right_ei");

            esPaginationOptimization = config.getBool("es_pagination_optimization", false);

            commonGrayMobileCardRenderTypeTenant = getSetFromConfig(config, "commonGrayMobileCardRenderTypeTenant");
            grayMobileCardRenderTypePredefineObjects = getSetFromConfig(config, "grayMobileCardRenderTypePredefineObjects");
            grayMobileCardRenderTypeObjects = parseMapFromConfig(config, "grayMobileCardRenderTypeObjects");

            whatListRelateDataMaxCount = getIntMapFromConfig(config, "what_list_relate_data_max_count");

            mappingRuleWhiteObj = getSetFromConfig(config, "mapping_rule_white_obj");

            enableCheckEnterpriseResourcesQuote = getSetFromConfig(config, "enable_check_enterprise_resources_quota");

            dataTypeValidateIgnore = getFieldListMapFromConfig(config, "data_type_validate_ignore");

            displayNameSupportWhatListFieldBlackListObjects = getSetFromConfig(config, "display_name_support_what_list_field_black_list_objects");
            startAddApprovalWithDataObjects = parseMapFromConfig(config, "startAddApprovalWithDataObjects");
            workflowBlacklist = parseMapFromConfig(config, "workflowBlacklist");
            optionDelayTimeLevel = config.getInt("option_delay_time_level", 14);

            listSingleButtonGray = initButtonGray(config, "list_single_button_gray");
            listNormalButtonGray = initButtonGray(config, "list_normal_button_gray");
            webDetailButtonGray = initButtonGray(config, "web_detail_button_gray");
            buttonPostFuncActionAsyncGray = initButtonGray(config, "button_post_func_action_async_gray");

            listAsyncFillFieldInfoWaitTime = config.getInt("list_async_fill_field_info_wait_time", 20);
            fillQuoteFieldInfoWaitTime = config.getInt("fill_quote_field_info_wait_time", 10);

            importExpireTimeOffsetDate = config.getInt("import_expire_time_offset_date", 0);

            numberStepMaxValue = config.getInt("number_step_max_value", Integer.MAX_VALUE);

            importSupportSpecifiedFieldGray = getFsGrayRule(config, "import_support_specified_field_gray");

            uiEventSupportModifyMasterDetailGray = getFsGrayRule(config, "ui_event_support_modify_master_detail_gray");
            designerLayoutButton = getFsGrayRule(config, "designer_layout_button");

            logTypeObject = getFieldListMapFromConfig(config, "log_type_object");

            grayManyAbstractLayoutFilterConfig = getFsGrayRule(config, "gray_many_abstract_layout_filter_config");

            relatedListFormSupportObject = getFsGrayRule(config, "related_list_form_support_object");
            relatedListFormSupportObjectField = getFieldListMapFromConfig(config, "related_list_form_support_object_field");

            unSupportButtonInternalObject = getSetFromConfig(config, "un_support_button_internal_object");

            setUndisplayedPresetObjects(getSetFromConfig(config, "undisplayed_preset_objects"));
            treeViewSupportObject = getFsGrayRule(config, "tree_view_support_object");
            treeViewObjectNotSupportImport = config.getBool("tree_view_object_not_support_import", false);
            ES_CHECK_DUPLICATE = config.getBool("es_check_duplicate", true);
            treeViewObjectAllowMaxHierarchy = config.getInt("tree_view_object_allow_max_hierarchy", 10);
            treeViewObjectAllowMaxSaveSize = config.getInt("tree_view_object_allow_max_save_size", 500);
            treeViewObjectAllowMaxChildSize = config.getInt("treeViewObjectAllowMaxChildSize", 2000);
            maxFlowLayoutCountMap = parseMapFromConfig(config, "maxFlowLayoutCountMap");
            needDiffSystemFieldName = getFieldListMapFromConfig(config, "need_diff_system_field_name");
            unSupportFieldForModifyLog = getFieldListMapFromConfig(config, "un_support_field_for_modify_log");
            maxCountObjectDataGray = config.getLong("max_count_object_data_gray", 1_000_000L);
            maxHistoryDataCountGray = config.getLong("max_history_data_count_gray", 10_000L);

            setCommunicationOperator(getListFromConfig(config, "communication_operator"));
            setMunicipalityDirectlyUnderTheCentralGovernmentCode(getListFromConfig(config, "municipality_directly_under_the_central_government_code"));
            masterDetailEditPageDetailSingleButtonGray = getFsGrayRule(config, "masterDetailEditPageDetailSingleButtonGray");
            masterDetailEditPageDetailSingleButtonInsertGray = getFsGrayRule(config, "masterDetailEditPageDetailSingleButtonInsertGray");
            validationFunctionMergeDetailDataGray = getFsGrayRule(config, "validation_function_merge_detail_data_gray");
            maskFieldEncryptGray = getFsGrayRule(config, "mask_field_encrypt_gray");
            maskFieldEncryptObjectFieldsGray = getFsGrayRule(config, "mask_field_encrypt_object_fields_gray");
            maskFieldEncryptObjectPagesGray = getFsGrayRule(config, "mask_field_encrypt_object_pages_gray");
            validationFunctionMergeDetailIgnoreFieldsGray = getSetFromConfig(config, "validation_function_merge_detail_ignore_fields_gray");
            filterButtonsProviderGray = getFsGrayRule(config, "filter_buttons_provider_gray");

            setSyncCalculateDataDataBatchSize(config.getInt("sync_calculate_data_data_batch_size", 100));
            setSyncCalculateDataMaxQueryCount(config.getInt("sync_calculate_data_max_query_count", 10_000));

            optionSetMaxCountGray = getMaxCountGray(config, "option_set_max_count_gray");

            dateTimeSupportNotUseMultitimeZoneTenant = getSetFromConfig(config, "not_use_multitime_zone");

            presetObjectsNoLicense = getListFromConfig(config, "preset_objects_no_license");

            multiLangSupport = parseMapFromConfig(config, "multi_lang_support");

            objectMultiLangGray = getFsGrayRule(config, "object_multi_lang_gray");

            setOrganizationBlackObjectList(getListFromConfig(config, "black.multi.organization.objects"));

            closeRelatedTeamSwitchObject = getSetFromConfig(config, "close_related_team_switch_object");
            closeGlobalSearchSwitchObject = getSetFromConfig(config, "close_global_search_switch_object");
            closeFollowUpDynamicSwitchObject = getSetFromConfig(config, "close_follow_up_dynamic_switch_object");
            closeModifyRecordSwitchObject = getSetFromConfig(config, "close_modify_record_switch_object");

            childBulkCreateGray = getFieldListMapFromConfig(config, "child_bulk_create_gray");

            listNormalUiActionSupportQueryParam = initButtonGray(config, "list_normal_ui_action_support_query_param");

            webToMobileLayoutReserveInStructureComponents = getUnmodifiedListFromConfig(config, "webToMobileLayoutReserveInStructureComponents");
            webToMobileLayoutReserveInStructureComponentTypes = getUnmodifiedListFromConfig(config, "webToMobileLayoutReserveInStructureComponentTypes");
            webToMobileLayoutFilterComponents = getUnmodifiedListFromConfig(config, "webToMobileLayoutFilterComponents");
            webToMobileLayoutFilterComponentTypes = getUnmodifiedListFromConfig(config, "webToMobileLayoutFilterComponentTypes");
            setGdprUrl(config.get("gdprUrl"));
            masterRelatedObjectComponentLimit = getMaxCountGray(config, "master_related_object_component_limit");
            htmlRichTextLimit = getIntMapFromConfig(config, "html_rich_text_limit_gray");
            richTextLimit = getIntMapFromConfig(config, "rich_text_limit_gray");
            bigTextLimit = getIntMapFromConfig(config, "big_text_limit_gray");
            objectRichTextFieldMaxCount = getMaxCountGray(config, "object_rich_text_field_max_count");
            richTextObjectBlackList = getSetFromConfig(config, "rich_text_object_black_list");
            objectHtmlRichTextFieldMaxCount = getMaxCountGray(config, "object_html_rich_text_field_max_count");
            objectBigTextFieldMaxCount = getMaxCountGray(config, "object_big_text_field_max_count");
            addTeamMemberLimit = getMaxCountGray(config, "addTeamMemberLimit");
            objectLookupFieldIncreaseNum = getMaxCountGray(config, "object_lookup_field_increase_num");

            isSupportSystemTag = getSetFromConfig(config, "is_support_system_tag");

            multiDuplicateRuleAndSupportFilterBlackGrayObject = getSetFromConfig(config, "multi_duplicate_rule_and_support_filter_black_gray_object");
            multiDuplicateRuleAndSupportFilterGrayEi = getSetFromConfig(config, "multi_duplicate_rule_and_support_filter_gray_ei");

            executeButtonBeforeValidateBlackObj = getSetFromConfig(config, "execute_button_before_validate_black_obj");

            setMultiDuplicateCountLimit(config.getInt("multi_duplicate_count_limit", 5));

            manageGroupGrayEi = getSetFromConfig(config, "manage_group_gray_ei");

            printTemplateDateTimeUseUserTimeZoneEi = getSetFromConfig(config, "print_template_date_time_use_user_time_zone_ei");

            socialObjGray = getSetFromConfig(config, "social_obj_gray");

            lookupTreeViewObjectNotDisplayWholePathGrayEi = getFsGrayRule(config, "lookup_tree_view_object_not_display_whole_path_gray_ei");

            simpleDescribeGrayEi = getFsGrayRule(config, "simple_describe_gray_ei");
            simpleDescribeFieldAttrWhiteList = getSetFromConfig(config, "simple_describe_field_attr_white_list");
            duplicateSearchSupportIgnoreUpperAndLower = parseMapFromConfig(config, "duplicate_search_support_ignore_upper_and_lower");

            paramsIdempotentGrayRules = getFsGrayRule(config, "paramsIdempotentGrayRules");
            whatListDescribeCacheGray = getFsGrayRule(config, "what_list_describe_cache_gray");

            transMultiLanguageFilterComponent = getSetFromConfig(config, "trans_multi_language_filter_component");
            transMultiLanguageFilterComponentV2 = getFsGrayRule(config, "trans_multi_language_filter_component_v2");

            listLayoutSupportTransComponentGray = getFsGrayRule(config, "list_layout_support_trans_component_gray");

            layoutUnincludeFlowComponent = getSetFromConfig(config, "layout_uninclude_flow_component", CONFIG_SPLITTER3);

            buttonAsyncCallBack = getFsGrayRule(config, "button_async_call_back");
            calculateCountWhenFindDataGrayRules = getFsGrayRule(config, "calculateCountWhenFindDataGrayRules");
            setOuterOrganizationMaxPageSize(config.getInt("outer_organization_max_page_size", 4));

            detailObjectByPreobjSizeLimit = parseMapFromConfig(config, "detail_object_by_preobj_size_limit");
            // 显示“是否可复制”配置（复制按钮返回初始数据结果）的企业
            fieldEnableCloneEiGrayRule = new GrayRule(config.get("field_enable_clone_ei_gray", "deny"));
            // 显示“是否可复制”配置（复制按钮返回初始数据结果）的字段类型
            fieldEnableCloneFieldTypeGray = getSetFromConfig(config, "field_enable_clone_field_type_gray");
            // 控制对象的字段显示“是否可复制”配置的灰度
            fieldEnableCloneGray = getBlack(config, "enable_clone_objectapi_and_fieldapi_gray");
            notCheckCountDetailObjects = getSetFromConfig(config, "notCheckCountDetailObjects");
            convertRuleSupportObjects = getSetFromConfig(config, "convert_rule_support_objects");
            convertRuleBlackObjects = getSetFromConfig(config, "convert_rule_black_objects", CONFIG_SPLITTER3);
            oldLayoutConvertV3AddComponents = getSetFromConfig(config, "old_layout_convert_v3_add_components");
            editDraftObjectGray = getFsGrayRule(config, "edit_draft_object_gray");
            notCheckDetailObjectPrivilegeMappingRules = getSetFromConfig(config, "notCheckDetailObjectPrivilegeMappingRules");
            SKIP_RECORD_REF_BY_TARGET_OBJ_GRAY = getSetFromConfig(config, SKIP_RECORD_REF_BY_TARGET_OBJ_GRAY_KEY);
            unSupportFieldTypesIfOutUserGrayRules = getFsGrayRule(config, "unSupportFieldTypesIfOutUserGrayRules");
            brushRouterInfoList = parseFromConfig(config, "brush_router_info_list", RouterInfo.class);
            initToolsSysdbRouteTenantIdMap = brushRouterInfoList.stream().collect(Collectors.toMap(RouterInfo::getEnv, RouterInfo::getRouterTenantId));
            UI_EVENT_DO_CALCULATE_DETAILS_LAST_TIME_GRAY = config.getLong("ui_event_do_calculate_details_last_time_gray", 1714492800000L);
            whatListObjectSortList = getListFromConfig(config, "what_list_object_sort_list");
            noSupportCurrentLoginUserObjects = getSetFromConfig(config, "no_support_current_login_user_objects");
            AI_CONFIG_INFO = parseAiConfigInfo(config, "AI_CONFIG_INFO");
            PACKAGE_PLUGIN_CONFIG_INFO = parseAiConfigInfo(config, "PACKAGE_PLUGIN_CONFIG_INFO");
            splitOrderMaxNumLimit = getMaxCountGray(config, "split_order_max_num_limit");
            ONLINE_DOC_CONFIG_INFO = parseAiConfigInfo(config, "ONLINE_DOC_CONFIG_INFO");
            ignoreFilterValidateFields = getSetFromConfig(config, "ignore_filter_validate_fields");
            validateIsIndexFields = parseMapFromConfig(config, "validate_is_index_fields");
            searchDataBlackObject = getSetFromConfig(config, "searchDataBlackObject");
            fieldGrayConfig = initGray(config, "field_gray_config");
            changeDetailOwnerAsync = getFieldListMapFromConfig(config, "changeDetailOwnerAsync");

            sidebarLayoutTemplate = parseMapFromConfig(config, "sidebarLayoutTemplate");
            maxFilterNumWhenSearchData = config.getInt("maxFilterNumWhenSearchData", 200);
            maxFieldValueNumWhenSearchData = config.getInt("maxFieldValueNumWhenSearchData", 1000);
            setButtonClickModifyRecordGrayEis(getSetFromConfig(config, "buttonClickModifyRecordGrayEis"));
            notSupportObjectsInMobileDraft = getListFromConfig(config, "not_support_objects_in_mobile_draft");
            asyncTaskMaxDayQueryLimit = getMaxCountGray(config, "async_task_max_day_query_limit");
            funcCheckWhiteApiNameList = getSetFromConfig(config, "funcCheckWhiteApiNameList");

            maxBodySize = config.getLong("maxBodySize", 50 * 1024 * 1024);
            LOAD_CONFIG_FAIL_NOTIFY_SESSION_ID = config.get("LOAD_CONFIG_FAIL_NOTIFY_SESSION_ID");

            ignoreCleanOptionRelation = getFieldListMapFromConfig(config, "ignore_clean_option_relation");
            notSupportEditCurrencyObjects = getSetFromConfig(config, "notSupportEditCurrencyObjects");
            outerUserUnsupportedComponentTypesGrayRules = getFsGrayRule(config, "outerUserUnsupportedComponentTypesGrayRules");
        }));

        ConfigFactory.getConfig("fs-apibus-ncrm", config -> {
            log.info("reload config fs-apibus-ncrm, content:{}", config.getString());
            importObjectModule = loadModuleAndObjects(config);
        });
    }

    /**
     * 统一的灰度规则解析方法，使用JSON格式：
     * {"AccountObj,LeadsObj":"white:74255|78057"}
     */
    public static Map<String, GrayRule> getFsGrayRule(IConfig config, String confKey) {
        Map<String, GrayRule> result = Maps.newHashMap();
        Map<String, String> stringObjectMap = parseMapFromConfig(config, confKey);
        if (CollectionUtils.empty(stringObjectMap)) {
            return Collections.emptyMap();
        }
        stringObjectMap.forEach((key, val) -> {
            GrayRule grayRule = new GrayRule(val);
            CONFIG_SPLITTER.split(key).forEach(it -> result.putIfAbsent(it, grayRule));
        });
        return result;
    }

    public static Map<String, GrayRule> getObjectAndTenantGrayRule(IConfig config, String confKey) {
        Map<String, GrayRule> result = Maps.newHashMap();
        Map<String, String> originalMap = getOriginalMapFromConfig(config, confKey);
        if (CollectionUtils.empty(originalMap)) {
            return Collections.emptyMap();
        }
        originalMap.forEach((key, val) -> {
            GrayRule grayRule = new GrayRule(val);
            CONFIG_SPLITTER.split(key).forEach(it -> result.putIfAbsent(it, grayRule));
        });
        return result;
    }

    /**
     * [{"ei":"white:74255|78057","describeApiName":"SalesOrderObj","buttonApiName":"PayInstantly_button_default,ConfirmReceipt2_button_default,BugAgain_button_default"}]
     *
     * @return {describeApiName: [GrayRuleHelper<rule, [buttonApiNames]>]
     */
    private static Map<String, List<GrayRuleHelper<Set<String>>>> initButtonGray(IConfig config, String key) {
        Map<String, List<GrayRuleHelper<Set<String>>>> map = Maps.newHashMap();
        String listSingleButtonGray = config.get(key);
        if (Strings.isNullOrEmpty(listSingleButtonGray)) {
            return Maps.newHashMap();
        }
        try {
            List<Map<String, String>> parse = (List<Map<String, String>>) JSONArray.parse(listSingleButtonGray);
            for (Map<String, String> objectMap : parse) {
                String describeApiName = objectMap.get("describeApiName");
                Set<String> buttons = Sets.newHashSet(CONFIG_SPLITTER.split(objectMap.get("buttonApiName")));
                GrayRuleHelper<Set<String>> grayRuleHelper = GrayRuleHelper.of(objectMap.get("ei"), buttons);
                map.computeIfAbsent(describeApiName, it -> Lists.newArrayList()).add(grayRuleHelper);
            }
            return map;
        } catch (Exception e) {
            log.error("initButtonGray failed,config:{},key:{},data:{}", config.getName(), key, listSingleButtonGray, e);
            return map;
        }
    }

    /**
     * [{"ei":"white:74255|78057","fieldApiName":"big_file_attachment","describeApiNames":"AllFieldType,ServiceLogObj",
     * "bizValue":"file_attachment"}]
     */
    private static Map<String, GrayRuleHelper<Set<String>>> initGray(IConfig config, String key) {
        Map<String, GrayRuleHelper<Set<String>>> map = Maps.newHashMap();
        String grayConfig = config.get(key);
        if (Strings.isNullOrEmpty(grayConfig)) {
            return Maps.newHashMap();
        }
        try {
            List<Map<String, String>> parse = (List<Map<String, String>>) JSONArray.parse(grayConfig);
            for (Map<String, String> objectMap : parse) {
                String fieldApiName = objectMap.get("fieldApiName");
                Set<String> describeApiNames = Sets.newHashSet(CONFIG_SPLITTER.split(objectMap.get("describeApiNames")));
                String bizValue = objectMap.get("bizValue");
                map.put(fieldApiName, GrayRuleHelper.of(objectMap.get("ei"), describeApiNames, bizValue));
            }
            return map;
        } catch (Exception e) {
            log.error("initButtonGray failed,config:{},key:{},data:{}", config.getName(), key, grayConfig, e);
            return map;
        }
    }

    private static Map<String, Set<String>> loadModuleAndObjects(IConfig config) {
        try {
            JsonNode jsonNode = JacksonUtils.readTree(config.getString());
            if (jsonNode == null) {
                return Collections.emptyMap();
            }
            JsonNode applications = jsonNode.get("applications");
            JsonNode anImport = applications.get("IMPORT");
            Map<String, Set<String>> result = Maps.newHashMap();
            anImport.fields().forEachRemaining(it -> {
                String moduleName = it.getKey();
                JsonNode node = it.getValue();
                List<String> objectAPINames = node.findValuesAsText("objectAPINames");
                Set<String> objectNames = CollectionUtils.nullToEmpty(objectAPINames).stream()
                        .map(CONFIG_SPLITTER::splitToList)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                result.put(moduleName, Sets.newHashSet(objectNames));
            });
            return result;
        } catch (Exception e) {
            log.error("loadModuleAndObjects failed,config:{}", config.getName(), e);
            return Collections.emptyMap();
        }
    }

    private static Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static Set<String> getSetFromConfig(IConfig config, String key, String defaultValueSet) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, defaultValueSet)));
    }

    private static Set<String> getSetFromConfig(IConfig config, String key, Splitter splitter) {
        return Sets.newHashSet(splitter.split(config.get(key, "")));
    }

    private static List<String> getListFromConfig(IConfig config, String key) {
        return Lists.newArrayList(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static List<String> getUnmodifiedListFromConfig(IConfig config, String key) {
        return Collections.unmodifiableList(getListFromConfig(config, key));
    }

    private static List<Map<String, String>> getListMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Lists.newArrayList();
        }
        try {
            return (List<Map<String, String>>) JSONArray.parse(data);
        } catch (Exception e) {
            log.error("getListMapFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Lists.newArrayList();
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
        try {
            Map map = JSON.parseObject(data, Map.class);
            return Collections.unmodifiableMap(map);
        } catch (Exception e) {
            log.error("parseMapFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
    }

    private static <T> List<T> parseFromConfig(IConfig config, String key, Class<T> clazz) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Collections.unmodifiableList(Lists.newArrayList());
        }
        try {
            List<T> list = JSON.parseArray(data, clazz);
            return Collections.unmodifiableList(list);
        } catch (Exception e) {
            log.error("parseFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Collections.unmodifiableList(Lists.newArrayList());
        }
    }

    private static Map<String, String> getOriginalMapFromConfig(IConfig config, String key) {
        String configStr = config.get(key);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        return CONFIG_SPLITTER2.splitToList(configStr).stream()
                .map(it -> Tuple.of(StringUtils.substringBefore(it, ":"), StringUtils.substringAfter(it, ":")))
                .collect(Collectors.toMap(Tuple::getKey, Tuple::getValue, (x, y) -> x));
    }

    private static Map<String, List<String>> getFieldListMapFromConfig(IConfig config, String key) {
        String configStr = config.get(key);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        return CONFIG_SPLITTER2.splitToList(configStr).stream()
                .map(it -> Tuple.of(StringUtils.substringBefore(it, ":"), StringUtils.substringAfter(it, ":")))
                .collect(Collectors.toMap(Tuple::getKey, it -> CONFIG_SPLITTER.splitToList(it.getValue()), (x, y) -> x));
    }


    private static Map<String, Integer> getIntMapFromConfig(IConfig config, String key) {
        String configStr = config.get(key);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        return CONFIG_SPLITTER2.splitToList(configStr).stream()
                .map(it -> Tuple.of(StringUtils.substringBefore(it, ":"), Integer.parseInt(StringUtils.substringAfter(it, ":"))))
                .collect(Collectors.toMap(Tuple::getKey, Tuple::getValue, (x, y) -> x));
    }

    private static Map<String, Integer> getMaxCountGray(IConfig config, String confKey) {
        String configStr = config.get(confKey);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        Map<String, Integer> resultMap = Maps.newHashMap();
        for (String str : CONFIG_SPLITTER2.split(configStr)) {
            String key = StringUtils.substringBefore(str, ":");
            String value = StringUtils.substringAfter(str, ":");
            Integer num = Integer.valueOf(key);
            for (String tenantId : CONFIG_SPLITTER.split(value)) {
                resultMap.put(tenantId, num);
            }
        }
        return resultMap;
    }

    /**
     * 解析AI配置项
     */
    private static Map<String, Object> parseAiConfigInfo(IConfig config, String key) {
        String configStr = config.get(key);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        try {
            return JSON.parseObject(configStr);
        } catch (Exception e) {
            log.error("parseAiConfigInfo error key:{}, value:{}", key, configStr, e);
        }
        return Maps.newHashMap();
    }

    public static boolean isUniqueRuleRedisGrayTenant(String tenant) {
        if (uniqueRuleRedisGrayTenant.contains("All")) {
            return true;
        }

        return uniqueRuleRedisGrayTenant.contains(tenant);
    }

    /**
     * 判断预设对象是否支持终端缓存
     */
    public static boolean isPackageObjectSupportDescribeCache(String tenantId, String objectApiName) {
        if (CollectionUtils.empty(packageObjectListWhichSupportDescribeCache) || CollectionUtils.empty(packageObjectListWhichSupportDescribeCacheTenants)) {
            return false;
        }
        return (packageObjectListWhichSupportDescribeCache.contains(ALL) || packageObjectListWhichSupportDescribeCache.contains(objectApiName))
                && (packageObjectListWhichSupportDescribeCacheTenants.contains(ALL) || packageObjectListWhichSupportDescribeCacheTenants.contains(tenantId));
    }

    /**
     * 判断企业id是否支持数据版本号的校验
     */
    public static boolean isSupportDataVersionCheck(String tenantId) {
        //670以前的终端不做版本号校验，如果有数据覆盖问题，引导用户升级版本
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_670)) {
            return false;
        }
        if (CollectionUtils.empty(tenantIdListWhichSupportDataVersionCheck)) {
            return false;
        }
        return tenantIdListWhichSupportDataVersionCheck.contains(ALL)
                || tenantIdListWhichSupportDataVersionCheck.contains(tenantId);
    }

    /**
     * 判断企业id是否在主从审批的白名单中
     */
    public static boolean isInMasterDetailApprovalWhiteList(String tenantId) {
        if (CollectionUtils.empty(masterDetailApprovalWhiteList)) {
            return false;
        }
        return masterDetailApprovalWhiteList.contains(tenantId);
    }

    public static Map<String, Map<String, String>> getLangFieldAlginConfigMap() {
        return CollectionUtils.nullToEmpty(fieldAlignDefaultConfigs).stream().collect(Collectors.toMap(x -> x.get("lang"),
                it -> it, (x1, x2) -> x1));
    }

    /**
     * 判断企业id是否在主从审批的灰度名单中
     */
    public static boolean isInMasterDetailApprovalGrayList(String tenantId) {
        if (isInMasterDetailApprovalWhiteList(tenantId)) {
            return false;
        }
        if (CollectionUtils.empty(masterDetailApprovalGrayList)) {
            return false;
        }
        return masterDetailApprovalGrayList.contains(ALL)
                || masterDetailApprovalGrayList.contains(tenantId);
    }

    public static boolean isChangeOwnerDetailAsync(String tenantId, String describeApiName) {
        if (StringUtils.isAnyBlank(tenantId, describeApiName)) {
            return false;
        }
        Map<String, List<String>> detailOwnerAsync = AppFrameworkConfig.getChangeDetailOwnerAsync();
        if (CollectionUtils.notEmpty(detailOwnerAsync)) {
            return CollectionUtils.nullToEmpty(detailOwnerAsync.get(tenantId)).stream()
                    .filter(StringUtils::isNotBlank)
                    .anyMatch(x -> Objects.equals(x, describeApiName));
        }
        return false;
    }

    public static boolean isTeamMemberRoleBlackField(IFieldDescribe referenceFieldDescribe) {
        String targetApiName = null;
        if (referenceFieldDescribe instanceof ObjectReferenceManyFieldDescribe) {
            targetApiName = ((ObjectReferenceManyFieldDescribe) referenceFieldDescribe).getTargetApiName();
        } else if (referenceFieldDescribe instanceof ObjectReferenceFieldDescribe) {
            targetApiName = ((ObjectReferenceFieldDescribe) referenceFieldDescribe).getTargetApiName();
        }
        List<String> fields = teamMemberRoleBlackField.get(targetApiName);
        if (CollectionUtils.empty(fields)) {
            return false;
        }
        return fields.contains(String.format("%s.%s", referenceFieldDescribe.getDescribeApiName(), referenceFieldDescribe.getApiName()));
    }

    public static boolean isAuditLogIgnoreField(String objectApiName, String fieldApiName) {
        List<String> fields = ignoreAuditLogFields.get(objectApiName);
        if (CollectionUtils.empty(fields)) {
            return false;
        }
        return fields.contains(fieldApiName);
    }

    public static boolean isSkipValidateLookupGrayTenant(String tenantId) {
        if (skipValidateLookupGrayTenant.contains(ALL)) {
            return true;
        }
        return skipValidateLookupGrayTenant.contains(tenantId);
    }

    public static boolean isMultiField(String fieldName) {
        return multiFieldName.contains(fieldName);
    }

    public static boolean isModifyRelatedTeamDataPermissionWhiteTenant(String tenantId) {
        if (relatedTeamDataPermissionModifyWhiteTenants.contains(ALL)) {
            return true;
        }
        return relatedTeamDataPermissionModifyWhiteTenants.contains(tenantId);
    }

    public static boolean isUpdateIgnoreOtherGrayApiName(String objectApiName) {
        return updateIgnoreOtherGrayApiName.contains(objectApiName);
    }

    public static List<String> getLayoutSpecialRelatedObjects(String tenantId, String objectApiName) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(layoutSpecialRelatedObjectSetting) && layoutSpecialRelatedObjectSetting.containsKey(tenantId)) {
            if (layoutSpecialRelatedObjectSetting.get(tenantId).containsKey(objectApiName)) {
                return layoutSpecialRelatedObjectSetting.get(tenantId).get(objectApiName);
            }
        }
        return result;
    }

    public static FrameComponent getLayoutSpecialFrameObjects(String tenantId, String objectApiName) {
        if (CollectionUtils.notEmpty(layoutSpecialFrameSetting) && layoutSpecialFrameSetting.containsKey(tenantId)) {
            if (layoutSpecialFrameSetting.get(tenantId).containsKey(objectApiName)) {
                return JSON.parseObject(layoutSpecialFrameSetting.get(tenantId).get(objectApiName).toString(), FrameComponent.class);
            }
        }
        return null;
    }

    public static boolean isUpdateImportByIdGrayTenant(String describeApiName, String tenantId) {
        List<String> tenantIdList = updateImportByIdGrayTenant.getOrDefault(describeApiName, Collections.emptyList());
        if (tenantIdList.contains(ALL)) {
            return true;
        }
        return tenantIdList.contains(tenantId);
    }

    //判断指定字段是否需要下发原值
    public static boolean isSpecialMaskField(String objectApiName, String fieldApiName) {
        String tenantId = Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getTenantId)
                .orElse(null);
        if (maskFieldEncryptGray(tenantId, objectApiName)) {
            return false;
        }
        if (CollectionUtils.empty(specialMaskFieldMap)) {
            return false;
        }
        return specialMaskFieldMap.getOrDefault(objectApiName, Collections.emptyList()).contains(fieldApiName);
    }

    public static boolean isIndustryEnterInfoGrayTenantId(String tenantId) {
        if (industryEnterInfoGrayTenantId.contains(ALL)) {
            return true;
        }

        return industryEnterInfoGrayTenantId.contains(tenantId);
    }

    public static boolean isRelatedListFunctionGrayTenantId(String tenantId) {
        if (relatedListFunctionTenant.contains(ALL)) {
            return true;
        }

        return relatedListFunctionTenant.contains(tenantId);
    }

    public static boolean isShowCustomButtonFromLock(String tenantId) {
        if (showCustomButtonInLockGrayTenant.contains(ALL)) {
            return true;
        }
        return showCustomButtonInLockGrayTenant.contains(tenantId);
    }

    public static Map<String, Integer> getRelatedListFunctionGrayCountMap() {
        return relatedListFunctionCount;
    }

    public static boolean isGrayListSearchObject(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            return true;
        }
        if (listSearchGrayObject.contains(objectApiName) || listSearchGrayObject.contains(ALL)) {
            return true;
        }
        List<String> tenantIds = listSearchSupportOr.get(objectApiName);
        if (CollectionUtils.empty(tenantIds)) {
            return false;
        }
        return tenantIds.contains(ALL) || tenantIds.contains(tenantId);
    }

    public static boolean isChangeOwnerSupportClearObject(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            return true;
        }
        if (changeOwnerClearGrayObject.contains(objectApiName) || changeOwnerClearGrayObject.contains(ALL)) {
            return true;
        }
        return graySupported(changeOwnerClearGray, false, objectApiName, tenantId);
    }

    public static boolean isSupportExportExcelPage(String tenantId, String objectApiName) {
        return graySupported(exportExcelPageGray, false, objectApiName, tenantId);
    }

    public static boolean isCustomObject(String objectApiName) {
        return UdobjGrayConfig.isCustomObject(objectApiName);
    }

    public static boolean isSaleLogHighOrderObject(String objectApiName) {
        return saleLogHighOrderObjects.contains(ALL) || saleLogHighOrderObjects.contains(objectApiName);
    }

    public static boolean isImportApprovalFlowTenantGray(String tenantId) {
        return importApprovalFlowTenantGray.contains(ALL) || importApprovalFlowTenantGray.contains(tenantId);
    }

    public static boolean isImportTriggerApprovalFlowGray(String tenantId, String describeApiName) {
        if (isCustomObject(describeApiName)) {
            describeApiName = UDOBJ;
        }
        GrayRule grayRule = importTriggerApprovalFlowGray.get(describeApiName);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isUnionInsertImportTriggerApprovalFlowGray(String tenantId, String objectCode) {
        if (isCustomObject(objectCode)) {
            objectCode = UDOBJ;
        }
        GrayRule grayRule = unionInsertImportTriggerApprovalFlowGray.get(objectCode);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isGoalValueObjectImportGray(String tenantId) {
        return goalValueObjectImportGray.contains(ALL) || goalValueObjectImportGray.contains(tenantId);
    }

    public static boolean isSupportSaveDraftButton(String tenantId, String objectApiName) {
        return !dataDraftTenantBlackList.contains(tenantId) && !dataDraftObjectBlackList.contains(objectApiName);
    }

    public static boolean isSupportEditDraftButton(String tenantId, String objectApiName) {
        return graySupported(editDraftObjectGray, false, objectApiName, tenantId);
    }

    public static List<Map<String, String>> getRelatedListNaviURL(String tenantId) {
        return relatedListNaviUrl.getOrDefault(tenantId, Lists.newArrayList());
    }

    public static List<Map<String, String>> getUIActionNaviURL(String tenantId) {
        return uiActionNaviUrl.getOrDefault(tenantId, Lists.newArrayList());
    }

    public static boolean isMobileListBlackObject(String objectApiName) {
        return mobileListBlackObjectList.contains(objectApiName);
    }

    public static boolean isAddEditUIActionGray(String tenantId, String objectApiName) {
        if (isAddEditUIActionGrayTenant(tenantId)) {
            if (isCustomObject(objectApiName)) {
                return true;
            }
            return addEditUiActionGrayObject.contains(objectApiName)
                    || addEditUiActionGrayObject735.contains(objectApiName)
                    || isGrayAddEditUiActionObjectEi(tenantId, objectApiName);
        }
        return false;
    }

    public static boolean isEditUIButtonDisplayConditions(String tenantId, String objectApiName) {
        if (!isAddEditUIActionGray(tenantId, objectApiName)) {
            return false;
        }
        return editUiButtonDisplayConditions.contains(ALL) || editUiButtonDisplayConditions.contains(tenantId);
    }

    private static boolean isGrayAddEditUiActionObjectEi(String tenantId, String objectApiName) {
        List<String> tenants = CollectionUtils.nullToEmpty(addEditUiActionGrayObjectEi.get(objectApiName));
        return tenants.contains(ALL) || tenants.contains(tenantId);
    }

    public static boolean isAddEditUIActionGrayTenant(String tenantId) {
        return addEditUiActionGrayTenant.contains(ALL) || addEditUiActionGrayTenant.contains(tenantId);
    }

    public static boolean isShowingOutTeamMemberGray(String tenantId) {
        return showingOutTeamMemberGrayTenant.contains(ALL) || showingOutTeamMemberGrayTenant.contains(tenantId);
    }

    public static boolean isSupportSaveAndCreate(String tenantId) {
        if (supportSaveAndCreateBlackTenant.contains(ALL)) {
            return false;
        }
        return !supportSaveAndCreateBlackTenant.contains(tenantId);
    }

    public static boolean isDuplicateDataGrayTenant(String tenantId) {
        return duplicateDataGrayTenant.contains(ALL) || duplicateDataGrayTenant.contains(tenantId);
    }

    public static boolean isHideSaleLogInV1Layout(String objectApiName) {
        return hideSaleLogInV1LayoutObjects.contains(objectApiName);
    }

    public static boolean isTeamMemberDisableSelectAll(String tenantId) {
        return teamMemberDisableSelectAllTenant.contains(ALL) || teamMemberDisableSelectAllTenant.contains(tenantId);
    }

    public static boolean isSupportAccountOperationMap(String tenantId) {
        return supportAccountOperationMapTenants.contains(ALL) || supportAccountOperationMapTenants.contains(tenantId);
    }

    public static boolean isGrayListLayout(String tenantId, String objectApiName) {
        return (Objects.nonNull(LIST_LAYOUT_GRAY_RULE) && LIST_LAYOUT_GRAY_RULE.isAllow(objectApiName))
                || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.LIST_LAYOUT_GRAY + "__" + objectApiName, tenantId);
    }

    public static boolean isListLayoutSupportTransComponent(String type) {
        return listLayoutSupportTransComponent.contains(type);
    }

    public static boolean isListLayoutSupportTransComponent(String tenantId, String type) {
        if (StringUtils.isBlank(type)) {
            return false;
        }
        GrayRule grayRule = listLayoutSupportTransComponentGray.get(type);
        if (Objects.nonNull(grayRule) && StringUtils.isNotEmpty(tenantId)) {
            return grayRule.isAllow(tenantId);
        }

        return isListLayoutSupportTransComponent(type);
    }

    private static boolean isGrayListLayout750(String tenantId, String objectApiName) {
        return listLayoutGrayEi750.contains(tenantId) && listLayoutGrayObject750.contains(objectApiName);
    }

    public static boolean disableDuplicateSearchLock(String tenantId) {
        return disableDuplicateSearchLockEi.contains(ALL) || disableDuplicateSearchLockEi.contains(tenantId);
    }

    public static boolean isPreciseDuplicateSearchGray(String tenantId, String objectApiName) {
        if (preciseDuplicateSearchGrayEi.contains(ALL) || preciseDuplicateSearchGrayEi.contains(tenantId)) {
            return true;
        }
        return preciseDuplicateSearchGray.getOrDefault(tenantId, Collections.emptyList()).contains(objectApiName);
    }

    public static Map<String, Integer> getBodySizeLimitMap() {
        return bodySizeLimit;
    }

    public static boolean isGrayBodySizeLimit(String tenantId) {
        return bodySizeLimitGrayEi.contains(ALL) || bodySizeLimitGrayEi.contains(tenantId);
    }

    public static int getDuplicateLimitGray(String tenantId) {
        if (duplicateLimitGrayEi.contains(ALL) || duplicateLimitGrayEi.contains(tenantId)) {
            return duplicateLimitGray;
        }
        return 200;
    }

    public static boolean isDuplicateOuterGrayTenantId(String tenantId) {
        return duplicateOuterGrayEi.contains(ALL) || duplicateOuterGrayEi.contains(tenantId);
    }

    public static boolean isSyncFieldGrayEi(String tenantId) {
        return syncFieldGrayEi.contains(ALL) || syncFieldGrayEi.contains(tenantId);
    }

    public static int getExportFileRowsThrottle(String tenantId) {
        if (AppFrameworkConfig.getExportVipTenantIds().contains(tenantId)) {
            return exportFileRowsThrottleVip;
        }
        return exportFileRowsThrottle;
    }

    public static boolean isApplyDataPrivilegeCheck(String tenantId) {
        return applyDataPrivilegeCheckEi.contains(ALL) || applyDataPrivilegeCheckEi.contains(tenantId);
    }

    public static boolean isApplyDataPrivilegeCheckBlackDescribeApiName(String describeApiName) {
        return applyDataPrivilegeCheckBlackDescribeApiName.contains(describeApiName);
    }

    public static boolean isExportUseDbTenantId(String tenantId) {
        return exportUseDbEi.contains(ALL) || exportUseDbEi.contains(tenantId);
    }

    public static boolean isImportObjectListGray(String tenantId) {
        return importObjectListGrayEi.contains(ALL) || importObjectListGrayEi.contains(tenantId);
    }

    public static Map<String, List<String>> getSupportMultiLangObjField() {
        return supportMultiLangObjField;
    }

    public static boolean isSupportFieldEnableDataLang(String tenantId, String describeApiName, String fieldApiName) {
        if (!objectMultiLangGray(tenantId, describeApiName)) {
            return false;
        }
        List<String> fieldList = supportMultiLangObjField.get(describeApiName);
        if (CollectionUtils.empty(fieldList) && supportMultiLangObjField.containsKey(CUSTOMER) && isCustomObject(describeApiName)) {
            fieldList = getSupportMultiLangObjField().get(CUSTOMER);
        }
        return CollectionUtils.notEmpty(fieldList) && (fieldList.contains(fieldApiName));
    }

    public static List<String> getSupportMultiLangPreFieldByDescribeApiName(String describeApiName) {
        if (isCustomObject(describeApiName)) {
            return supportMultiLangObjField.get(CUSTOMER);
        } else {
            return supportMultiLangObjField.get(describeApiName);
        }
    }

    public static List<String> getImportObjectModule(String tenantId) {
        Set<String> modules = Sets.newHashSet(importObjectModule.keySet());
        modules.add(DefObjConstants.UDOBJ);
        return modules.stream()
                .filter(it -> {
                    List<String> tenantIds = importObjectModuleGray.getOrDefault(it, Collections.emptyList());
                    return tenantIds.contains(ALL) || tenantIds.contains(tenantId);
                })
                .collect(Collectors.toList());
    }

    public static String getImportModuleNameByObjectApiName(String tenantId, String objectApiName) {
        if (!isImportObjectListGray(tenantId)) {
            return DefObjConstants.UDOBJ;
        }
        List<String> objectModule = getImportObjectModule(tenantId);
        for (Map.Entry<String, Set<String>> entry : importObjectModule.entrySet()) {
            String moduleName = entry.getKey();
            if (!objectModule.contains(moduleName)) {
                continue;
            }
            Set<String> value = entry.getValue();
            if (CollectionUtils.nullToEmpty(value).contains(objectApiName)) {
                return moduleName;
            }
        }
        return DefObjConstants.UDOBJ;
    }

    public static Set<String> getObjectsByModules(String... modules) {
        if (Objects.isNull(modules)) {
            return Collections.emptySet();
        }
        return Stream.of(modules)
                .map(importObjectModule::get)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    public static List<String> getImportObjectModuleOrder() {
        return ImmutableList.copyOf(importObjectModuleOrder);
    }

    public static boolean isDetailRenderTable(String tenantId, String apiName) {
        return detailRenderTable.containsKey(tenantId) && detailRenderTable.get(tenantId).contains(apiName);
    }

    public static boolean isGraySwitchCache(String tenantId) {
        return switchCacheGrayEi.contains(ALL) || switchCacheGrayEi.contains(tenantId);
    }

    public static boolean isGrayFilterEmployeeFormula(String tenantId) {
        return grayFilterByEmployeeFormulaTenants.contains(tenantId);
    }

    public static int getMaxDetailObjectCount(String tenantId) {
        return detailObjectSizeLimit.getOrDefault(tenantId, 5);
    }

    public static boolean insertImportUnSupportField(IFieldDescribe fieldDescribe) {
        return CollectionUtils.nullToEmpty(insertImportUnsupportFieldName.get(DefObjConstants.UDOBJ)).contains(fieldDescribe.getApiName())
                || CollectionUtils.nullToEmpty(insertImportUnsupportFieldType.get(DefObjConstants.UDOBJ)).contains(fieldDescribe.getType());
    }

    public static boolean updateImportUnSupportField(IFieldDescribe fieldDescribe) {
        return CollectionUtils.nullToEmpty(updateImportUnsupportFieldName.get(DefObjConstants.UDOBJ)).contains(fieldDescribe.getApiName())
                || CollectionUtils.nullToEmpty(updateImportUnsupportFieldType.get(DefObjConstants.UDOBJ)).contains(fieldDescribe.getType());
    }

    public static List<String> getGrayAllFieldTypeList(String tenantId) {
        List<String> result = Lists.newArrayList(grayAllFieldTypeList);
        if (grayBigFileAttachmentFieldListEi.contains(ALL) || grayBigFileAttachmentFieldListEi.contains(tenantId)) {
            result.add(result.indexOf(IFieldType.FILE_ATTACHMENT) + 1, IFieldType.BIG_FILE_ATTACHMENT);
        }
        return result;
    }

    public static List<String> getBigObjectFieldTypeList() {
        return bigObjectFieldTypeList;
    }

    public static boolean isGrayFuncPermissionAPPID(String tenantId) {
        return functionPermissionAppIdGrayEi.contains(ALL) || functionPermissionAppIdGrayEi.contains(tenantId);
    }

    public static boolean needConvertQueryResultDateFieldValue(String tenantId) {
        return TimeZoneConfig.INSTANCE.isGray(tenantId) && needConvertQueryResultDateFieldValue;
    }

    public static boolean isWhatListWhatFieldGray(String tenantId) {
        return whatListWhatFieldGrayEi.contains(ALL) || whatListWhatFieldGrayEi.contains(tenantId);
    }

    public static boolean isDraftFilterAppId(String tenantId, String appId) {
        return (outDraftAppIdGrayEi.contains(ALL) || outDraftAppIdGrayEi.contains(tenantId)) && !Strings.isNullOrEmpty(appId) && !draftIgnoreAppId.contains(appId);
    }

    public static boolean isGrayCreateEditSystemButton(String tenantId) {
        return createEditSystemButtonGrayEi.contains(ALL) || createEditSystemButtonGrayEi.contains(tenantId);
    }

    public static boolean isGrayCreateEditSystemButton(String objectApiName, String buttonApiName) {
        if (createEditSystemButtonSupport.getOrDefault(ALLOBJ, Collections.emptyList()).contains(buttonApiName)) {
            return true;
        }
        return createEditSystemButtonSupport.getOrDefault(objectApiName, Collections.emptyList()).contains(buttonApiName);
    }

    public static boolean supportMoreFields(String tenantId) {
        return supportMoreFieldsEI.contains(tenantId);
    }

    public static boolean calculateInImportEI(String tenantId) {
        return calculateInImportEI.contains(ALL) || calculateInImportEI.contains(tenantId);
    }

    public static boolean isSupportNamespace(String namespace) {
        return functionNamespaceValidator.containsKey(namespace);
    }

    public static boolean isSupportReturnType(String namespace, String returnTye) {
        return functionNamespaceValidator.get(namespace).contains(returnTye);
    }

    public static boolean isCalculateSkipVersionChange(String tenantId) {
        return calculateSkipVersionChangeEI.contains(tenantId) || calculateSkipVersionChangeEI.contains(ALL);
    }

    public static boolean validateLookupSearchDataInDb() {
        return validateLookupSearchDataInDb;
    }

    public static boolean isGrayDataPrivilegeProviderSimple(String tenantId) {
        return dataPrivilegeProviderSimpleGrayEi.contains(ALL) || dataPrivilegeProviderSimpleGrayEi.contains(tenantId);
    }

    public static boolean mobileSupportUiPaasButtonGrayEi(String tenantId) {
        return mobileSupportUiPaasButtonGrayEi.contains(ALL) || mobileSupportUiPaasButtonGrayEi.contains(tenantId);
    }

    public static boolean isGrayDataTypeValidate(String tenantId) {
        return UdobjGrayConfig.isAllow("data_type_validate_ei", tenantId) || TenantUtil.isNewTenant(tenantId);
    }

    public static boolean isIgnoreDataTypeValidate(String objectApiName, String fieldApiName) {
        if (!dataTypeValidateIgnore.containsKey(objectApiName)) {
            return false;
        }
        List<String> fields = dataTypeValidateIgnore.getOrDefault(objectApiName, Lists.newArrayList());
        return fields.contains(fieldApiName) || fields.contains(ALL);
    }

    public static boolean isGrayWhatListIsAllowDeleteAllMembers(String tenantId, String objectApiName) {
        if (whatListIsAllowDeleteAllMembersGrayEi.contains(ALL) || whatListIsAllowDeleteAllMembersGrayEi.contains(tenantId)) {
            return whatListIsAllowDeleteAllMembersGrayApiName.contains(objectApiName);
        }
        return false;
    }

    public static boolean isGrayRemoveDuplicatedValue(String tenantId) {
        return removeDuplicatedValueGrayEi.contains(ALL) || removeDuplicatedValueGrayEi.contains(tenantId);
    }

    public static boolean grayRelationOuterDataPrivilegeTenant(String tenantId) {
        return grayRelationOuterDataPrivilegeTenant.contains(ALL) || grayRelationOuterDataPrivilegeTenant.contains(tenantId);
    }

    public static boolean isSupportBatchPrintExport(String tenantId, String objectApiName) {
        if (batchPrintExportGrayEi.contains(ALL) || batchPrintExportGrayEi.contains(tenantId)) {
            if (Objects.nonNull(objectApiName) && objectApiName.endsWith("__c")) {
                return batchPrintExportObjectList.contains(UDOBJ);
            }
            return batchPrintExportObjectList.contains(objectApiName);
        }
        return false;
    }

    public static boolean isGrayWhatListDescribeCache(String tenantId, String objectApiName) {
        return graySupported(whatListDescribeCacheGray, false, objectApiName, tenantId);
    }

    public static boolean isGrayExportAccordingToTemplateFieldList(String tenantId) {
        return exportAccordingToTemplateFieldListGrayEi.contains(ALL) || exportAccordingToTemplateFieldListGrayEi.contains(tenantId);
    }

    public static boolean isIncrementUpdateInEditAction(String tenantId, String objectApiName) {
        //自定义对象
        if (isCustomObject(objectApiName)) {
            return incrementUpdateInEditActionTenant.contains(ALL) || incrementUpdateInEditActionTenant.contains(tenantId);
        }
        //预设对象
        List<String> predefineTenants = incrementUpdateInEditActionObjects.getOrDefault(objectApiName, Collections.emptyList());
        return predefineTenants.contains(ALL) || predefineTenants.contains(tenantId);
    }

    public static boolean isGrayMobileCardRenderType(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            return true;
        }
        //独立灰度的预设对象
        if (grayMobileCardRenderTypeObjects.containsKey(objectApiName)) {
            List<String> grayTenants = grayMobileCardRenderTypeObjects.getOrDefault(objectApiName, Collections.emptyList());
            return grayTenants.contains(ALL) || grayTenants.contains(tenantId);
        }
        //已经全网的预设对象
        if (grayMobileCardRenderTypePredefineObjects.contains(objectApiName)) {
            return true;
        }
        //一起灰度的预设对象
        return commonGrayMobileCardRenderTypeTenant.contains(ALL) || commonGrayMobileCardRenderTypeTenant.contains(tenantId);
    }

    // 已经全网择机删除
    public static boolean grayWXMiniProgramEi(String tenantId) {
        return wxMiniProgramEi.contains(ALL) || wxMiniProgramEi.contains(tenantId);
    }

    public static boolean isGrayLayoutDesignerButtonManagerObject(String objectApiName) {
        return layoutDesignerButtonManagerGray.contains(ALL) || layoutDesignerButtonManagerGray.contains(objectApiName);
    }

    public static boolean isGrayRecoverButton(String tenantId, String apiName) {
        if (grayRecoverButtonEi.contains(ALL) || grayRecoverButtonEi.contains(tenantId)) {
            if (isCustomObject(apiName)) {
                return true;
            }
            return grayRecoverButtonObject.contains(ALL) || grayRecoverButtonObject.contains(apiName);
        }
        return false;
    }

    public static boolean isSupportDisplayNameField(String objectApiName) {
        return isCustomObject(objectApiName) || supportDisplayNameFieldObject.contains(ALL) || supportDisplayNameFieldObject.contains(objectApiName);
    }

    public static boolean isSupportDisplayNameFieldEnterprise(String tenantId) {
        return primaryAttributeSupportDisplayNameGrayEi.contains(ALL) || primaryAttributeSupportDisplayNameGrayEi.contains(tenantId);
    }

    public static boolean isGrayFlowTaskPassTemplateDataRightTenantId(String tenantId) {
        return flowTaskPassTemplateDataRightEi.contains(ALL) || flowTaskPassTemplateDataRightEi.contains(tenantId);
    }

    public static boolean isGrayEsPaginationOptimization(String tenantId) {
        return esPaginationOptimization;
    }

    public static int getMaxWhatListDataCount(String tenantId) {
        return whatListRelateDataMaxCount.getOrDefault(tenantId, 20);
    }

    public static Set<String> getMappingRuleWhiteObjects() {
        return mappingRuleWhiteObj;
    }

    public static boolean isDisplayNameSupportWhatListFieldBlackListObject(String objectApiName) {
        return (isCustomObject(objectApiName) && displayNameSupportWhatListFieldBlackListObjects.contains(DefObjConstants.UDOBJ)) || displayNameSupportWhatListFieldBlackListObjects.contains(objectApiName);
    }

    public static boolean isStartAddApprovalWithData(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName) || Utils.SALES_ORDER_API_NAME.equals(objectApiName)) {
            return true;
        }
        List<String> grayTenants = startAddApprovalWithDataObjects.getOrDefault(objectApiName, Collections.emptyList());
        return grayTenants.contains(ALL) || grayTenants.contains(tenantId);
    }

    public static boolean isInWorkflowBlacklist(String tenantId, String objectApiName) {
        if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(objectApiName) || CollectionUtils.empty(workflowBlacklist)) {
            return false;
        }
        return workflowBlacklist.getOrDefault(tenantId, Collections.emptyList()).contains(objectApiName);
    }

    public static Set<String> listSingleButtonGray(String tenantId, String describeApiName) {
        if (CollectionUtils.empty(listSingleButtonGray)) {
            return Sets.newHashSet();
        }
        Set<String> buttonApiNameSet = getAllObjectSingleButtonGray(tenantId);
        List<GrayRuleHelper<Set<String>>> grayRuleHelpers = listSingleButtonGray.get(describeApiName);
        if (CollectionUtils.empty(grayRuleHelpers)) {
            return buttonApiNameSet;
        }
        Set<String> buttonApiNames = grayRuleHelpers.stream()
                .map(it -> it.getGrayItem(tenantId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        buttonApiNameSet.addAll(buttonApiNames);
        return buttonApiNameSet;
    }

    public static Set<String> getAllObjectSingleButtonGray(String tenantId) {
        return getAllObjectButtonGray(listSingleButtonGray, tenantId);
    }

    public static Set<String> getAllObjectWebDetailButtonGray(String tenantId) {
        return getAllObjectButtonGray(webDetailButtonGray, tenantId);
    }

    private static Set<String> getAllObjectButtonGray(Map<String, List<GrayRuleHelper<Set<String>>>> buttonGray, String tenantId) {
        if (CollectionUtils.empty(buttonGray)) {
            return Sets.newHashSet();
        }
        List<GrayRuleHelper<Set<String>>> ruleHelpers = buttonGray.get(ALL);
        return CollectionUtils.nullToEmpty(ruleHelpers).stream()
                .map(it -> it.getGrayItem(tenantId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
    }

    public static Set<String> listNormalButtonGray(String tenantId, String describeApiName) {
        if (CollectionUtils.empty(listNormalButtonGray)) {
            return Sets.newHashSet();
        }
        List<GrayRuleHelper<Set<String>>> grayRuleHelpers = listNormalButtonGray.get(describeApiName);
        if (CollectionUtils.empty(grayRuleHelpers)) {
            return Sets.newHashSet();
        }
        return grayRuleHelpers.stream()
                .map(it -> it.getGrayItem(tenantId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
    }

    public static Set<String> webDetailButtonGray(String tenantId, String describeApiName) {
        if (CollectionUtils.empty(webDetailButtonGray)) {
            return Sets.newHashSet();
        }
        if (isCustomObject(describeApiName)) {
            describeApiName = UDOBJ;
        }
        Set<String> buttonApiNameSet = getAllObjectWebDetailButtonGray(tenantId);   // 全部对象都可用的按钮
        List<GrayRuleHelper<Set<String>>> grayRuleHelpers = webDetailButtonGray.get(describeApiName);   // 查询对象级可用的按钮
        if (CollectionUtils.empty(grayRuleHelpers)) {
            if (UDOBJ.equals(describeApiName)) {
                return buttonApiNameSet;
            }
            grayRuleHelpers = webDetailButtonGray.get(UDOBJ);   // 预设对象降级为自定义对象的
            if (CollectionUtils.empty(grayRuleHelpers)) {
                return buttonApiNameSet;
            }
        }
        Set<String> buttonApiNames = grayRuleHelpers.stream()
                .map(it -> it.getGrayItem(tenantId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        buttonApiNameSet.addAll(buttonApiNames);
        return buttonApiNameSet;
    }

    public static boolean buttonPostFuncActionAsyncGray(String tenantId, String describeApiName, String buttonApiName) {
        if (CollectionUtils.empty(buttonPostFuncActionAsyncGray)) {
            return false;
        }
        List<GrayRuleHelper<Set<String>>> grayRuleHelpers = buttonPostFuncActionAsyncGray.get(describeApiName);
        if (CollectionUtils.empty(grayRuleHelpers)) {
            return false;
        }
        Set<String> buttonItems = grayRuleHelpers.stream()
                .map(it -> it.getGrayItem(tenantId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        return buttonItems.contains(buttonApiName);
    }

    public static boolean isIgnoreFieldForApproval(String objectApiName, String fieldApiName, String fieldType) {
        if (getIgnoreFieldTypesForApproval().contains(fieldType)) {
            return true;
        }
        Set<String> filterFields = Sets.newHashSet();
        //所有对象通用的字段
        filterFields.addAll(getIgnoreFieldApiNamesForApproval().getOrDefault(UDOBJ, Collections.emptyList()));
        //对象特有的字段
        filterFields.addAll(getIgnoreFieldApiNamesForApproval().getOrDefault(objectApiName, Collections.emptyList()));
        if (filterFields.contains(fieldApiName)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowChildBulkCreate(String tenantId, String apiName) {
        String objectApiName = StringUtils.endsWith(apiName, "__c") ? "Udobj" : apiName;
        if (childBulkCreateGray.containsKey(objectApiName)) {
            return childBulkCreateGray.getOrDefault(objectApiName, Lists.newArrayList()).contains(tenantId);
        }
        return false;
    }

    public static int getMaxDetailObjectCountPreObject(String apiName) {
        if (detailObjectByPreobjSizeLimit.containsKey(apiName)) {
            return detailObjectByPreobjSizeLimit.get(apiName);
        }
        return 5;
    }

    public static boolean isEsCheckDuplicate() {
        return ES_CHECK_DUPLICATE;
    }

    public static boolean isMatchFieldTypeWithAttributeKey(IFieldDescribe fieldDescribe, String attributeKey) {
        if (Objects.isNull(fieldDescribe) || StringUtils.isBlank(attributeKey)) {
            return false;
        }
        List<String> attributeList = batchUpdateFieldTypeSupportAttribute.get(fieldDescribe.getType());
        if (CollectionUtils.empty(attributeList)) {
            return false;
        }
        return attributeList.contains(attributeKey);
    }

    private static void setVipTenantIds(Set<String> vipTenantIds) {
        AppFrameworkConfig.vipTenantIds = vipTenantIds;
    }

    private static void setExportVipTenantIds(Set<String> exportVipTenantIds) {
        AppFrameworkConfig.exportVipTenantIds = exportVipTenantIds;
    }

    private static void setClonePicTenantIds(Set<String> clonePicTenantIds) {
        AppFrameworkConfig.clonePicTenantIds = clonePicTenantIds;
    }

    private static void setNumberFieldTypes(Set<String> numberFieldTypes) {
        AppFrameworkConfig.numberFieldTypes = numberFieldTypes;
    }

    private static void setDateFieldTypes(Set<String> dateFieldTypes) {
        AppFrameworkConfig.dateFieldTypes = dateFieldTypes;
    }

    private static void setFileFieldTypes(Set<String> fileFieldTypes) {
        AppFrameworkConfig.fileFieldTypes = fileFieldTypes;
    }

    private static void setIgnoreFieldTypesForApproval(Set<String> ignoreFieldTypesForApproval) {
        AppFrameworkConfig.ignoreFieldTypesForApproval = ignoreFieldTypesForApproval;
    }

    private static void setIgnoreFieldTypesForDiff(Set<String> ignoreFieldTypesForDiff) {
        AppFrameworkConfig.ignoreFieldTypesForDiff = ignoreFieldTypesForDiff;
    }

    private static void setIgnoreFieldApiNamesForApproval(Map<String, List<String>> ignoreFieldApiNamesForApproval) {
        AppFrameworkConfig.ignoreFieldApiNamesForApproval = ignoreFieldApiNamesForApproval;
    }

    private static void setObjectOrder(List<String> objectOrder) {
        AppFrameworkConfig.objectOrder = objectOrder;
    }

    private static void setDataPrivilegeMap(Map<String, String> dataPrivilegeMap) {
        AppFrameworkConfig.dataPrivilegeMap = dataPrivilegeMap;
    }

    private static void setRelatedNotNeedDataAuth(Set<String> relatedNotNeedDataAuth) {
        AppFrameworkConfig.relatedNotNeedDataAuth = relatedNotNeedDataAuth;
    }

    private static void setUniqueRuleRedisExpireTime(int uniqueRuleRedisExpireTime) {
        AppFrameworkConfig.uniqueRuleRedisExpireTime = uniqueRuleRedisExpireTime;
    }

    private static void setUniqueRuleIncludeNull(boolean uniqueRuleIncludeNull) {
        AppFrameworkConfig.uniqueRuleIncludeNull = uniqueRuleIncludeNull;
    }

    private static void setUniqueRuleValueAllEmptyNotQueryGray(boolean uniqueRuleValueAllEmptyNotQueryGray) {
        AppFrameworkConfig.uniqueRuleValueAllEmptyNotQueryGray = uniqueRuleValueAllEmptyNotQueryGray;
    }

    private static void setOutTeamMemberRole(Map<String, List<String>> outTeamMemberRole) {
        AppFrameworkConfig.outTeamMemberRole = outTeamMemberRole;
    }

    private static void setTeamMemberRole(Map<String, List<String>> teamMemberRole) {
        AppFrameworkConfig.teamMemberRole = teamMemberRole;
    }

    private static void setDefaultButtonOrder(List<String> defaultButtonOrder) {
        AppFrameworkConfig.defaultButtonOrder = defaultButtonOrder;
    }

    private static void setDuplicatedSearchRedisExpireTimes(int duplicatedSearchRedisExpireTimes) {
        AppFrameworkConfig.duplicatedSearchRedisExpireTimes = duplicatedSearchRedisExpireTimes;
    }

    private static void setCurrencyList(List<Map<String, String>> currencyList) {
        AppFrameworkConfig.currencyList = currencyList;
    }

    private static void setCurrencyBlackObjectList(List<String> currencyBlackObjectList) {
        AppFrameworkConfig.currencyBlackObjectList = currencyBlackObjectList;
    }

    private static void setOrganizationBlackObjectList(List<String> organizationBlackObjectList) {
        AppFrameworkConfig.organizationBlackObjectList = organizationBlackObjectList;
    }

    private static void setDuplicateSearchLockWaitTimeSeconds(long duplicateSearchLockWaitTimeSeconds) {
        AppFrameworkConfig.duplicateSearchLockWaitTimeSeconds = duplicateSearchLockWaitTimeSeconds;
    }

    private static void setDuplicateSearchLockWaitLeaseTimeSeconds(long duplicateSearchLockWaitLeaseTimeSeconds) {
        AppFrameworkConfig.duplicateSearchLockWaitLeaseTimeSeconds = duplicateSearchLockWaitLeaseTimeSeconds;
    }

    private static void setDuplicateSearchGlobalLockWaitTimeSeconds(long duplicateSearchGlobalLockWaitTimeSeconds) {
        AppFrameworkConfig.duplicateSearchGlobalLockWaitTimeSeconds = duplicateSearchGlobalLockWaitTimeSeconds;
    }

    private static void setDuplicateSearchGlobalLockWaitLeaseTimeSeconds(long duplicateSearchGlobalLockWaitLeaseTimeSeconds) {
        AppFrameworkConfig.duplicateSearchGlobalLockWaitLeaseTimeSeconds = duplicateSearchGlobalLockWaitLeaseTimeSeconds;
    }

    private static void setBulkDeleteLimit(int bulkDeleteLimit) {
        AppFrameworkConfig.bulkDeleteLimit = bulkDeleteLimit;
    }

    private static void setFieldPromptLimit(int fieldPromptLimit) {
        AppFrameworkConfig.fieldPromptLimit = fieldPromptLimit;
    }

    private static void setBulkDeleteMasterLimit(int bulkDeleteMasterLimit) {
        AppFrameworkConfig.bulkDeleteMasterLimit = bulkDeleteMasterLimit;
    }

    private static void setPictureAnnexDownloadFuncCodeSwitch(boolean pictureAnnexDownloadFuncCodeSwitch) {
        AppFrameworkConfig.pictureAnnexDownloadFuncCodeSwitch = pictureAnnexDownloadFuncCodeSwitch;
    }

    private static void setUiEventCurrencyGrayEi(Set<String> uiEventCurrencyGrayEi) {
        AppFrameworkConfig.uiEventCurrencyGrayEi = uiEventCurrencyGrayEi;
    }

    private static void setApprovalTaskFlowLayoutFieldUseGray(boolean approvalTaskFlowLayoutFieldUseGray) {
        AppFrameworkConfig.approvalTaskFlowLayoutFieldUseGray = approvalTaskFlowLayoutFieldUseGray;
    }

    private static void setUndisplayedPresetObjects(Set<String> undisplayedPresetObjects) {
        AppFrameworkConfig.undisplayedPresetObjects = undisplayedPresetObjects;
    }

    private static void setCommunicationOperator(List<String> communicationOperator) {
        AppFrameworkConfig.communicationOperator = communicationOperator;
    }

    private static void setMunicipalityDirectlyUnderTheCentralGovernmentCode(List<String> municipalityDirectlyUnderTheCentralGovernmentCode) {
        AppFrameworkConfig.municipalityDirectlyUnderTheCentralGovernmentCode = municipalityDirectlyUnderTheCentralGovernmentCode;
    }

    private static void setSyncCalculateDataDataBatchSize(int syncCalculateDataDataBatchSize) {
        AppFrameworkConfig.syncCalculateDataDataBatchSize = syncCalculateDataDataBatchSize;
    }

    private static void setSyncCalculateDataMaxQueryCount(int syncCalculateDataMaxQueryCount) {
        AppFrameworkConfig.syncCalculateDataMaxQueryCount = syncCalculateDataMaxQueryCount;
    }

    private static void setGdprUrl(String gdprUrl) {
        AppFrameworkConfig.gdprUrl = gdprUrl;
    }

    private static void setMultiDuplicateCountLimit(int multiDuplicateCountLimit) {
        AppFrameworkConfig.multiDuplicateCountLimit = multiDuplicateCountLimit;
    }

    private static void setOuterOrganizationMaxPageSize(int outerOrganizationMaxPageSize) {
        AppFrameworkConfig.outerOrganizationMaxPageSize = outerOrganizationMaxPageSize;
    }

    private static void setMaxQueryLimit(int maxQueryLimit) {
        AppFrameworkConfig.maxQueryLimit = maxQueryLimit;
    }

    private static void setMaxChangeOwnerDataIdNum(int maxChangeOwnerDataIdNum) {
        AppFrameworkConfig.maxChangeOwnerDataIdNum = maxChangeOwnerDataIdNum;
    }

    private static void setMaxQueryAreaLimit(int maxQueryAreaLimit) {
        AppFrameworkConfig.maxQueryAreaLimit = maxQueryAreaLimit;
    }

    private static void setMaxQueryLimitWithCalculateCount(int maxQueryLimitWithCalculateCount) {
        AppFrameworkConfig.maxQueryLimitWithCalculateCount = maxQueryLimitWithCalculateCount;
    }

    private static void setMaxQueryLimitWithIds(int maxQueryLimitWithIds) {
        AppFrameworkConfig.maxQueryLimitWithIds = maxQueryLimitWithIds;
    }

    private static void setMaxQueryLimitWithIdsAndCalculateCount(int maxQueryLimitWithIdsAndCalculateCount) {
        AppFrameworkConfig.maxQueryLimitWithIdsAndCalculateCount = maxQueryLimitWithIdsAndCalculateCount;
    }

    public static boolean unSupportExecuteButtonBeforeValidate(String objectApiName) {
        return executeButtonBeforeValidateBlackObj.contains(objectApiName);
    }

    public static boolean isMobileSpecialComponents(String name) {
        if (mobileSpecialComponents.contains("ALL")) {
            return true;
        }
        return mobileSpecialComponents.contains(name);
    }

    public static Integer getEnableMultiLangFieldCountByDescribeApiName(String describeApiName) {
        if (isCustomObject(describeApiName)) {
            describeApiName = CUSTOMER;
        }
        Integer count = enableMultiLangFieldCount.get(describeApiName);
        if (Objects.isNull(count)) {
            count = Objects.nonNull(enableMultiLangFieldCount.get(DEFAULT)) ? enableMultiLangFieldCount.get(DEFAULT) : 10;
        }
        return count;
    }

    public static Set<String> specialMultiLangDescribeApiName() {
        return AppFrameworkConfig.SPECIAL_MULTI_LANG_DESCRIBE_API_NAME;
    }

    public static boolean isUseMultiRegionByFieldType(String type) {
        return useMultiRegionFiledType.contains(type);
    }

    public static boolean isCustomSidebarObject(String describeApiName) {
        return !CollectionUtils.nullToEmpty(sidebarLayoutTemplate).containsKey(describeApiName);
    }

    public static class GrayRuleHelper<T> {
        private final GrayRule rule;
        private final T grayItem;
        @Getter
        private final String bizValue;

        private GrayRuleHelper(GrayRule rule, T grayItem, String bizValue) {
            this.rule = rule;
            this.grayItem = grayItem;
            this.bizValue = bizValue;
        }

        public static <E> GrayRuleHelper<E> of(String rule, E grayItem) {
            GrayRule grayRule = new GrayRule(rule);
            return new GrayRuleHelper<>(grayRule, grayItem, null);
        }

        public static <E> GrayRuleHelper<E> of(String rule, E grayItem, String bizValue) {
            GrayRule grayRule = new GrayRule(rule);
            return new GrayRuleHelper<>(grayRule, grayItem, bizValue);
        }

        public boolean isAllow(String euid) {
            return rule.isAllow(euid);
        }

        public Optional<T> getGrayItem(String euid) {
            return Optional.ofNullable(grayItem)
                    .filter(it -> isAllow(euid));
        }
    }

    public static int listAsyncFillFieldInfoWaitTime() {
        return listAsyncFillFieldInfoWaitTime;
    }

    public static int fillQuoteFieldInfoWaitTime() {
        return fillQuoteFieldInfoWaitTime;
    }

    public static String getRecordTypeFunc(String tenantId, String objectApiName) {
        if (recordTypeFuncGray.containsKey(tenantId)
                && CollectionUtils.notEmpty(recordTypeFuncGray.get(tenantId))) {
            Map<String, String> objectFuncMap = recordTypeFuncGray.get(tenantId);
            String func = objectFuncMap.get(objectApiName);
            if (!Strings.isNullOrEmpty(func)) {
                return func;
            }
        }
        return null;
    }

    public static boolean updateImportSupportSpecifiedField(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            return true;
        }
        GrayRule grayRule = importSupportSpecifiedFieldGray.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean uiEventSupportModifyMasterDetailFieldGray(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        GrayRule grayRule = uiEventSupportModifyMasterDetailGray.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean designerLayoutButton(String tenantId, String objectApiName) {
        GrayRule defaultGrayRule = designerLayoutButton.get(ALL);
        if (isCustomObject(objectApiName)) {
            objectApiName = DefObjConstants.UDOBJ;
        }
        GrayRule grayRule = designerLayoutButton.getOrDefault(objectApiName, defaultGrayRule);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isLogTypeObject(String ei, String objectApiName) {
        return logTypeObject.getOrDefault(ei, Lists.newArrayList()).contains(objectApiName);
    }

    public static boolean manyAbstractLayout(String objApiName, String tenantId) {
        return graySupported(grayManyAbstractLayoutFilterConfig, false, objApiName, tenantId);
    }

    public static List<Map<String, Object>> manyAbstractLayout(List<String> objApiNames, String tenantId) {
        return graySupported(grayManyAbstractLayoutFilterConfig, false, objApiNames, tenantId);
    }

    public static Table<String, String, GrayRule> parseGrayRuleTable(Map<String, Map<String, String>> map) {
        Table<String, String, GrayRule> result = HashBasedTable.create();
        if (CollectionUtils.empty(map)) {
            return result;
        }
        map.forEach((rowKey, rowValue) ->
                rowValue.forEach((columnKey, rule) ->
                        result.put(rowKey, columnKey, new GrayRule(rule))
                )
        );
        return result;
    }


    private static boolean graySupported(Map<String, GrayRule> ruleMap, boolean defaultMode, String objApiName, String tenantId) {
        //1、某个预制对象或者所有自定义对象使用
        String objApiNameIdx = isCustomObject(objApiName) ? DefObjConstants.UDOBJ : objApiName;
        GrayRule grayRule = ruleMap.get(objApiNameIdx);
        if (Objects.nonNull(grayRule)) {
            return grayRule.isAllow(tenantId);
        }
        //2、全部对象使用
        GrayRule defaultRule = ruleMap.get(ALLOBJ);
        if (Objects.nonNull(defaultRule)) {
            return defaultRule.isAllow(tenantId);
        }
        //3、默认
        return defaultMode;
    }

    private static List<Map<String, Object>> graySupported(Map<String, GrayRule> ruleMap, boolean defaultMode, List<String> objApiNames, String tenantId) {
        List<Map<String, Object>> grayForObjApiName = new ArrayList<>(objApiNames.size());
        objApiNames.forEach(objApiName -> {
            Map<String, Object> rule = Maps.newHashMap();
            rule.put("describeApiName", objApiName);
            rule.put("gray", graySupported(ruleMap, defaultMode, objApiName, tenantId));
            grayForObjApiName.add(rule);
        });
        return grayForObjApiName;
    }

    public static boolean relatedListFormSupportObject(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = DefObjConstants.UDOBJ;
        }
        GrayRule grayRule = relatedListFormSupportObject.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean relatedListFormSupportObjectWithField(String tenantId, String objectApiName, Collection<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return false;
        }
        if (isCustomObject(objectApiName)) {
            return true;
        }
        if (!relatedListFormSupportObject(tenantId, objectApiName)) {
            return false;
        }
        for (String fieldName : fieldNames) {
            if (isCustomObject(fieldName)) {
                return true;
            }
        }

        List<String> fieldNameList = CollectionUtils.nullToEmpty(relatedListFormSupportObjectField.get(objectApiName));
        return org.apache.commons.collections4.CollectionUtils.containsAny(fieldNameList, fieldNames);
    }

    public static Set<String> relatedListFormSupportObjectFields(String tenantId, String objectApiName, Collection<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return Collections.emptySet();
        }
        if (isCustomObject(objectApiName)) {
            return Sets.newHashSet(fieldNames);
        }
        if (!relatedListFormSupportObject(tenantId, objectApiName)) {
            return Collections.emptySet();
        }

        Set<String> result = Sets.newHashSetWithExpectedSize(fieldNames.size());
        Set<String> confFieldNames = Sets.newHashSet(CollectionUtils.nullToEmpty(relatedListFormSupportObjectField.get(objectApiName)));
        for (String fieldName : fieldNames) {
            if (isCustomObject(fieldName) || confFieldNames.contains(fieldName)) {
                result.add(fieldName);
            }
        }
        return result;
    }

    public static boolean unSupportButtonInternalObject(String describeApiName) {
        return unSupportButtonInternalObject.contains(describeApiName);
    }

    public static boolean supportTreeView(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = DefObjConstants.UDOBJ;
        }
        GrayRule grayRule = treeViewSupportObject.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean treeViewObjectNotSupportImport() {
        return treeViewObjectNotSupportImport;
    }

    public static int getMaxFlowLayoutCount(String tenantId) {
        return maxFlowLayoutCountMap.getOrDefault(tenantId, 100);
    }

    public static long getMaxCountObjectData() {
        return maxCountObjectDataGray;
    }

    public static long getMaxCountHistoryObjectData() {
        return maxHistoryDataCountGray;
    }

    public static boolean needDiffFieldName(String objectApiName, String fieldName) {
        Set<String> fieldNames = Sets.newHashSet(needDiffSystemFieldName.getOrDefault(UDOBJ, Collections.emptyList()));
        if (!isCustomObject(objectApiName)) {
            List<String> objectFieldNames = needDiffSystemFieldName.getOrDefault(objectApiName, Collections.emptyList());
            fieldNames.addAll(objectFieldNames);
        }
        return fieldNames.contains(fieldName);
    }

    public static boolean unSupportFieldForModifyLog(String objectApiName, String fieldName) {
        Set<String> fieldNames = Sets.newHashSet(unSupportFieldForModifyLog.getOrDefault(UDOBJ, Collections.emptyList()));
        if (!isCustomObject(objectApiName)) {
            List<String> objectFieldNames = unSupportFieldForModifyLog.getOrDefault(objectApiName, Collections.emptyList());
            fieldNames.addAll(objectFieldNames);
        }
        return fieldNames.contains(fieldName);
    }

    public static boolean isInMasterDetailEditPageDetailSingleButtonGray(String tenantId, String objectApiName) {
        String grayKey = isCustomObject(objectApiName) ? UDOBJ : objectApiName;
        GrayRule grayRule = masterDetailEditPageDetailSingleButtonGray.get(grayKey);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isInMasterDetailEditPageDetailSingleButtonInsertGray(String tenantId, String objectApiName) {
        String grayKey = isCustomObject(objectApiName) ? UDOBJ : objectApiName;
        GrayRule grayRule = masterDetailEditPageDetailSingleButtonInsertGray.get(grayKey);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isEnableCheckEnterpriseResourcesQuote(String tenantId) {
        return enableCheckEnterpriseResourcesQuote.contains(ALL) || enableCheckEnterpriseResourcesQuote.contains(tenantId);
    }

    public static int getOptionSetMaxCountGray(String tenantId, Integer defaultMaxCount) {
        return optionSetMaxCountGray.getOrDefault(tenantId, defaultMaxCount);
    }

    public static boolean isDateTimeSupportNotUseMultitimeZoneTenant(String tenantId) {
        return DateTimeUtils.isGrayTimeZone() && (dateTimeSupportNotUseMultitimeZoneTenant.contains(ALL) || dateTimeSupportNotUseMultitimeZoneTenant.contains(tenantId));
    }

    public static boolean isValidationFunctionMergeDetailDataGray(String tenantId, String objectApiName) {
        String grayKey = isCustomObject(objectApiName) ? UDOBJ : objectApiName;
        GrayRule grayRule = validationFunctionMergeDetailDataGray.get(grayKey);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static Set<String> getValidationFunctionMergeDetailIgnoreFieldsGray() {
        return Sets.newHashSet(validationFunctionMergeDetailIgnoreFieldsGray);
    }

    public static List<Lang> multiLangSupport(List<Lang> langs, String describeApiName, String fieldName) {
        Map<String, List<String>> fieldMap;
        fieldMap = multiLangSupport.get(describeApiName);
        if (CollectionUtils.empty(fieldMap) && isCustomObject(describeApiName)) {
            fieldMap = multiLangSupport.get(CUSTOMER);
        }
        if (CollectionUtils.empty(fieldMap)) {
            return langs;
        }
        List<String> values = fieldMap.get(fieldName);
        if (CollectionUtils.empty(values) && fieldMap.containsKey("*")) {
            values = fieldMap.get("*");
        }
        if (CollectionUtils.empty(values)) {
            return langs;
        }
        List<String> finalValues = values;
        return langs.stream().filter(lang -> finalValues.contains(lang.getValue())).collect(Collectors.toList());
    }

    public static boolean objectMultiLangGray(String tenantId, String objectApiName) {
        GrayRule grayRule = objectMultiLangGray.get(objectApiName);
        if (Objects.isNull(grayRule) && objectMultiLangGray.containsKey(CUSTOMER) && isCustomObject(objectApiName)) {
            grayRule = objectMultiLangGray.get(CUSTOMER);
        }
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isCreateAndEditLayoutSupportTransComponentType(String type) {
        return createAndEditLayoutSupportTransComponentType.contains(type);
    }

    public static boolean notCopyDescribeInInnerMethod(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_IN_INNER_METHOD_EI, tenantId);
    }

    public static boolean notCopyDescribeInController(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_IN_CONTROLLER_EI, tenantId)
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_IN_CONTROLLER_OBJ, objectApiName);
    }

    public static boolean notCopyDescribeInAction(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_IN_ACTION_EI, tenantId)
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_IN_ACTION_OBJ, objectApiName);
    }

    public static boolean isCloseRelatedTeamSwitchObject(String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return closeRelatedTeamSwitchObject.contains(objectApiName);
    }

    public static boolean isCloseGlobalSearchSwitchObject(String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return closeGlobalSearchSwitchObject.contains(objectApiName);
    }

    public static boolean isCloseFollowUpDynamicSwitchObject(String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return closeFollowUpDynamicSwitchObject.contains(objectApiName);
    }

    public static boolean isCloseModifyRecordSwitchObject(String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return closeModifyRecordSwitchObject.contains(objectApiName);
    }

    public static boolean isListNormalUiActionSupportQueryParam(String tenantId, String describeApiName, String buttonApiName) {
        if (CollectionUtils.empty(listNormalUiActionSupportQueryParam)) {
            return false;
        }
        List<GrayRuleHelper<Set<String>>> grayRuleHelpers = listNormalUiActionSupportQueryParam.get(describeApiName);
        if (CollectionUtils.empty(grayRuleHelpers)) {
            return false;
        }
        return grayRuleHelpers.stream()
                .map(it -> it.getGrayItem(tenantId))
                .flatMap(it -> it.orElse(Collections.emptySet()).stream())
                .anyMatch(it -> Objects.equals(it, buttonApiName));
    }

    public static boolean isOptionalFeaturesSupport(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPTIONAL_FEATURES_SWITCH_EI, tenantId) || TenantUtil.isOptionalFeatures(tenantId);
    }

    public static boolean isSupportMobileComponentByTransWorkBench(String componentTyp) {
        return transWorkBenchSupportMobileComponents.contains(componentTyp);
    }

    public static boolean isWebToMobileLayoutFilterComponent(String componentApiName) {
        return webToMobileLayoutFilterComponents.contains(componentApiName);
    }

    public static boolean isWebToMobileLayoutFilterComponentType(String componentType) {
        return webToMobileLayoutFilterComponentTypes.contains(componentType);
    }

    public static boolean isOuterUserUnsupportedComponentType(String tenantId, String componentType) {
        // 只检查灰度配置
        GrayRule grayRule = outerUserUnsupportedComponentTypesGrayRules.get(componentType);
        if (Objects.isNull(grayRule)) {
            return false;
        }

        return grayRule.isAllow(tenantId);
    }

    public static int getMasterRelatedObjectComponentLimit(String tenantId) {
        return masterRelatedObjectComponentLimit.getOrDefault(tenantId, 5);
    }

    public static boolean grayHTMLRichTextLimit(String tenantId) {
        return htmlRichTextLimit.containsKey(tenantId);
    }

    public static int getRichTextLimit(String tenantId) {
        return richTextLimit.getOrDefault(tenantId, 1);
    }

    public static int getBigTextLimit(String tenantId) {
        return bigTextLimit.getOrDefault(tenantId, 3);
    }

    public static int getHTMLRichTextLimit(String tenantId) {
        return htmlRichTextLimit.getOrDefault(tenantId, 1);
    }

    public static int getRichTextFieldMaxCount(String describeApiName, Integer defaultMaxCount) {
        return objectRichTextFieldMaxCount.getOrDefault(describeApiName, defaultMaxCount);
    }

    /**
     * 获取HTML富文本类型字段的最大限制数量，按照对象和租户级别取最大值
     *
     * @param describeApiName 对象API名称
     * @param tenantId        租户ID
     * @return 最大限制数量
     */
    public static int getHtmlRichTextFieldMaxLimitByObject(String describeApiName, String tenantId) {
        int tenantMaxCount = getHTMLRichTextLimit(tenantId);
        if (StringUtils.isEmpty(describeApiName)) {
            return tenantMaxCount;
        }
        int objectMaxCount = getHtmlRichTextFieldMaxCount(describeApiName, tenantMaxCount);
        return Math.max(tenantMaxCount, objectMaxCount);
    }

    /**
     * 获取协同富文本类型字段的最大限制数量，按照对象和租户级别取最大值
     *
     * @param describeApiName 对象API名称
     * @param tenantId        租户ID
     * @return 最大限制数量
     */
    public static int getRichTextFieldMaxLimitByObject(String describeApiName, String tenantId) {
        int tenantMaxCount = getRichTextLimit(tenantId);
        if (StringUtils.isEmpty(describeApiName)) {
            return tenantMaxCount;
        }
        int objectMaxCount = getRichTextFieldMaxCount(describeApiName, tenantMaxCount);
        return Math.max(tenantMaxCount, objectMaxCount);
    }

    public static int getHtmlRichTextFieldMaxCount(String describeApiName, Integer defaultMaxCount) {
        return objectHtmlRichTextFieldMaxCount.getOrDefault(describeApiName, defaultMaxCount);
    }

    public static int getBigTextFieldMaxCount(String describeApiName, Integer defaultMaxCount) {
        return objectBigTextFieldMaxCount.getOrDefault(describeApiName, defaultMaxCount);
    }

    public static int getAddTeamMemberLimit(String tenantId) {
        return addTeamMemberLimit.getOrDefault(tenantId, 500);
    }

    public static int getLookupFieldIncreaseNum(String describeApiName, Integer defaultMaxCount) {
        return objectLookupFieldIncreaseNum.getOrDefault(describeApiName, defaultMaxCount);
    }

    public static boolean cooperativeRichTextGray(String tenantId, String objectApiName) {
        if (richTextObjectBlackList.contains(objectApiName)) {
            return false;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.RICH_TEXT_GRAY_EI, tenantId);
    }

    public static boolean maskFieldEncryptGray(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        GrayRule grayRule = maskFieldEncryptGray.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static Set<String> maskFieldEncryptObjectFieldsGray(String objectApiName, Collection<String> fieldNames) {
        GrayRule grayRule = maskFieldEncryptObjectFieldsGray.get(objectApiName);
        if (Objects.isNull(grayRule) || CollectionUtils.empty(fieldNames)) {
            return Collections.emptySet();
        }
        return fieldNames.stream()
                .filter(grayRule::isAllow)
                .collect(Collectors.toSet());
    }

    /**
     * 灰度的企业对象、只在列表页对掩码字段加密
     */
    public static boolean maskFieldEncryptObjectPagesGray(String tenantId, String objectApiName) {
        GrayRule grayRule = maskFieldEncryptObjectPagesGray.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean isFilterButtonsProviderGray(String tenantId, String describeApiName) {
        if (isCustomObject(describeApiName)) {
            describeApiName = UDOBJ;
        }
        GrayRule grayRule = filterButtonsProviderGray.get(describeApiName);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isGrayMultiDuplicateRuleAndSupportFilterGray(String tenantId, String objectApiName) {
        return isGrayMultiDuplicateRuleEi(tenantId) && !isGrayMultiDuplicateRuleAndSupportFilterBlackObj(objectApiName, tenantId);
    }

    public static boolean isGrayMultiDuplicateRuleEi(String tenantId) {
        return multiDuplicateRuleAndSupportFilterGrayEi.contains("ALL") || multiDuplicateRuleAndSupportFilterGrayEi.contains(tenantId);
    }

    public static Set<String> multiDuplicateRuleBlackApiNameList() {
        return multiDuplicateRuleAndSupportFilterBlackGrayObject;
    }

    public static boolean isGrayMultiDuplicateRuleAndSupportFilterBlackObj(String objectApiName, String tenantId) {
        // 如果企业在该灰度中,则代表三大对象不是黑名单对象
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MULTI_DUPLICATE_RULE_SUPPORT_PRE_OBJ_GRAY, tenantId)) {
            return false;
        }
        return multiDuplicateRuleAndSupportFilterBlackGrayObject.contains(objectApiName);
    }

    public static boolean isGrayManageGroup(String tenantId) {
        return manageGroupGrayEi.contains(tenantId) || manageGroupGrayEi.contains(ALL);
    }

    public static boolean isGrayPrintTemplateUseUserTimeZoneEi(String tenantId) {
        return printTemplateDateTimeUseUserTimeZoneEi.contains(ALL) || printTemplateDateTimeUseUserTimeZoneEi.contains(tenantId);
    }

    public static Set<String> getSocialGrayObj() {
        return CollectionUtils.nullToEmpty(socialObjGray);
    }

    public static boolean lookupTreeViewObjectNotDisplayWholePath(String tenantId, String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        GrayRule grayRule = lookupTreeViewObjectNotDisplayWholePathGrayEi.get(objectApiName);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean isSimpleDescribeFieldAttrWhite(String fieldName) {
        return simpleDescribeFieldAttrWhiteList.contains(fieldName);
    }

    public static boolean isSimpleDescribeGray(String tenantId, String describeApiName) {
        GrayRule grayRule = getGrayRule(describeApiName, simpleDescribeGrayEi);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    private static GrayRule getGrayRule(String describeApiName, Map<String, GrayRule> grayRuleMap) {
        GrayRule grayRule = grayRuleMap.get(describeApiName);
        if (Objects.nonNull(grayRule)) {
            return grayRule;
        }
        if (isCustomObject(describeApiName)) {
            grayRule = simpleDescribeGrayEi.get(UDOBJ);
        }
        if (Objects.isNull(grayRule)) {
            return simpleDescribeGrayEi.get(ALLOBJ);
        }
        return grayRule;
    }

    public static boolean getIsSupportSystemTag(String tenantId) {
        return isSupportSystemTag.contains(tenantId);
    }

    public static Set<String> getDuplicateSearchSupportUpperAndIgnoreField(String tenantId, String objectApiName) {
        if (StringUtils.isAnyEmpty(tenantId, objectApiName)) {
            return Sets.newHashSet();
        }
        Map<String, Set<String>> objToFieldMap = duplicateSearchSupportIgnoreUpperAndLower.get(tenantId);
        if (CollectionUtils.empty(objToFieldMap)) {
            return Sets.newHashSet();
        }
        if (Objects.isNull(objToFieldMap.get(objectApiName))) {
            return Sets.newHashSet();
        }
        return CollectionUtils.nullToEmpty(Sets.newHashSet(objToFieldMap.get(objectApiName)));
    }

    public static boolean isGrayParamsIdempotent(String tenantId, String objectApiName) {
        if (CollectionUtils.empty(paramsIdempotentGrayRules) || !paramsIdempotentGrayRules.containsKey(objectApiName)) {
            return false;
        }
        return paramsIdempotentGrayRules.get(objectApiName).isAllow(tenantId);
    }

    /**
     * 下发布局是否包含审批流程和流程列表组件
     */
    public static boolean isGrayIncludeFlowComponent(String objectApiName) {
        return !layoutUnincludeFlowComponent.contains(objectApiName);
    }

    public static boolean isFilterByTransMultiLanguageComponent(String componentType, String tenantId) {
        //在这个名单中，则意味着能漏出来
        GrayRule grayRule = transMultiLanguageFilterComponentV2.get(componentType);
        if (Objects.nonNull(grayRule) && StringUtils.isNotEmpty(tenantId)) {
            return !grayRule.isAllow(tenantId);
        }
        //这是一个黑名单
        return transMultiLanguageFilterComponent.contains(componentType);
    }

    public static boolean isButtonAsyncCallBack(String tenantId, String describeApiName) {
        GrayRule grayRule = getGrayRule(describeApiName, buttonAsyncCallBack);
        if (Objects.isNull(grayRule)) {
            return false;
        }
        return grayRule.isAllow(tenantId);
    }

    public static boolean isGrayCalculateCountWhenFindData(String tenantId, String objectApiName) {
        GrayRule grayRule = calculateCountWhenFindDataGrayRules.get(objectApiName);
        if (Objects.isNull(grayRule) && isCustomObject(objectApiName)) {
            grayRule = calculateCountWhenFindDataGrayRules.get(UDOBJ);
        }
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isGrayUserControlBpmAndApprovalListObj(String objApiName) {
        return userControlBpmListAndApprovalListComponentsObj.contains(objApiName);
    }

    public static boolean grayFieldEnableClone(String tenantId) {
        return fieldEnableCloneEiGrayRule.isAllow(tenantId);
    }

    public static Set<String> getFieldEnableCloneFieldTypes(String tenantId) {
        return grayFieldEnableClone(tenantId) ? fieldEnableCloneFieldTypeGray : Sets.newHashSet();
    }

    public static String getBrushToolsRouterTenantId(String env) {
        String tenantId = initToolsSysdbRouteTenantIdMap.get(env);
        if (StringUtils.isBlank(tenantId)) {
            return initToolsSysdbRouteTenantIdMap.get("1");
        }
        return tenantId;
    }

    public static Map<String, String> getRouteTenantId2EnvLabelMap() {
        return brushRouterInfoList.stream()
                .collect(Collectors.toMap(RouterInfo::getRouterTenantId, RouterInfo::getEnvLabel, (k1, k2) -> k1));
    }

    public static boolean isNotCheckCountDetailObject(String objectApiName) {
        if (Strings.isNullOrEmpty(objectApiName) || CollectionUtils.empty(notCheckCountDetailObjects)) {
            return false;
        }
        return notCheckCountDetailObjects.contains(objectApiName);
    }

    public static boolean isOldLayoutConvertV3AddComponents(String componentApiName) {
        return oldLayoutConvertV3AddComponents.contains(componentApiName);
    }

    public static boolean isConvertRuleSupportObject(String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return convertRuleSupportObjects.contains(objectApiName);
    }

    public static boolean isConvertRuleBlackObject(String objectApiName) {
        if (isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return convertRuleBlackObjects.contains(objectApiName);
    }

    public static boolean isGrayCalculateFormulaForFunctionCreate(String tenantId) {
        if (RequestUtil.isActionRequest()) {
            return false;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_FOR_CREATE, tenantId)) {
            return true;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_FORMULA_FOR_FUNCTION_CREATE, tenantId) && RequestUtil.isFromFunction();
    }

    public static boolean isGrayCalculateDefaultValueForInsertImport(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_DEFAULT_VALUE_FOR_INSERT_IMPORT, tenantId);
    }

    public static String getApiNameByName(String name) {
        return preObjNameToApiName.get(name);
    }

    public static boolean isPostMessage(String describeApiName) {
        return duplicatedSearchPostMessageObj.contains(describeApiName);
    }

    public static boolean checkDetailObjectPrivilegeInMapping(String ruleApiName) {
        return Strings.isNullOrEmpty(ruleApiName)
                || CollectionUtils.empty(notCheckDetailObjectPrivilegeMappingRules)
                || !notCheckDetailObjectPrivilegeMappingRules.contains(ruleApiName);
    }

    public static boolean skipRecordRefByTargetObjGray(String objApi) {
        return Optional.ofNullable(SKIP_RECORD_REF_BY_TARGET_OBJ_GRAY)
                .map(set -> set.contains(objApi))
                .orElse(false);
    }

    public static boolean unSupportFieldTypesIfOutUserGrayRules(String tenantId, String describeApiName) {
        GrayRule grayRule = unSupportFieldTypesIfOutUserGrayRules.get(describeApiName);
        if (Objects.isNull(grayRule) && isCustomObject(describeApiName)) {
            grayRule = unSupportFieldTypesIfOutUserGrayRules.get(UDOBJ);
        }
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static boolean isSupportListQueryByLangObj(String describeApiName) {
        return !listQueryByCurrentLangBlackObject.contains(describeApiName);
    }

    public static boolean afterUiEventDoCalculateDetailsLastTime(Long time) {
        if (Objects.isNull(time) || Objects.isNull(UI_EVENT_DO_CALCULATE_DETAILS_LAST_TIME_GRAY)) {
            return false;
        }
        return time > UI_EVENT_DO_CALCULATE_DETAILS_LAST_TIME_GRAY;
    }

    public static Map<String, Object> getAiConfig() {
        return AI_CONFIG_INFO;
    }

    public static Map<String, Object> getPluginConfig() {
        return PACKAGE_PLUGIN_CONFIG_INFO;
    }

    public static int getOnlineDocPluginNameLengthMax() {
        return (int) ONLINE_DOC_CONFIG_INFO.getOrDefault("nameLengthMax", 32);
    }

    public static int getOnlineDocPluginApiNameLengthMax() {
        return (int) ONLINE_DOC_CONFIG_INFO.getOrDefault("pluginApiNameLengthMax", 32);
    }

    public static int getOnlineDocFuncApiNameLengthMax() {
        return (int) ONLINE_DOC_CONFIG_INFO.getOrDefault("functionApiNameLengthMax", 32);
    }

    public static int getOnlineDocPwcApiNameLengthMax() {
        return (int) ONLINE_DOC_CONFIG_INFO.getOrDefault("pwcApiNameLengthMax", 32);
    }

    public static String getOnlineDocCallbackUrl(String tenantId) {
        List<String> tenantIds = (List<String>) ONLINE_DOC_CONFIG_INFO.getOrDefault("supportTenantIds", Lists.newArrayList());
        if (tenantIds.contains(tenantId)) {
            return (String) ONLINE_DOC_CONFIG_INFO.get("grayCallbackUrl");
        } else {
            return (String) ONLINE_DOC_CONFIG_INFO.get("callbackUrl");
        }
    }

    public static boolean ignoreFilterValidate(String fieldApiName) {
        return ignoreFilterValidateFields.contains(fieldApiName);
    }

    public static void processFieldTypes(String tenantId, String describeApiName, List<String> fieldTypes) {
        if (CollectionUtils.empty(fieldGrayConfig)) {
            return;
        }
        for (Map.Entry<String, GrayRuleHelper<Set<String>>> entry : fieldGrayConfig.entrySet()) {
            String fieldApiName = entry.getKey();
            GrayRuleHelper<Set<String>> grayRuleHelper = entry.getValue();
            Optional<Set<String>> grayItem = grayRuleHelper.getGrayItem(tenantId);
            if (fieldTypes.contains(fieldApiName) || !grayItem.isPresent()) {
                continue;
            }
            if (CollectionUtils.nullToEmpty(grayItem.get()).contains(describeApiName)) {
                String bizValue = grayRuleHelper.getBizValue();
                if (!Strings.isNullOrEmpty(bizValue)) {
                    int index = fieldTypes.indexOf(bizValue);
                    index = index >= 0 ? index : fieldTypes.size() - 1;
                    fieldTypes.add(index + 1, fieldApiName);
                } else {
                    fieldTypes.add(fieldApiName);
                }
            }
        }
    }

    /**
     * 黑名单
     * {"UDObj":"name|owner"}
     *
     * @param config    配置文件
     * @param configKey 配置项Key
     * @return 黑名单规则
     */
    public static Map<String, GrayRule> getBlack(IConfig config, String configKey) {
        Map<String, GrayRule> result = Maps.newHashMap();
        Map<String, String> stringString = parseMapFromConfig(config, configKey);
        if (CollectionUtils.empty(stringString)) {
            return Collections.emptyMap();
        }
        stringString.forEach((key, val) -> {
            GrayRule rule = new GrayRule("black:" + val);
            CONFIG_SPLITTER.split(key).forEach(it -> result.putIfAbsent(it, rule));
        });
        return result;
    }

    /**
     * 黑名单表示新字段新对象默认支持"是否可复制"配置
     * <br>对象下字段是否支持"是否可复制"配置，即是否显示"是否可复制"配置
     * <br>（不配置）默认显示"是否可复制"配置
     * UDObj 项为通用配置，适用所有对象
     *
     * @param objApi   对象
     * @param fieldApi 字段
     * @return 是否支持
     */
    public static boolean isNeedEnableClone(String objApi, String fieldApi) {

        // 通用字段规则
        GrayRule allObjGrayRule = fieldEnableCloneGray.get(DefObjConstants.UDOBJ);
        if (allObjGrayRule != null && !allObjGrayRule.isAllow(fieldApi)) {
            // 通用字段指定设置为不显示
            return false;
        }

        if (isCustomObject(objApi)) {
            // 自定义对象，默认显示
            return true;
        }

        // 预设对象支持配置指定对象配置
        GrayRule specialGrayRule = fieldEnableCloneGray.get(objApi);

        if (specialGrayRule == null) {
            // 预设对象（不配置）默认为显示
            return true;
        }

        return specialGrayRule.isAllow(fieldApi);
    }

    public static Optional<ILayout> getSidebarLayoutTemplate(String describeApiName) {
        return Optional.ofNullable(sidebarLayoutTemplate.get(describeApiName))
                .map(it -> new Layout(CopyOnWriteMap.copy(it)));
    }

    public static boolean isDuplicatedSearchRemoveSelf() {
        return DUPLICATED_SEARCH_REMOVE_SELF;
    }

    public static boolean stopButtonClickModifyRecord(String tenantId) {
        return buttonClickModifyRecordGrayEis.contains(ALL) || buttonClickModifyRecordGrayEis.contains(tenantId);
    }

    public static boolean stopButtonClickModifyRecord() {
        String tenantId = Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getTenantId)
                .orElse(null);
        return StringUtils.isBlank(tenantId) || stopButtonClickModifyRecord(tenantId);
    }

    public static boolean validateIsIndexFields(String objectApiName, String fieldApiName) {
        if (CollectionUtils.empty(validateIsIndexFields)) {
            return false;
        }
        return validateIsIndexFields.getOrDefault(objectApiName, Collections.emptyList()).contains(fieldApiName);
    }

    public static Set<String> signedUrlFieldTypes(String tenantId) {
        // 图片字段已经全网，默认所有企业都支持
        Set<String> fieldTypes = Sets.newHashSet(IFieldType.IMAGE);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_ATTACHMENT_SIGNED_URL_GRAY, tenantId)) {
            fieldTypes.add(IFieldType.FILE_ATTACHMENT);
        }
        return fieldTypes;
    }

    public static int getAsyncTaskMaxDayQueryLimit(String tenantId) {
        return asyncTaskMaxDayQueryLimit.getOrDefault(tenantId, 30);
    }

    public static String getLoadConfigFailNotifySessionId() {
        return LOAD_CONFIG_FAIL_NOTIFY_SESSION_ID;
    }

    public static boolean isGrayDuplicatedSupportDistance(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_SUPPORT_DISTANCE, tenantId)
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, tenantId);
    }

    public static boolean shouldOptionRelationCleanup(String objectApiName, String fieldApiName) {
        if (CollectionUtils.empty(ignoreCleanOptionRelation)) {
            return true;
        }
        return !ignoreCleanOptionRelation.getOrDefault(objectApiName, Collections.emptyList()).contains(fieldApiName);
    }

    public static boolean supportEditCurrency(String tenantId, String objectApiName) {
        if (CollectionUtils.notEmpty(notSupportEditCurrencyObjects) && notSupportEditCurrencyObjects.contains(objectApiName)) {
            return false;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_EDIT_CURRENCY_EI, tenantId);
    }
}